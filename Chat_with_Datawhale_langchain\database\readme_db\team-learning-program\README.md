﻿## 简介

本项目主要存储Datawhale组队学习中“编程、数据结构与算法”方向的资料。

主要包括：

- [AlgorithmRunning](https://github.com/datawhalechina/team-learning-program/tree/master/AlgorithmRunning)

- [Linux教程](https://github.com/datawhalechina/team-learning-program/tree/master/Linux)
- [Docker教程](https://github.com/datawhalechina/team-learning-program/tree/master/Docker)
- [Web开发入门教程](https://github.com/datawhalechina/whale-web)
- [Go编程语言](https://github.com/datawhalechina/go-talent)
- [C#编程语言](https://github.com/datawhalechina/team-learning-program/tree/master/CSharpLanguage)
- [SQL编程语言](https://github.com/datawhalechina/team-learning-sql)
- [Python编程语言](https://github.com/datawhalechina/team-learning-program/tree/master/PythonLanguage)
- [数据结构与算法](https://github.com/datawhalechina/team-learning-program/tree/master/DataStructureAndAlgorithm)
- [编程实践（Numpy）](https://github.com/datawhalechina/team-learning-program/tree/master/IntroductionToNumpy)
- [编程实践（Pandas）](https://github.com/datawhalechina/joyful-pandas)
- [编程实践（LeetCode 分类练习）](https://github.com/datawhalechina/team-learning-program/tree/master/LeetCodeClassification)
- [编程实践（LeetCode 腾讯精选练习50）](https://github.com/datawhalechina/team-learning-program/tree/master/LeetCodeTencent)
- [编程实践（蓝桥杯刷题）](https://github.com/datawhalechina/team-learning-program/tree/master/LanQiao)
- [编程实践（Python 爬虫）](https://github.com/datawhalechina/team-learning-program/tree/master/WebSpider)
- [编程实践（Python 综合）](https://github.com/datawhalechina/team-learning-program/tree/master/ProjectPractice)
- [编程实践（Python办公自动化）](https://github.com/datawhalechina/team-learning-program/tree/master/OfficeAutomation)
- [编程实践（区块链）](https://github.com/datawhalechina/team-learning-program/tree/master/Blockchain)
- [编程实践（设计模式）](https://github.com/datawhalechina/team-learning-program/tree/master/DesignPattern)
- [编程实践（数据可视化）](https://github.com/datawhalechina/fantastic-matplotlib)
- [编程实践（Django网站开发）](https://github.com/datawhalechina/team-learning-program/tree/master/Django)
- [青少年编程（Scratch）](https://github.com/datawhalechina/team-learning-program/tree/master/Scratch)
- [青少年编程（Turtle）](https://github.com/datawhalechina/team-learning-program/tree/master/Turtle)



## 备注

有关组队学习的开源内容

- [team-learning](https://github.com/datawhalechina/team-learning)：主要展示Datawhale的组队学习计划。
- [team-learning-program](https://github.com/datawhalechina/team-learning-program)：主要存储Datawhale组队学习中“编程、数据结构与算法”方向的资料。
- [team-learning-data-mining](https://github.com/datawhalechina/team-learning-data-mining)：主要存储Datawhale组队学习中“数据挖掘/机器学习”方向的资料。
- [team-learning-nlp](https://github.com/datawhalechina/team-learning-nlp)：主要存储Datawhale组队学习中“自然语言处理”方向的资料。
- [team-learning-cv](https://github.com/datawhalechina/team-learning-cv)：主要存储Datawhale组队学习中“计算机视觉”方向的资料。
- [team-learning-rs](https://github.com/datawhalechina/team-learning-rs)：主要存储Datawhale组队学习中“推荐系统”方向的资料。
- [team-learning-rl](https://github.com/datawhalechina/team-learning-rl)：主要存储Datawhale组队学习中“强化学习”方向的资料。