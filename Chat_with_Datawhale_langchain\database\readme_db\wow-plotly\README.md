## Wow-Plotly

---------

![](https://tva1.sinaimg.cn/large/008i3skNgy1gs40j25kzfj30ts0d0q3g.jpg)


作为一名和数据经常打交道的人，数据可视化在我们生活中随处可见，它也是每一位数据人都无法避免的问题。有句话讲的好：

> 一图胜千言

在我们表达、解释数据含义的时候，能够用表格就不要用文字描述，能够用图形就不用表格。可见，图形，尤其是可视化图形才是展现数据的最佳利器。

## Plotly介绍

现在有很多工具能够实现数据的可视化，比如：excel、PowerBI、Tableau等软件工具，也有Python的很多的第三方库，比如：matplotlib、seaborn、pyecharts，还有类似D3、Highcharts、Bokeh等基于JavaScript前端开发的可视化库。它们都有着自己的一些缺点或者劣势：

- 绘制出来的图形是静态的，无法实现动态可视化的效果
- 部分软件工具免费使用的功能很少，需要付费才能体验到更全的功能
- 需要掌握良好的前端知识，上手很慢；后期前端代码量大等

本系列的教程讲解的是一个比较新的Python可视化库：Plotly，它主要有以下的特点：

- 图形多样化：plotly中能够绘制多种图形，常见的折线图、散点图、饼图、桑基图、气泡图、联合分布图；还有和机器学习、统计相关的各种图形等
- 开源免费：Plotly官网的所有资源都是免费，用户可直接使用
- 在线可编辑：Plotly有自己的在线可编辑平台，使用者将图形上传即可编辑和分享
- 图形绚丽：Plotly的图形全部是动态可视化的，而且还有多种颜色面板供用户使用
- 代码量少：Plotly绘图的过程中，尤其是使用Plotly的高级封装Plotly_express，一行代码即可实现非常漂亮的图形等



## 前期工作

如果屏幕前的你热爱python，喜欢数据可视化，那么我肯定：Plotly绝对让你爱不释手。本项目针对的是有一定Python基础的同学，在正式学习之前，希望屏幕前的你：

1. 有一定的Python基础，喜欢python可视化；
2. 掌握python第三库pandas库的使用，能够进行数据处理、数据分析等工作；
3. 掌握Jupyter notebook的安装和使用，本项目的源代码全部在notebook中运行



笔者真正接触Plotly到现在也才一年半的时间，本项目的主要参考资料是官网，同时结合了很多自己模拟的数据和案例；在笔者自己学习和整理资料的过程中，不可避免地会有知识点过于浅薄的地方，目前笔者掌握的内容也只是Plotly的冰山一角，更希望能够和读者朋友一起共同深入学习Plotly。

最后想说的是，如果对Plotly可视化感兴趣，学完本课程之后，仍然想继续前进，建议到官网上进行查阅相关资料来提升自己，相信你会收益颇丰！附上官网学习地址：https://plotly.com/ 和 https://plotly.com/python/



## 项目介绍

关于项目名，wow-plotly，当笔者第一次看到Plotly官网中的快速入门图表，真的有被惊艳到，非常吃惊，因此用了wow来表达笔者自己的惊讶之情，也希望通过这样的一个开源教程，屏幕前的你也能够大吃一惊：wow，原来可视化的图标可以这么酷炫！

下面👇的思维导图是目前入门Plotly的学习大纲，总共准备通过5大章节来介绍整个项目：

![](https://tva1.sinaimg.cn/large/008i3skNgy1gs4ntzab1kj30zx0u0grv.jpg)

## 使用说明

- 使用前请安装好Python环境，建议用anaconda；安装好jupyter notebook和扩展的内置插件
- 使用过程中如果有任何问题，或是你对本项目的内容有好的建议，欢迎留言一起交流

## 贡献者

本项目的主要贡献者:

| 成员   | 个人简介                             | 个人主页         |
| ------ | ------------------------------------ | ---------------- |
| 皮钱超 | 项目负责人，厦门大学硕士，数据分析师 | 公众号：尤而小屋 |
| 谢文睿 | Datawhale成员                        |                  |

## 关注我们
> "Datawhale是一个专注AI领域的开源组织，以“for the learner，和学习者一起成长”为愿景，构建对学习者最有价值的开源学习社区。关注我们，一起学习成长。"

![Datawhale是一个专注AI领域的开源组织，以“for the learner，和学习者一起成长”为愿景，构建对学习者最有价值的开源学习社区。关注我们，一起学习成长。](https://tva1.sinaimg.cn/large/008i3skNgy1gs4yhidbilj30by0bygmk.jpg)

## LICENSE

[![知识共享许可协议](https://camo.githubusercontent.com/9b67185684b4d255c9be19bcf5416c62f8be06cb6597f1e57242473b65a4ce6b/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f6c6963656e73652d434325323042592d2d4e432d2d5341253230342e302d6c6967687467726579)](http://creativecommons.org/licenses/by-nc-sa/4.0/)

本作品采用[知识共享署名-非商业性使用-相同方式共享 4.0 国际许可协议](http://creativecommons.org/licenses/by-nc-sa/4.0/)进行许可。

