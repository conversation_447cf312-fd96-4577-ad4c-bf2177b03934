# 华为OD算法题解

本项目选取华为OD算法题，主要是该套算法题都是基于具体场景，通过精选100分题、200分题以及其他100分题能扩大数据结构知识体系，并采用模块化代码，形成解题思路。

**试题来源：** 华为OD联盟整理收集

## 使用说明

1. 本项目主要基于Python语言，使用很多Python语言的标准库，希望大家能通过题目，更好地熟悉Python语法，并灵活运用语法特性。
2. 在推荐资料部分，给出了学习Python和算法的优秀资料，如果大家觉得解题有困难，可以按照顺序学习这些资料，再来练习算法题。
3. 可以先完成精选100分题，再练习200分题，最后可以挑选其他100分题练习，建议每天练习3道题，熟悉解题思路。
4. 如果觉得解答不详细，可以[点击这里](https://github.com/datawhalechina/huawei-od-python/issues)提交你希望补充内容或者习题编号，我们看到后会尽快进行完善。

### 在线阅读地址

在线阅读地址：https://datawhalechina.github.io/huawei-od-python

### 环境安装

1. Python版本

Python 3.8以上版本。

2. 本地启动docsify
```shell
docsify serve ./docs
```

## 协作规范

1. 默认使用Python语言。
2. 做完一道题后，需提交程序及`markdown`文档，文档命名按题号。
3. 程序提交至`codes`文件夹，`markdown`文档提交至`docs`文件夹，并上传GitHub仓库。
4. `markdown`文档格式：包含题目标题、题目描述、输入描述、输出描述、示例描述、解题思路、解题代码；文档命名：001_题目名（小写英文，单词用`-`分隔）.md，可参考模板`docs/template.md`。
5. 图片格式：提供`png`格式的图片；图片命名：001-图片描述（小写英文，单词用`-`分隔）。

### 项目进度

|  题号   |         负责人         | 完成情况 |
| :-----: | :--------------------: | :------: |
| 其他100分题 001~020 |   陈春龙、胡锐锋    |  已完成  |
| 其他100分题 021~040 |  陈希、胡锐锋   |  已完成  |
| 其他100分题 041~060 |    左凯文、胡锐锋 |  已完成  |
| 其他100分题 061~080 | 张超、胡锐锋  |  已完成  |
| 其他100分题 081~100 | 胡锐锋 |  已完成  |
| 其他100分题 101~120 |   毛瑞盈、胡锐锋    |  已完成  |
| 其他100分题 121~140 |  胡锐锋   |  已完成  |
| 其他100分题 141~150 |  冯亚林、胡锐锋  |  已完成  |
| 其他100分题 151~160 |   李洪荣、胡锐锋   |  已完成  |
| 其他100分题 161~180 | 李宇鸿、胡锐锋 |  已完成  |
| 其他100分题 181~200 |   袁畅、胡锐锋    |  已完成  |
| 其他100分题 201~218 |  胡锐锋   |  已完成  |
| 200分题 001~023 |  周理璇、胡锐锋  |  已完成   |
| 200分题 024~051 |   李碧涵、胡锐锋    |  已完成  |
| 精选100分题 001~027 |  胡锐锋   |  已完成  |
| 精选100分题 028~050 |   李昌盛、胡锐锋   |  已完成  |

## 项目结构

<pre>
codes----------------------------------------------习题代码
|   +---choice100--------------------------------------精选100分题代码
|   +---others100--------------------------------------其他100分题代码
|   +---questions200-----------------------------------200分题代码
docs-----------------------------------------------习题解答
</pre>

## 致谢

**核心贡献者**

- [胡锐锋-项目负责人](https://github.com/Relph1119) （Datawhale成员-华东交通大学-系统架构设计师）
- [李碧涵](https://github.com/libihan) （Datawhale成员-东南大学-软件开发工程师）
- [周理璇](https://github.com/Aomferni) （Datawhale成员-电子科技大学-Linux C 软件开发工程师）
- [李宇鸿](https://github.com/PeakWalkerLYH) （英国利兹大学-软件开发工程师）
- [陈春龙](https://github.com/D-Dragon0318) （广东工业大学-计算机科学与技术）
- [李昌盛](https://github.com/jackielics) （Datawhale意向成员-杭州电子科技大学-软件开发）
- [陈希](https://github.com/CompassNull) （东莞理工学院-数据分析）
- [毛瑞盈](https://github.com/catcooc/) （南京大学-凝聚态物理）
- [冯亚林](https://github.com/Westwood-Lin) （Datawhale意向成员-南京大学-AI安全）
- [张超](https://github.com/BITprogramMan) （中国科学技术大学-nlp算法工程师）
- [袁畅](https://github.com/voyagebio) （Datawhale意向成员-河海大学-数据分析）
- [左凯文](https://github.com/Regankevin) （Datawhale意向成员-华威大学-计算机科学）
- [李洪荣](https://github.com/duqing12) （东华理工大学-数据科学与大数据技术）
  
**其他**

特别感谢 [@Sm1les](https://github.com/Sm1les)、[@LSGOMYP](https://github.com/LSGOMYP) 对本项目的帮助与支持。

## 推荐资料

【1】 [Datawhale的《聪明办法学Python第二版》](https://github.com/datawhalechina/learn-python-the-smart-way-v2)  
【2】 [Datawhale的《LeetCode算法笔记》](https://github.com/datawhalechina/leetcode-notes)   
【3】 [Carl的《代码随想录》](https://programmercarl.com/)   

## 关注我们

<div align=center>
<p>扫描下方二维码关注公众号：Datawhale</p>
<img src="images/qrcode.jpeg" width = "180" height = "180">
</div>
&emsp;&emsp;Datawhale，一个专注于AI领域的学习圈子。初衷是for the learner，和学习者一起成长。目前加入学习社群的人数已经数千人，组织了机器学习，深度学习，数据分析，数据挖掘，爬虫，编程，统计学，Mysql，数据竞赛等多个领域的内容学习，微信搜索公众号Datawhale可以加入我们。

## LICENSE
<a rel="license" href="http://creativecommons.org/licenses/by-nc-sa/4.0/"><img alt="知识共享许可协议" style="border-width:0" src="https://img.shields.io/badge/license-CC%20BY--NC--SA%204.0-lightgrey" /></a><br />本作品采用<a rel="license" href="http://creativecommons.org/licenses/by-nc-sa/4.0/">知识共享署名-非商业性使用-相同方式共享 4.0 国际许可协议</a>进行许可。