{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from wenxin_llm import Wenxin_LLM"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from dotenv import find_dotenv, load_dotenv\n", "import os\n", "\n", "# 读取本地/项目的环境变量。\n", "\n", "# find_dotenv()寻找并定位.env文件的路径\n", "# load_dotenv()读取该.env文件，并将其中的环境变量加载到当前的运行环境中\n", "# 如果你设置的是全局的环境变量，这行代码则没有任何作用。\n", "_ = load_dotenv(find_dotenv())\n", "\n", "# 获取环境变量 OPENAI_API_KEY\n", "wenxin_api_key = os.environ[\"wenxin_api_key\"]\n", "wenxin_secret_key = os.environ[\"wenxin_secret_key\"]"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["llm = Wenxin_LLM(model = \"ERNIE-Bot-turbo\", api_key=wenxin_api_key, secret_key=wenxin_secret_key)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["'您好，我是百度研发的知识增强大语言模型，中文名是文心一言，英文名是ERNIE Bot。我能够与人对话互动，回答问题，协助创作，高效便捷地帮助人们获取信息、知识和灵感。\\n\\n如果您有任何问题，请随时告诉我。'"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["llm(\"你是谁\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["from spark_llm import Spark_LLM"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["from dotenv import find_dotenv, load_dotenv\n", "import os\n", "\n", "# 读取本地/项目的环境变量。\n", "\n", "# find_dotenv()寻找并定位.env文件的路径\n", "# load_dotenv()读取该.env文件，并将其中的环境变量加载到当前的运行环境中\n", "# 如果你设置的是全局的环境变量，这行代码则没有任何作用。\n", "_ = load_dotenv(find_dotenv())\n", "#填写控制台中获取的 APPID 信息\n", "appid = os.environ[\"spark_appid\"]\n", "#填写控制台中获取的 APISecret 信息\n", "api_secret = os.environ[\"spark_api_secret\"]\n", "#填写控制台中获取的 APIKey 信息\n", "api_key = os.environ[\"spark_api_key\"]"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["llm = Spark_LLM(model = \"spark\", appid=appid, api_secret=api_secret, api_key=api_key)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["'\\n\\n我是一个AI语言模型，可以回答你的问题和提供帮助。'"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["llm(\"你是谁\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["from zhipuai_llm import ZhipuAILLM\n", "\n", "from dotenv import find_dotenv, load_dotenv\n", "import os\n", "\n", "# 读取本地/项目的环境变量。\n", "\n", "# find_dotenv()寻找并定位.env文件的路径\n", "# load_dotenv()读取该.env文件，并将其中的环境变量加载到当前的运行环境中\n", "# 如果你设置的是全局的环境变量，这行代码则没有任何作用。\n", "_ = load_dotenv(find_dotenv())\n", "\n", "api_key = os.environ[\"ZHIPUAI_API_KEY\"]    #填写控制台中获取的 APIKey 信息"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["'我是一个名为 ChatGLM 的人工智能助手，由智谱 AI 公司于2023年训练的语言模型开发而成。我的任务是针对用户的问题和要求提供适当的答复和支持。'"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["llm = ZhipuAILLM(model=\"chatglm_pro\", zhipuai_api_key=api_key, temperature=0.1)\n", "llm(\"你是谁\") "]}, {"cell_type": "markdown", "metadata": {}, "source": ["测试原生接口"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["from call_llm import get_completion"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["from dotenv import find_dotenv, load_dotenv\n", "import os\n", "\n", "# 读取本地/项目的环境变量。\n", "\n", "# find_dotenv()寻找并定位.env文件的路径\n", "# load_dotenv()读取该.env文件，并将其中的环境变量加载到当前的运行环境中\n", "# 如果你设置的是全局的环境变量，这行代码则没有任何作用。\n", "_ = load_dotenv(find_dotenv())\n", "\n", "# 获取环境变量 OPENAI_API_KEY\n", "openai_api_key = os.environ[\"OPENAI_API_KEY\"]\n", "wenxin_api_key = os.environ[\"wenxin_api_key\"]\n", "wenxin_secret_key = os.environ[\"wenxin_secret_key\"]\n", "spark_appid = os.environ[\"spark_appid\"]\n", "spark_api_secret = os.environ[\"spark_api_secret\"]\n", "spark_api_key = os.environ[\"spark_api_key\"]\n", "zhipu_api_key = os.environ[\"ZHIPUAI_API_KEY\"]\n", "\n", "# os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:7890'\n", "# os.environ[\"HTTP_PROXY\"] = 'http://127.0.0.1:7890'"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["'我是一个人工智能助手，可以回答你的问题并提供帮助。有什么可以帮到你的吗？'"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["get_completion(\"你是谁\",model=\"gpt-3.5-turbo\", api_key=openai_api_key)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["'您好，我是百度研发的知识增强大语言模型，中文名是文心一言，英文名是ERNIE Bot。我能够与人对话互动，回答问题，协助创作，高效便捷地帮助人们获取信息、知识和灵感。'"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["get_completion(\"你是谁\",model=\"ERNIE-Bot-turbo\", api_key=wenxin_api_key, secret_key=wenxin_secret_key)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["'\\n\\n我是一个AI语言模型，可以回答你的问题和提供帮助。'"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["get_completion(\"你是谁\",model=\"Spark-1.5\", appid=spark_appid, api_key=spark_api_key, api_secret=spark_api_secret)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["'我是一个名为 ChatGLM 的人工智能助手，由智谱 AI 公司于2023年训练的语言模型开发而成。我的任务是针对用户的问题和要求提供适当的答复和支持。'"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["get_completion(\"你是谁\",model=\"chatglm_std\", api_key=zhipu_api_key)"]}], "metadata": {"kernelspec": {"display_name": "langchain", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 2}