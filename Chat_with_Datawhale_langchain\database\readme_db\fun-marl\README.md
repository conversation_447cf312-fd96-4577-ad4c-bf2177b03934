# fun-marl

本项目旨在根据英文书籍、综述论文，梳理多智能体强化学习(Multi-Agent RL)的相关知识点。在内容深度上，希望能够对MARL领域内的博弈论及其解法，还有MARL常见算法类型能够均有深入梳理；在内容广度上，希望能够涵盖博弈论、单智能体强化学习、多智能体强化学习，以及多智能体强化学习的挑战。



## 内容导航

| 章节                                |  进度  |
| :---------------------------------- | :----: |
| 第一章 强化学习简介                 | 更新中 |
| 第二章 博弈：多智能体之间交互的模型 | 更新中 |
| 第三章 博弈的解概念                 | 更新中 |
| 第四章 多智能体强化学习：概览       | 更新中 |
| 第五章 多智能体强化学习：基础算法   |        |
| 第六章 多智能体强化学习：算法进阶   |        |
| 第七章：多智能体环境                |        |



**注：** 与该项目有关的意见以及建议可以联系<<EMAIL>>



## 关注我们

<div align=center>
<p>扫描下方二维码关注公众号：Datawhale</p>
<img src="https://raw.githubusercontent.com/datawhalechina/pumpkin-book/master/res/qrcode.jpeg" width = "180" height = "180">
</div>


## LICENSE

<a rel="license" href="http://creativecommons.org/licenses/by-nc-sa/4.0/"><img alt="知识共享许可协议" style="border-width:0" src="https://img.shields.io/badge/license-CC%20BY--NC--SA%204.0-lightgrey" /></a><br />本作品采用<a rel="license" href="http://creativecommons.org/licenses/by-nc-sa/4.0/">知识共享署名-非商业性使用-相同方式共享 4.0 国际许可协议</a>进行许可。

*注：默认使用CC 4.0协议，也可根据自身项目情况选用其他协议*
