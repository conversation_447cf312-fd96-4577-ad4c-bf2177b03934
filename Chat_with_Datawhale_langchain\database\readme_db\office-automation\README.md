# office-automation

课程基本信息

- 学习周期：14天，每天平均花费时间1小时-3小时不等，根据个人学习接受能力强弱有所浮动。
- 学习形式：理论学习 + 练习
- 人群定位：有Python语言编程基础，对自动化办公有需求的学员。
- 先修内容：<a href="https://github.com/datawhalechina/learn-python-the-smart-way">Python编程语言</a>
- 相关课程：<a href="https://github.com/datawhalechina/team-learning-program/tree/master/CollectData" >数据采集</a>
- 测试课程：<a href="https://github.com/datawhalechina/team-learning-program/tree/master/OfficeAutomation" >Datawhale组队学习-办公自动化</a>

### 课程大纲

**Task01 文件处理与邮件自动化**
- 文件路径识别、处理、文件夹的操作理论学习
- 文件自动化处理实践
- 邮件自动发送理论学习

**Task02 Python与excel**
- Excel读取与写入
- Excel样式调整
- 综合练习

**Task03 Python与word和PDF**
- python与word相关的理论知识学习
- python与PDF相关的理论知识学习

**Task04 简单的Python爬虫**
- requests库的理论与实践
- HTML页面解析与提取方法
- 自如公寓数据抓取
- 36kr信息抓取与邮件发送

**Task05 Python操作钉钉自动化**
- Python操作钉钉的相关知识学习

**Task06 其它推荐软件和网页**
- 一些好用的小工具和网页

### 致谢

感谢以下成员对项目推进作出的贡献

<table border="0">
  <tbody>
    <tr align="center" >
      <td>
         <a href="https://github.com/muxiaoxiong"><img width="70" height="70" src="https://github.com/muxiaoxiong.png?s=40" alt="pic"></a><br>
         <a href="https://github.com/muxiaoxiong">牧小熊</a>
         <p> Task04&Task06 </p>
      </td>
      <td>
         <a href="https://github.com/XksA-me"><img width="70" height="70" src="https://github.com/XksA-me.png?s=40" alt="pic"></a><br>
         <a href="https://github.com/XksA-me">老表</a>
         <p>Task02</p>
      </td>
      <td>
         <a href="https://github.com/double-point"><img width="70" height="70" src="https://github.com/double-point.png?s=40" alt="pic"></a><br>
         <a href="https://github.com/double-point">小一</a>
         <p>Task05&Task03</p>
      </td>
      <td>
         <a href="https://github.com/1121091694"><img width="70" height="70" src="https://github.com/1121091694.png?s=40" alt="pic"></a><br>
         <a href="https://github.com/1121091694">赵信达</a>
         <p>Task01</p>
      </td>
      <td>
         <a href="https://github.com/feijuan"><img width="70" height="70" src="https://github.com/feijuan.png?s=40" alt="pic"></a><br>
         <a href="https://github.com/feijuan">于鸿飞</a>
         <p>Task03</p>
      </td>
    </tr>
  </tbody>
</table>


关于Datawhale： Datawhale是一个专注于数据科学与AI领域的开源组织，汇集了众多领域院校和知名企业的优秀学习者，聚合了一群有开源精神和探索精神的团队成员。Datawhale 以“for the learner，和学习者一起成长”为愿景，鼓励真实地展现自我、开放包容、互信互助、敢于试错和勇于担当。同时 Datawhale 用开源的理念去探索开源内容、开源学习和开源方案，赋能人才培养，助力人才成长，建立起人与人，人与知识，人与企业和人与未来的联结。

![logo.png](https://camo.githubusercontent.com/8578ee173c78b587d5058439bbd0b98fa39c173def229a8c3d957e62aac0b649/68747470733a2f2f696d672d626c6f672e6373646e696d672e636e2f323032303039313330313032323639382e706e67237069635f63656e746572)
