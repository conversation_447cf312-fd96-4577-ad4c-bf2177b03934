# 第九章 总结

**恭喜您完成了本书第一单元内容的学习！**

总的来说，在第一部分中，我们学习并掌握了关于 Prompt 的两个核心原则：

- 编写清晰具体的指令；
- 如果适当的话，给模型一些思考时间。

您还学习了迭代式 Prompt 开发的方法，并了解了如何找到适合您应用程序的 Prompt 的过程是非常关键的。

我们还讨论了大型语言模型的许多功能，包括摘要、推断、转换和扩展。您也学习了如何搭建个性化的聊天机器人。在第一部分中，您的收获应该颇丰，希望通过第一部分学习能为您带来愉悦的体验。

我们期待您能灵感迸发，尝试创建自己的应用。请大胆尝试，并分享给我们您的想法。您可以从一个微型项目开始，或许它具备一定的实用性，或者仅仅是一项有趣的创新。请利用您在第一个项目中得到的经验，去创造更优秀的下一项目，以此类推。如果您已经有一个宏大的项目设想，那么，请毫不犹豫地去实现它。

最后，希望您在完成第一部分的过程中感到满足，感谢您的参与。我们热切期待着您的惊艳作品。接下来，我们将进入第二部分的学习！
