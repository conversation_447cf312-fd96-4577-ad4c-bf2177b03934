# LeetCode 算法笔记（Leetcode-Notes）

## 项目简介

- 超详细的「算法与数据结构」基础讲解教程，「LeetCode」经典题目的详细解析。
- 本项目汇总整理了 100 道互联网大厂面试的高频算法真题，帮助面试者更有针对性的准备面试。
- 本项目易于理解，没有大跨度的思维跳跃，项目中使用大量图示、例子来帮助理解。
- 本项目先从基础的数据结构和算法开始讲解，再针对不同分类的数据结构和算法，进行具体题目的讲解分析。让学习者可以通过「算法基础理论学习」和「编程实战学习」相结合的方式，彻底的掌握算法知识。

## 使用说明

### 1. 章节划分

1. 项目总共 8 个章节，其中第 01 ~ 05 章节为「算法与数据结构知识」，第 06 ~ 07 章节为「100 道高频面试题汇总」，第 08 章节为「各章节习题解析」。
2. 项目中 的第 01 ~ 07 章节，每一章节对应一个「学习周期」，章节中每一小节对应一个「学习专题」。
3. 小节中每天都有对应的「学习内容」和「练习题目（每天 3 道）」。

### 2. 时间花费

1. 项目总计 7 个学习周期，共 104 天学习任务。
2. 每个学习周期为 14 ~ 16 天学习任务。
3. 每天平均花费时间 1 ~ 3 小时不等，根据个人学习接受能力强弱有所浮动。

### 3. 学习建议

1. 从未刷过算法题目的同学，推荐先看下 「01.01.03 LeetCode 入门及攻略（第 01 ~ 02 天）」 相关内容，对应链接为：[网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch01/01.01/01.01.03-LeetCode-Guide.md)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch01/01.01/01.01.03-LeetCode-Guide.md)。
2. 零基础或者想综合学习算法基础知识的同学，推荐按照项目中制定的学习周期进行专题学习。
3. 近期有面试需求的同学，推荐直接学习「06. 面试篇（上）（共 16 天）」、「07. 面试篇（下）（共 16 天）」这两期相关内容。
4. 可在看完每日的学习内容之后，选择每日对应的练习题目进行巩固练习。
5. 做完每日练习题目之后，如有余力，可通过相关专题下的题目清单进行拓展练习。

### 在线阅读

[https://datawhalechina.github.io/leetcode-notes/](https://datawhalechina.github.io/leetcode-notes/)

### Github 地址

[https://github.com/datawhalechina/leetcode-notes](https://github.com/datawhalechina/leetcode-notes)

## 章节目录

### 01. 算法入门与数组篇（共 14 天）：[网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch01/index.md)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch01/index.md)

| 章节标题                               | 链接地址                                                     |
| -------------------------------------- | ------------------------------------------------------------ |
| 01.01 数据结构与算法简介、LeetCode 入门及攻略（第 01 ~ 02 天） | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch01/01.01/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch01/01.01/index.md) |
| 01.02 数组基础（第 03 ~ 04 天） | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch01/01.02/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch01/01.02/index.md) |
| 01.03 数组排序（第 05 ~ 08 天） | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch01/01.03/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch01/01.03/index.md) |
| 01.04 数组二分查找（第 09 ~ 11 天） | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch01/01.04/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch01/01.04/index.md) |
| 01.05 数组双指针、滑动窗口（第 12 ~ 14 天） | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch01/01.05/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch01/01.05/index.md) |

### 02. 基础数据结构篇（上）：链表、堆栈、队列（共 15 天）：[网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch02/index.md)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch02/index.md)

| 章节标题                               | 链接地址                                                     |
| -------------------------------------- | ------------------------------------------------------------ |
| 02.01 链表（第 01 ~ 04 天） | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch02/02.01/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch02/02.01/index.md) |
| 02.02 堆栈与单调栈（第 05 ~ 07 天） | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch02/02.02/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch02/02.02/index.md) |
| 02.03 深度优先搜索（第 08 ~ 10 天） | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch02/02.03/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch02/02.03/index.md) |
| 02.04 队列与优先队列（第 11 ~ 12 天） | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch02/02.04/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch02/02.04/index.md) |
| 02.05 广度优先搜索（第 13 ~ 14 天） | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch02/02.05/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch02/02.05/index.md) |
| 02.06 拓扑排序（第 15 天） | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch02/02.06/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch02/02.06/index.md) |

### 03. 基础数据结构篇（下）：哈希表、字符串、二叉树、并查集（共 14 天）：[网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch03/index.md)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch03/index.md)

| 章节标题                               | 链接地址                                                     |
| -------------------------------------- | ------------------------------------------------------------ |
| 03.01 哈希表（第 01 ~ 02 天） | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch03/03.01/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch03/03.01/index.md) |
| 03.02 字符串与字符串匹配（ 第 03 ~ 07 天） | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch03/03.02/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch03/03.02/index.md) |
| 03.03 二叉树（第 08 ~ 10 天） | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch03/03.03/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch03/03.03/index.md) |
| 03.04 二叉搜索树（第 11 ~ 12 天） | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch03/03.04/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch03/03.04/index.md) |
| 03.05 并查集（第 13 ~ 14 天） | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch03/03.05/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch03/03.05/index.md) |

### 04. 基础算法篇：枚举、递归、分治、回溯、贪心、位运算（共 14 天）：[网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch04/index.md)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch04/index.md)

| 章节标题                               | 链接地址                                                     |
| -------------------------------------- | ------------------------------------------------------------ |
| 04.01 枚举算法（第 01 ~ 02 天） | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch04/04.01/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch04/04.01/index.md) |
| 04.02 递归算法与分治算法（第 03 ~ 06 天） | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch04/04.02/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch04/04.02/index.md) |
| 04.03 回溯算法（第 07 ~ 09 天） | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch04/04.03/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch04/04.03/index.md) |
| 04.04 贪心算法（第 10 ~ 12 天） | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch04/04.04/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch04/04.04/index.md) |
| 04.05 位运算（第 13 ~ 14 天） | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch04/04.05/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch04/04.05/index.md) |

### 05. 动态规划篇（共 15 天）：[网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch05/index.md)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch05/index.md)

| 章节标题                               | 链接地址                                                     |
| -------------------------------------- | ------------------------------------------------------------ |
| 05.01 动态规划基础、记忆化搜索（ 第 01 ~ 02 天） | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch05/05.01/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch05/05.01/index.md) |
| 05.02 线性动态规划（第 03 ~ 06 天） | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch05/05.02/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch05/05.02/index.md) |
| 05.03 背包问题（第 07 ~ 09 天） | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch05/05.03/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch05/05.03/index.md) |
| 05.04 区间 DP 和 树形 DP（第 10 ~ 12 天） | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch05/05.04/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch05/05.04/index.md) |
| 05.05  状压 DP、计数 DP 和数位 DP（第 13 ~ 15 天） | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch05/05.05/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch05/05.05/index.md) |

### 06. 面试篇（上）（共 16 天）[网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch06/index.md)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch06/index.md)

| 章节标题                               | 链接地址                                                     |
| -------------------------------------- | ------------------------------------------------------------ |
| 06.01 第 001 ~ 012 题（第 01 ~ 04 天） | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch06/06.01/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch06/06.01/index.md) |
| 06.02 第 013 ~ 025 题（第 05 ~ 08 天） | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch06/06.02/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch06/06.02/index.md) |
| 06.03 第 026 ~ 037 题（第 09 ~ 12 天） | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch06/06.03/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch06/06.03/index.md) |
| 06.04 第 038 ~ 050 题（第 13 ~ 16 天） | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch06/06.04/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch06/06.04/index.md) |

### 07. 面试篇（下）（共 16 天）：[网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch07/index.md)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch07/index.md)

| 章节标题                               | 链接地址                                                     |
| -------------------------------------- | ------------------------------------------------------------ |
| 07.01 第 051 ~ 062 题（第 01 ~ 04 天） | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch07/07.01/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch07/07.01/index.md) |
| 07.02 第 063 ~ 074 题（第 05 ~ 08 天） | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch07/07.02/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch07/07.02/index.md) |
| 07.03 第 075 ~ 087 题（第 09 ~ 12 天） | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch07/07.03/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch07/07.03/index.md) |
| 07.04 第 088 ~ 100 题（第 13 ~ 16 天） | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/ch07/07.04/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/ch07/07.04/index.md) |

### 08. 各章节习题解析：[网页链接](https://datawhalechina.github.io/leetcode-notes/#/keys/index.md)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/keys/index.md)

| 章节标题                               | 链接地址                                                     |
| -------------------------------------- | ------------------------------------------------------------ |
| 01 章节习题解析 | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/keys/ch01-keys/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/keys/ch01-keys/index.md) |
| 02 章节习题解析 | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/keys/ch02-keys/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/keys/ch02-keys/index.md) |
| 03 章节习题解析 | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/keys/ch03-keys/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/keys/ch03-keys/index.md) |
| 04 章节习题解析 | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/keys/ch04-keys/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/keys/ch04-keys/index.md) |
| 05 章节习题解析 | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/keys/ch05-keys/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/keys/ch05-keys/index.md) |
| 06 章节习题解析 | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/keys/ch06-keys/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/keys/ch06-keys/index.md) |
| 07 章节习题解析 | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/keys/ch07-keys/index)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/keys/ch07-keys/index.md) |
| LeetCode 题解（字典序排序） | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/keys/solutions/Solutions-List)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/keys/solutions/Solutions-List.md) |
| LeetCode 题解（按分类排序） | [网页链接](https://datawhalechina.github.io/leetcode-notes/#/keys/solutions/Categories-List)、[Github 链接](https://github.com/datawhalechina/leetcode-notes/blob/main/docs/keys/solutions/Categories-List.md) |

## 致谢

### 贡献者名单

| 姓名                                  | 职责                          | 简介                        | 联系          |
| :------------------------------------ | :---------------------------- | :-------------------------- | ------------- |
| [杨世超](https://github.com/itcharge) | 项目负责人，第 01~07 章节内容 | https://github.com/itcharge | <EMAIL> |

### 其他

1. 特别感谢 [@LSGOMYP](https://github.com/LSGOMYP)、[@Sm1les](https://github.com/Sm1les) 对本项目的帮助与支持。

## 关注我们

<div align=center>
<p>扫描下方二维码关注公众号：Datawhale</p>
<img src="https://raw.githubusercontent.com/datawhalechina/pumpkin-book/master/res/qrcode.jpeg" width = "180" height = "180">
</div>

## LICENSE

<a rel="license" href="http://creativecommons.org/licenses/by-nc-sa/4.0/"><img alt="知识共享许可协议" style="border-width:0" src="https://img.shields.io/badge/license-CC%20BY--NC--SA%204.0-lightgrey" /></a><br/>本作品采用 <a rel="license" href="http://creativecommons.org/licenses/by-nc-sa/4.0/">知识共享署名 - 非商业性使用 - 相同方式共享 4.0 国际许可协议</a> 进行许可。
