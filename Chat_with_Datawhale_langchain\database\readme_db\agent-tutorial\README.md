# agent-tutorial

## 教程介绍
本教程是 Datawhale 成员写作的关于 Agent 的教程，特点是通过实践引导学习者加深对Agent的理解。

目前已有的内容，主要用于支持《动手学Agent开发》学习活动，介绍如何使用 [ModelScope Agent](https://github.com/modelscope/modelscope-agent/tree/master) 开发一个智能助手，并探讨一些 Agent 的通用创作思路及应用展望。

学习活动详见[《动手学Agent应用开发》学习手册](https://datawhaler.feishu.cn/docx/DqaydpsFdovWonxDrYxcrBYxnkf)。

## 教程大纲
第1章：Agent入门简介
- [Agent原理](./notebook/第一章：Agent简介/1.1%20Agent原理.md)
- [Agent原理深入+环境配置](./notebook/第一章：Agent简介/1.2%20Agent原理深入+环境配置.md)

第2章：Agent实践：日程规划小助手
- [高德开放API实践](./notebook/第二章：Agent实践/2.1%20高德开放API实践.md)
- [日程规划小助手](./notebook/第二章：Agent实践/2.2%20日程规划小助手.md)

第3章：Agent应用展望
- [Agent通用创作思路](./notebook/第三章：Agent应用展望/3.1%20Agent通用创作思路.md)
- [Agent应用展望](./notebook/第三章：Agent应用展望/3.2%20Agent应用展望.md)
