WEBVTT

00:01.000 --> 00:03.000
B站的小伙伴们好

00:03.000 --> 00:07.000
我是蘑菇书一语二语二强化学习教程的作者之一王奇

00:07.000 --> 00:11.000
今天来有给大家带来一个强化学习的入门指南

00:13.000 --> 00:18.000
本次入门指南基于蘑菇书一语二语二强化学习教程

00:18.000 --> 00:20.000
本书的作者目前都是Dell会员成员

00:20.000 --> 00:22.000
也都是数学在读

00:22.000 --> 00:24.000
下面去介绍每个作者

00:26.000 --> 00:27.000
我是王奇

00:27.000 --> 00:29.000
目前就留于中国科研院大学

00:29.000 --> 00:33.000
引用方向是深度学习、静态视觉以及数据挖掘

00:36.000 --> 00:38.000
杨玉云目前就读于清华大学

00:38.000 --> 00:39.000
他的引用方向为

00:39.000 --> 00:43.000
时空数据挖掘、智能冲砍系统以及深度学习

00:44.000 --> 00:46.000
张记目前就读于北京大学

00:46.000 --> 00:49.000
他的引用方向为强化学习记忆人

00:52.000 --> 00:54.000
接下来开始正式的分享

00:54.000 --> 00:55.000
本次分享分为三部分

00:55.000 --> 00:56.000
第一部分

00:56.000 --> 00:57.000
为什么要学强化学习

00:57.000 --> 00:58.000
第二部分

00:58.000 --> 01:00.000
为什么要用本书来学

01:00.000 --> 01:01.000
第三部分

01:01.000 --> 01:02.000
这本书怎么学最高效

01:04.000 --> 01:06.000
首先讲一下为什么要学强化学习

01:09.000 --> 01:11.000
我们先聊一下强化学习的基本概念

01:11.000 --> 01:15.000
强化学习用来学习如何做出一系列好的决策

01:15.000 --> 01:17.000
而人工智能的基本挑战是

01:17.000 --> 01:20.000
学习在不确定的情况下做出好的决策

01:20.000 --> 01:21.000
这边我举个例子

01:21.000 --> 01:23.000
比如你想让一个小孩学会走路

01:23.000 --> 01:26.000
他就需要通过不断尝试来发现

01:26.000 --> 01:27.000
怎么走比较好

01:27.000 --> 01:28.000
怎么走比较快

01:31.000 --> 01:34.000
强化学习的交互过程可以通过这张图来表示

01:34.000 --> 01:37.000
强化学习由智能体和环境两部分组成

01:37.000 --> 01:38.000
在强化学习过程中

01:38.000 --> 01:40.000
智能体与环境一直在交互

01:41.000 --> 01:44.000
智能体在环境中获取某个状态后

01:44.000 --> 01:46.000
它会利用刚刚的状态输出一个动作

01:46.000 --> 01:48.000
这个动作也被称为决策

01:48.000 --> 01:52.000
然后这个动作会被环境中执行

01:52.000 --> 01:55.000
环境会根据智能体采取的动作

01:55.000 --> 01:57.000
来输出下一个状态

01:57.000 --> 02:00.000
以及当前这个动作带来的奖励

02:00.000 --> 02:01.000
整体它的目的呢

02:01.000 --> 02:04.000
就是尽可能的多的

02:05.000 --> 02:08.000
尽可能多的在环境中获取奖励

02:15.000 --> 02:16.000
强化学习的应用非常广泛

02:16.000 --> 02:19.000
比如说我们可以使用强化学习来玩游戏

02:19.000 --> 02:21.000
玩游戏的话可以玩这种电子游戏

02:21.000 --> 02:23.000
也可以玩这种围棋游戏

02:24.000 --> 02:26.000
围棋游戏中比较出名的一个

02:26.000 --> 02:29.000
强化学习的算法就是AlphaGo

02:31.000 --> 02:32.000
此外我们可以使用强化学习

02:32.000 --> 02:33.000
来控制机器人

02:33.000 --> 02:35.000
以及来实现助力交通

02:35.000 --> 02:37.000
另外还可以使用强化学习

02:37.000 --> 02:39.000
来更好地给我们做推进

02:41.000 --> 02:42.000
接下来就到第二部分

02:42.000 --> 02:46.000
也就是为什么要使用本书来学习强化学习

02:46.000 --> 02:47.000
这部分其实也是讲

02:47.000 --> 02:50.000
这个蘑菇书它出版的一些故事

02:51.000 --> 02:53.000
当时我在学习强化学习的时候

02:53.000 --> 02:55.000
搜集了一些资料

02:55.000 --> 02:58.000
然后我发现这些资料

02:58.000 --> 02:59.000
都有点灰色难懂

02:59.000 --> 03:02.000
并不是那么容易地上手

03:03.000 --> 03:05.000
于是我开始到网上

03:06.000 --> 03:08.000
搜索一些公开课来学习

03:08.000 --> 03:10.000
首先我搜索到的是

03:10.000 --> 03:12.000
李宏毅老师的一些公开课

03:12.000 --> 03:15.000
很多人就是入门深度学习

03:15.000 --> 03:16.000
和基学低门课

03:16.000 --> 03:18.000
其实就是李宏毅老师的课

03:18.000 --> 03:20.000
李宏毅老师的课

03:22.000 --> 03:25.000
李宏毅老师的基础学习和深度学习公开课

03:25.000 --> 03:27.000
在编上有很高的播放量

03:28.000 --> 03:31.000
于是我搜索了李宏毅老师的强化学习课

03:31.000 --> 03:33.000
这门课叫顺强化学习

03:33.000 --> 03:35.000
这门课跟以往的李宏毅老师的

03:35.000 --> 03:37.000
课人的风格都是一样的

03:37.000 --> 03:39.000
就是非常的生动有趣

03:39.000 --> 03:41.000
李宏毅老师经常会用

03:41.000 --> 03:44.000
玩亚达利游戏的例子来讲强化学习

03:44.000 --> 03:46.000
这样强化学你就变得通透易懂

03:46.000 --> 03:48.000
然后就很多人都会

03:48.000 --> 03:51.000
把这门课作为自己的强化学习入门课

03:51.000 --> 03:54.000
这边我们念出了一些

03:54.000 --> 03:57.000
就是观众的一些好评

03:57.000 --> 04:00.000
比如说这边观众说

04:00.000 --> 04:02.000
国内这个讲得最好了

04:02.000 --> 04:04.000
然后李宏毅老师讲得真厉害

04:04.000 --> 04:07.000
还有这个评论比较搞笑

04:07.000 --> 04:09.000
这个视频每天晚上都有

04:09.000 --> 04:11.000
几个到十几个人在看

04:11.000 --> 04:13.000
因此我们可以发现

04:13.000 --> 04:19.000
这门课还是非常受人欢迎的

04:22.000 --> 04:24.000
后来我发现李宏毅老师的课

04:24.000 --> 04:25.000
不是那么全面

04:25.000 --> 04:27.000
他的课主要集中于讲解

04:27.000 --> 04:28.000
迅速强化学算法

04:28.000 --> 04:30.000
而忽略了全球强化学算法

04:30.000 --> 04:33.000
以及一些比较前沿的强化学算法

04:33.000 --> 04:34.000
因此我开始

04:34.000 --> 04:36.000
到网上搜索其他的强化学公开课

04:36.000 --> 04:38.000
当时是搜索到了

04:38.000 --> 04:40.000
周博恩老师的强化学纲要

04:40.000 --> 04:42.000
周博恩老师是

04:42.000 --> 04:45.000
人文智能底层的一个顶尖学者

04:45.000 --> 04:48.000
他在人文智能底层会议

04:48.000 --> 04:50.000
亲爱发表了五十余篇学术论文

04:50.000 --> 04:53.000
然后论文的作用量超过一万次

04:53.000 --> 04:57.000
周博恩老师的这门强化学纲要课

04:57.000 --> 04:58.000
就是理论严谨

04:58.000 --> 04:59.000
然后内容丰富

04:59.000 --> 05:01.000
全面的教训介绍了强化学领域

05:01.000 --> 05:03.000
并有相关代码实践

05:08.000 --> 05:09.000
在学习完

05:09.000 --> 05:14.000
女红衣和周博恩老师两门强化学课以后

05:14.000 --> 05:16.000
我发现还是有一点不足

05:16.000 --> 05:18.000
就是代码的实践还偏少

05:18.000 --> 05:22.000
于是我在网上搜索其他的强化学课

05:22.000 --> 05:24.000
当时就搜索了李克强老师的一个

05:24.000 --> 05:28.000
叫试验冠军代理丛林实践强化学习这门课

05:28.000 --> 05:30.000
这门课有个特别突出的优点

05:30.000 --> 05:32.000
就是实战性强

05:32.000 --> 05:35.000
突兵课只能会使用单单的代码来讲解强化学习

05:35.000 --> 05:37.000
这边也有很多好评

05:40.000 --> 05:44.000
有了这三门课以后

05:44.000 --> 05:47.000
我发现这三门课

05:47.000 --> 05:50.000
通过这些公开课来进学的话

05:50.000 --> 05:52.000
有些优点但也有缺点

05:52.000 --> 05:55.000
优点的话就是这些课都是非常经典的一些公开课

05:55.000 --> 05:58.000
然后他们这些课的这些老师

05:58.000 --> 06:00.000
也都是人文领域的一些大流

06:00.000 --> 06:03.000
然后他们的播放量非常高

06:03.000 --> 06:05.000
就比较受欢迎

06:05.000 --> 06:07.000
但是这些课呢也会存在一些问题

06:07.000 --> 06:09.000
就用这些公开课来进学的课会存在一些问题

06:09.000 --> 06:11.000
比如说它不便于实际的查询

06:11.000 --> 06:14.000
然后对于一些重点的知识点它会缺乏一些讲解

06:14.000 --> 06:17.000
此外这些知识点比较分散

06:17.000 --> 06:19.000
就每个老师他讲的知识点各有侧重

06:19.000 --> 06:23.000
此外就是视频的扩弱化比较严重

06:23.000 --> 06:27.000
因此基于这三门公开课

06:27.000 --> 06:31.000
就是我和杨亦远和江静

06:31.000 --> 06:33.000
三个DDR成员

06:33.000 --> 06:36.000
对这些三门课的内容进行整合补充优化

06:36.000 --> 06:39.000
然后写出了这本教材叫

06:39.000 --> 06:41.000
Easy IR 强化域教程

06:45.000 --> 06:47.000
这本教程当时是在

06:47.000 --> 06:49.000
一开始是在Github上发布的

06:49.000 --> 06:51.000
然后Github上呢当时发完以后

06:51.000 --> 06:52.000
就是有很多好评

06:52.000 --> 06:53.000
比如这边

06:53.000 --> 06:55.000
比如说有人的同学就说

06:55.000 --> 06:56.000
昨天非常棒

06:56.000 --> 06:57.000
超越有帮助感谢博主

06:57.000 --> 06:59.000
然后等等等等

06:59.000 --> 07:03.000
然后目前这本书对应的那个Github仓库的

07:03.000 --> 07:06.000
start数已经达48k

07:06.000 --> 07:08.000
然后

07:08.000 --> 07:10.000
它的增长就是它的

07:10.000 --> 07:12.000
start数的一个增长曲线

07:12.000 --> 07:14.000
也是曾经上升的一个态势

07:15.000 --> 07:18.000
后来我们在DataWare举办了一次组队学习

07:18.000 --> 07:20.000
然后组队学习的那个教材呢

07:20.000 --> 07:22.000
就是以我们这本书的

07:22.000 --> 07:25.000
Github的那个在线版

07:25.000 --> 07:27.000
作为一个教材

07:29.000 --> 07:31.000
在组队学习结束以后

07:31.000 --> 07:33.000
然后也是有很多的

07:33.000 --> 07:36.000
来自世界各地的学习的小伙伴

07:36.000 --> 07:37.000
给出好评

07:37.000 --> 07:38.000
比如这边我就

07:38.000 --> 07:40.000
列出了一个小伙伴的好评

07:42.000 --> 07:44.000
然后因为在Github上有很多的读者

07:44.000 --> 07:46.000
会通过issue来给我们反馈

07:46.000 --> 07:49.000
比如说他们会觉得我们来的地方

07:49.000 --> 07:51.000
翻译来电会有些问题

07:51.000 --> 07:52.000
他们会直接列出来

07:52.000 --> 07:55.000
然后我们也会给他们及时的反馈

07:55.000 --> 07:58.000
然后对我们的教程进行优化

07:59.000 --> 08:02.000
后来我们初入以后

08:02.000 --> 08:07.000
有幸得到了七位强行学习领域

08:07.000 --> 08:08.000
大开的亲笔推荐

08:08.000 --> 08:10.000
比如说台湾大学的宁欧英老师

08:10.000 --> 08:13.000
周伯伦老师以及李克强老师

08:13.000 --> 08:17.000
还有比如清华的宁生梦老师

08:17.000 --> 08:19.000
汪峥老师张伟老师

08:19.000 --> 08:21.000
胡玉静老师等等

08:22.000 --> 08:23.000
然后这边我们列出了

08:23.000 --> 08:25.000
宁欧英老师的一个推荐语

08:25.000 --> 08:27.000
因为我觉得宁欧英老师的

08:27.000 --> 08:30.000
推荐语非常的有趣

08:30.000 --> 08:31.000
比如他说

08:31.000 --> 08:32.000
他当时看到我们这个

08:32.000 --> 08:34.000
伊丽艾尔这本书的时候

08:34.000 --> 08:35.000
他第一个想法是

08:35.000 --> 08:37.000
这成员把强化学习的知识整理得真好

08:37.000 --> 08:39.000
不仅有理论说明

08:39.000 --> 08:40.000
还加上了诚实实力

08:40.000 --> 08:43.000
同学们以后可以直接阅读这份教程

08:43.000 --> 08:45.000
这样我以后上课

08:45.000 --> 08:48.000
就不用再讲强化学习的部分了

08:53.000 --> 08:54.000
可以发现宁欧英对我们

08:54.000 --> 08:56.000
这本书还是挺认可的

08:56.000 --> 08:57.000
然后再来讲一个问题

08:57.000 --> 08:59.000
这本书为什么叫蘑菇书呢

08:59.000 --> 09:01.000
难道是因为作者都是吃货

09:01.000 --> 09:04.000
之前有一本西瓜书

09:04.000 --> 09:05.000
还有一本南瓜书

09:05.000 --> 09:07.000
然后作为一个吃货的话

09:07.000 --> 09:09.000
还有一个传承一下叫蘑菇书

09:09.000 --> 09:12.000
就吃完西瓜南瓜再来热蘑菇

09:12.000 --> 09:14.000
但其实并不是

09:14.000 --> 09:17.000
蘑菇书真的寓意是玛里奥

09:17.000 --> 09:19.000
大家如果玩过超级玛里奥游戏

09:19.000 --> 09:21.000
就知道玛里奥吃了蘑菇以后

09:21.000 --> 09:22.000
会变得更加强大

09:22.000 --> 09:25.000
然后我们也希望读者

09:26.000 --> 09:29.000
在吃下这个蘑菇书以后

09:29.000 --> 09:32.000
能够养有兴致的探索强化学习

09:32.000 --> 09:34.000
然后像玛里奥那样越强大

09:34.000 --> 09:38.000
然后进而在人工智能领域里面

09:38.000 --> 09:41.000
获得一些奇葩收获

09:43.000 --> 09:45.000
这边我们放出了一个小彩蛋

09:45.000 --> 09:48.000
就是我们的作者之一杨亦远

09:48.000 --> 09:50.000
跟宁欧英老师的一个意外接触

09:51.000 --> 09:54.000
当时杨亦远在参加一个顶位的时候

09:54.000 --> 09:59.000
发现宁欧英老师也在参会者名单里面

09:59.000 --> 10:01.000
他给宁欧英老师发了封邮件

10:01.000 --> 10:04.000
介绍了我们当时GitHub的开源教程

10:05.000 --> 10:08.000
然后非常惊喜的就是

10:08.000 --> 10:11.000
宁欧英老师很快地给出了回信

10:11.000 --> 10:15.000
然后也是对我们这个教程给出了好评

10:18.000 --> 10:21.000
这边列出了蘑菇书获得的一些荣誉

10:21.000 --> 10:23.000
比如说人民邮件出版社的季度好书

10:24.000 --> 10:26.000
然后日读期期间

10:26.000 --> 10:29.000
当当计算机期中网的第一名

10:29.000 --> 10:31.000
然后上市时间以后

10:31.000 --> 10:34.000
获得的就是京东人工智能榜的第一名

10:34.000 --> 10:37.000
全网的推文阅读量破十万

10:37.000 --> 10:39.000
然后目前清华大学李淑木教授

10:39.000 --> 10:42.000
小米NLP首位科学家王斌老师

10:42.000 --> 10:45.000
以及百度高级研发工程师李克强老师

10:45.000 --> 10:51.000
还有一些等20家的一个大咖公众号

10:51.000 --> 10:54.000
以及微博大V社区他们进行的一个转发推荐

10:54.000 --> 10:58.000
然后这个也被推荐到华为电影大学的一个保定图书馆

10:59.000 --> 11:02.000
蘑菇书全书一共13章可以分两部分

11:02.000 --> 11:05.000
第一部分他介绍了强学的基础知识

11:05.000 --> 11:07.000
以及传统的强化学习算法

11:07.000 --> 11:09.000
第二部分他介绍了

11:09.000 --> 11:11.000
适用强化学习算法

11:11.000 --> 11:14.000
以及常见问题的解决方法

11:15.000 --> 11:18.000
咱们这本书还有一些突出的特点

11:18.000 --> 11:20.000
比如第一点

11:20.000 --> 11:23.000
我们会利用一些简单生动的例子来解释强化学概念

11:23.000 --> 11:26.000
比如说我们可以用这个玩视频游戏

11:26.000 --> 11:28.000
以及下围棋的例子

11:28.000 --> 11:31.000
来对强化学的一些基本概念做出解释

11:33.000 --> 11:35.000
然后还有一些其他特点

11:35.000 --> 11:38.000
比如说我们会对专业的一些公式

11:38.000 --> 11:40.000
进行详细的推导和分析

11:40.000 --> 11:42.000
这边我们以这个比尔曼方程的

11:42.000 --> 11:44.000
这个推导过程的例子

11:44.000 --> 11:46.000
我们会一步一步的把这个推导过程念出来

11:46.000 --> 11:47.000
不会跳步走

11:47.000 --> 11:49.000
然后此外呢

11:51.000 --> 11:52.000
我们还会对一些

11:52.000 --> 11:54.000
可能大家比较难以理解的地方

11:54.000 --> 11:56.000
我们会加入一些注解

11:56.000 --> 11:57.000
通过这些注解

11:57.000 --> 12:00.000
我们会让大家更容易理解一些相关的概念

12:03.000 --> 12:06.000
此外本书配有对应的一个观念词

12:06.000 --> 12:08.000
习题和面试题

12:08.000 --> 12:10.000
当读者读完一章以后

12:10.000 --> 12:13.000
大家可以通过观念词来快速的掌握重点

12:13.000 --> 12:16.000
然后一个通过习题和面试题

12:16.000 --> 12:18.000
来巩固对知识的理解

12:18.000 --> 12:20.000
然后这个习题和面试题

12:20.000 --> 12:22.000
也方便大家的一个程度补缺

12:22.000 --> 12:23.000
然后最后呢

12:23.000 --> 12:25.000
我们会有对应的代码实战

12:25.000 --> 12:27.000
大家学完这个理论以后

12:27.000 --> 12:30.000
还要通过动手来把这个代码进行

12:30.000 --> 12:32.000
来把这个算法进行一个实践

12:32.000 --> 12:35.000
就是大家把这个算法进行实践以后

12:35.000 --> 12:37.000
大家才算对这个算法

12:37.000 --> 12:39.000
有一个比较深入了解

12:41.000 --> 12:43.000
接下来到最后一部分

12:43.000 --> 12:45.000
叫这本书怎么学比较高效

12:46.000 --> 12:48.000
第一个

12:48.000 --> 12:49.000
当然你可以把这本书

12:49.000 --> 12:51.000
作为三门公开课的一个必要教材

12:51.000 --> 12:54.000
就是当你在看这三门课的时候

12:54.000 --> 12:58.000
如果你发现有哪些概念不是很清楚的情况下

12:58.000 --> 13:00.000
你是可以直接翻到

13:00.000 --> 13:02.000
本书对应的知识点进行学习

13:02.000 --> 13:05.000
当然本书也是完全图形于三门教材的

13:05.000 --> 13:08.000
大家可以直接预读本书进行学习

13:10.000 --> 13:12.000
本书在GitHub上面

13:12.000 --> 13:13.000
配有对应的代码

13:13.000 --> 13:15.000
这个代码也会适宜的更新

13:15.000 --> 13:19.000
大家如果只是想很快的

13:19.000 --> 13:21.000
把这个算法给应用上

13:21.000 --> 13:22.000
大家可以直接去GitHub上

13:22.000 --> 13:24.000
跑对应的代码

13:26.000 --> 13:31.000
此外本书还有相关的一个刊物和修订

13:31.000 --> 13:32.000
这些刊物和修订

13:32.000 --> 13:34.000
也会根据大家的反馈的一些意见

13:34.000 --> 13:36.000
进行一些适宜的更新

13:38.000 --> 13:40.000
然后这边也要讲一下

13:40.000 --> 13:43.000
本书它在GitHub有个叫PK版

13:43.000 --> 13:46.000
然后它还有这种纸质书的版本叫纸质版

13:46.000 --> 13:47.000
它们有什么区别呢

13:47.000 --> 13:50.000
就GitHub的那种PK版本是本书的一个初稿

13:50.000 --> 13:53.000
然后在本书的作者已经憋进来

13:53.000 --> 13:54.000
不断的修改中

13:55.000 --> 13:58.000
我们最后有了纸质版

13:59.000 --> 14:01.000
大家看到我们

14:01.000 --> 14:04.000
对这个PK版本里面有了大量的修订

14:04.000 --> 14:06.000
所以说纸质书的一个质量

14:06.000 --> 14:09.000
相对于PK版本是要高不少的

14:11.000 --> 14:13.000
讲到这里大家可能会有疑问

14:15.000 --> 14:18.000
可能是如果我目前我涉及的工作

14:18.000 --> 14:19.000
不涉及到强化学习

14:19.000 --> 14:21.000
那我还有必要学强化学习吗

14:23.000 --> 14:25.000
对于这个问题我想用乔布斯的观点

14:25.000 --> 14:26.000
叫黏生命的点

14:26.000 --> 14:27.000
或者叫应用相连

14:29.000 --> 14:31.000
这边就举一下乔布斯他本人的例子

14:32.000 --> 14:35.000
乔布斯当时在大学学了一门书法课

14:35.000 --> 14:38.000
这门课在很多人眼里都是没有用处的

14:38.000 --> 14:40.000
而乔布斯他本人也是出于兴趣

14:40.000 --> 14:41.000
才学这门课

14:44.000 --> 14:48.000
他也没有对这门课抱有很大的期望

14:48.000 --> 14:50.000
但是后来发现他这门课

14:50.000 --> 14:53.000
在设计苹果电脑字体的时候

14:53.000 --> 14:54.000
取到了极大作用

14:55.000 --> 14:58.000
如果乔布斯当时没有学这门课的话

14:58.000 --> 14:59.000
大家可能就看不到

14:59.000 --> 15:05.000
苹果电脑中这些优美丰富赏意悦目的字体

15:06.000 --> 15:10.000
而对强化学习来说

15:10.000 --> 15:13.000
强化学习这种应用非常广泛的技术

15:13.000 --> 15:17.000
即使现在可能跟你的工作没有很大关联

15:17.000 --> 15:18.000
但说不定某一天

15:18.000 --> 15:21.000
可能你的工作就要涉及到相关的

15:21.000 --> 15:22.000
涉及到强化学习

15:22.000 --> 15:24.000
这时候如果你在之前

15:24.000 --> 15:27.000
对强化学习有一个基本了解

15:27.000 --> 15:28.000
或者说入门的强化学习

15:28.000 --> 15:31.000
这时候可能对你的工作

15:32.000 --> 15:35.000
会起到一个意想不到的助理

15:39.000 --> 15:42.000
最后我们念出了蘑菇书

15:42.000 --> 15:45.000
在京东以及档案的一个购买的链接

15:45.000 --> 15:46.000
以及对应的二维码

15:47.000 --> 15:49.000
然后大家可以在这两个

15:51.000 --> 15:53.000
可以在京东和档案上

15:53.000 --> 15:55.000
购买对应的直述进行学习

15:56.000 --> 15:58.000
然后我们念一下slogan

15:58.000 --> 16:00.000
就是我们这本书的一个标语

16:00.000 --> 16:01.000
标语叫EAR

16:01.000 --> 16:04.000
像柴蘑菇一样轻松入门强化学习

16:04.000 --> 16:05.000
然后这个标语

16:05.000 --> 16:08.000
也代表了我们作者的编写这本书的一个目的

16:08.000 --> 16:10.000
就想让大家学习强化学习的时候

16:10.000 --> 16:11.000
更加轻松

16:11.000 --> 16:14.000
不要像当时作者学习强化学习那样

16:14.000 --> 16:15.000
有很多的困难

16:15.000 --> 16:19.000
然后最后祝大家都能够轻松入门强化学习

16:21.000 --> 16:23.000
然后学习强化学习的过程都很顺利

