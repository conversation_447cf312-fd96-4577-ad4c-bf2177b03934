# aima-notes
## 人工智能：现代方法（第4版）笔记

<a href="url"><img src="https://github.com/datawhalechina/aima-notes/blob/main/images/%E4%BA%BA%E5%B7%A5%E6%99%BA%E8%83%BD%E7%8E%B0%E4%BB%A3%E6%96%B9%E6%B3%95.png" height="280" width="280" ></a>

## 项目背景
《人工智能：现代方法》是一本人工智能大百科全书，自1995年第一版上市以来畅销至今，被全球135个国家的1500多所学校、国内32所高校采用为教材，可谓是全球最权威、最经典的人工智能畅销教材，第四版是近十年重要更新版本。<br>
作者为AI领域的两位大牛：斯图尔特•罗素（Stuart Russell），加利福尼亚大学伯克利分校计算机科学系教授；彼得•诺维格（Peter Norvig）谷歌公司研究总监，美国宇航局艾姆斯研究中心计算科学部负责人，美国艺术与科学院和加利福尼亚科学院的院士。<br>
全书试图全方位探索人工智能领域，书中内容涵盖逻辑、概率和连续数学，感知、推理、学习和行动，以及公平、信任、社会公益和安全。本次课程主要学习其中的绪论、智能体和应用搜索解决问题方法三部分内容。我们将共读《人工智能：现代方法》，梳理读书笔记与习题解读。

## 任务安排
本次课程只学习书本的前三章，如果对后续内容感兴趣，可以后续再自行学习

|   Task   |      内容     |   时间   |
|----------|:-------------:|:------:|
| Task01   |  第1章 绪论 | 1-16 —— 1-19 |
| Task02   |  第2章 智能体   | 1-19 —— 1-22 |
| Task03   |  3.1-3.3 问题求解智能体、问题示例、搜索算法 | 1-22 —— 1-26 |
| Task04   |  3.4 无信息搜索策略 | 1-26 —— 1-30 |
| Task05   |  3.5 有信息（启发式）搜索策略 | 1-30 —— 2-3 |
| Task06   |  3.6 启发式函数 | 2-3 —— 2-5 |
| Task07   |  总结与回顾 | 2-5 —— 2-6 |

## 拓展材料
书籍配套代码：https://github.com/aimacode

## 购书福利
Datawhale专属五折优惠<br>
平装版：https://u.jd.com/4dVt4LN<br>
精装版：https://u.jd.com/4CVwOPv<br>
