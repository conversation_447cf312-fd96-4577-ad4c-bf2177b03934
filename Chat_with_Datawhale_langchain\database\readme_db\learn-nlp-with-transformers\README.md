# 基于transformers的自然语言处理(NLP)入门
Natural Language Processing with transformers.
本项目面向的对象是：
- NLP初学者、transformer初学者
- 有一定的python、pytorch编程基础
- 对前沿的transformer模型感兴趣
- 了解和知道简单的深度学习模型

本项目的愿景是：

希望结合形象生动的原理讲解和多个动手实践项目，帮助初学者快速入门深度学习时代的NLP。

本项目的主要参考资料是：
- Huggingface/Transformers代码库
- 多个优秀的Transformer讲解和分享

项目成员：
- erenup([多多笔记](https://www.zhihu.com/people/nai-ping-46-76))，北京大学，负责人
- [张帆](https://github.com/zhangfanTJU)，Datawhale，天津大学，篇章4
- 张贤，哈尔滨工业大学，篇章2
- 李泺秋，浙江大学，篇章3
- 蔡杰，北京大学，篇章4
- hlzhang，麦吉尔大学，篇章4
- 台运鹏 篇章2
- 张红旭 篇章2

本项目总结和学习了多篇优秀文档和分享，在各个章节均有标注来源，如有侵权，请及时联系项目成员，谢谢。去[Github点完Star](https://github.com/datawhalechina/learn-nlp-with-transformers)再学习事半功倍哦😄，谢谢。

# 项目内容
## 篇章1-前言
* [1.0-本地阅读和代码运行环境配置.md](./篇章1-前言/1.0-本地阅读和代码运行环境配置.md)
* [1.1-Transformers在NLP中的兴起](./篇章1-前言/1.1-Transformers在NLP中的兴起.md)

## 篇章2-Transformer相关原理
* [2.1-图解attention](./篇章2-Transformer相关原理/2.1-图解attention.md)
* [2.2-图解transformer](./篇章2-Transformer相关原理/2.2-图解transformer.md)
* [2.2.1-Pytorch编写Transformer.md](./篇章2-Transformer相关原理/2.2.1-Pytorch编写Transformer.md)
* [2.2.2-Pytorch编写Transformer-选读.md](./篇章2-Transformer相关原理/2.2.1-Pytorch编写Transformer-选读.md)
* [2.3-图解BERT](./篇章2-Transformer相关原理/2.3-图解BERT.md)
* [2.4-图解GPT](./篇章2-Transformer相关原理/2.4-图解GPT.md)
* [2.5-篇章小测](./篇章2-Transformer相关原理/2.5-篇章小测.md)

## 篇章3-编写一个Transformer模型：BERT
* [3.1-如何实现一个BERT](./篇章3-编写一个Transformer模型：BERT/3.1-如何实现一个BERT.md)
* [3.2-如何应用一个BERT](./篇章3-编写一个Transformer模型：BERT/3.2-如何应用一个BERT.md)
* [3.3-篇章小测](./篇章3-编写一个Transformer模型：BERT/3.3-篇章小测.md)

## 篇章4-使用Transformers解决NLP任务
* [4.0-前言](./篇章4-使用Transformers解决NLP任务/4.0-前言.md)
* [4.1-文本分类](./篇章4-使用Transformers解决NLP任务/4.1-文本分类.md)
* [4.2-序列标注](./篇章4-使用Transformers解决NLP任务/4.2-序列标注.md)
* [4.3-问答任务-抽取式问答](./篇章4-使用Transformers解决NLP任务/4.3-问答任务-抽取式问答.md)
* [4.4-问答任务-多选问答](./篇章4-使用Transformers解决NLP任务/4.4-问答任务-多选问答.md)
* [4.5-生成任务-语言模型](./篇章4-使用Transformers解决NLP任务/4.5-生成任务-语言模型.md)
* [4.6-生成任务-机器翻译](./篇章4-使用Transformers解决NLP任务/4.6-生成任务-机器翻译.md)
* [4.7-生成任务-摘要生成](./篇章4-使用Transformers解决NLP任务/4.7-生成任务-摘要生成.md)
* [4.8-篇章小测](./篇章4-使用Transformers解决NLP任务/4.8-篇章小测.md)
