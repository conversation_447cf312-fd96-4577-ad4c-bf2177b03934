


第44期 Datawhale 组队学习活动马上就要开始啦！

- 12月07日（星期三），宣发，[Datawhale年末组队学习来了！](https://mp.weixin.qq.com/s/MHU-V5cOsbxJI2PKpWEkUg)。
- 12月10日（星期六），进入学习群、开营仪式。

本次组队学习的内容为：

1. 吃瓜教程——西瓜书+南瓜书
2. 周志华机器学习视频课
3. 可解释机器学习
4. OpenCV教程
5. Whalepaper推荐系统论文阅读
6. 大话设计模式



大家可以根据我们的开源内容进行自学，也可以加入我们的组队学习一起来学。


---
# 1. 吃瓜教程——西瓜书+南瓜书

- 航路开辟者：谢文睿、秦州
- 领航员：徐韵婉
- 航海士：谢文睿、秦州、学弱猹

## 基本信息

- 开源内容：https://linklearner.com/#/learn/detail/10
- 开源内容：https://github.com/datawhalechina/pumpkin-book
- B站视频：https://www.bilibili.com/video/BV1Mh411e7VU
- 内容属性：机器学习（理论）专题
- 课程编号：107
- 内容说明：周志华老师的“西瓜书”是机器学习经典入门教材，值得反复阅读，配合“南瓜书”从本科数学基础的视角进行讲解，一起打好基础！
- 定位人群：有本科数学基础（高等数学、线性代数、概率论与数理统计）的同学。
- 特别提示：吃瓜教程学习形式为直播+社群答疑，旨在促进更多人动手学习。

## 任务安排

- 学习周期：18天

![](https://files.mdnice.com/user/3656/8d107723-c7d9-4eac-960f-d926d7440f62.png)








---

# 2. 周志华机器学习视频课


- 航路开辟者：谢文睿
- 领航员：小年
- 航海士：崔腾松、周辉池

## 基本信息

- 开源内容：https://www.xuetangx.com/course/nju0802bt/14363483
- 内容属性：机器学习（理论）专题
- 课程编号：202
- 内容说明：《机器学习初步》课程覆盖机器学习的入门基石内容，课程主讲人周志华教授是领域专家，周志华老师的西瓜书正是本课程的教材。
- 定位人群：学习者需具备一定的微积分、线性代数、概率统计、最优化方法的基础知识。
- 特别提示：学习形式为学习视频+社群答疑，旨在促进更多人动手学习。

## 任务安排

- 学习周期：18天

![](https://files.mdnice.com/user/3656/ef146864-cf36-452e-b129-c157a074cc8d.png)

---

# 3. 可解释机器学习


- 航路开辟者：张子豪
- 领航员：吕豪杰
- 航海士：田文博、张燕、黄俊程

## 基本信息

- 开源内容：https://github.com/TommyZihao/zihao_course/tree/main/XAI
- 内容属性：机器学习基础公开课
- 视频地址：https://space.bilibili.com/1900783
- 内容说明：算法导论、综述、经典论文精读、代码实战、前沿讲座
- 定位人群：机器学习、深度学习、计算机视觉初学者



## 任务安排

- 学习周期：15天

![](https://files.mdnice.com/user/3656/8b7baad6-9b83-4ded-b4ac-596a03878cc4.png)


---

# 4. OpenCV教程

- 航路开辟者：谢文昕
- 领航员：赖南英
- 航海士：陈伟峰，曹志宾，张露雨

## 基本信息

- 开源内容：https://vxr.xet.tech/s/49dV3o
- 内容属性：计算机视觉
- 内容说明：OpenCV和计算机视觉的基础相关知识点学习。
- 定位人群：计算机视觉初学者。
- 特别提示：学习形式为课程材料学习。


## 任务安排

- 学习周期：14天

![](https://files.mdnice.com/user/3656/003653c7-dd15-46af-bd9b-f83e78a7c665.png)

---
# 5. Whalepaper推荐系统论文阅读

- 航路开辟者：王凯，芙蕖
- 领航员：邱雯
- 航海士：郭棉昇

## 基本信息

- 开源内容：https://oljacoephk.feishu.cn/docx/He0GdxFr5o9hVwx7Vzjc3M4JnKd
- 内容说明：Whalepaper入门课程，让初学者更好的学习推荐相关paper，掌握推荐系统基本方法。
- 特别提示：学习形式为课程材料学习+直播答疑


## 任务安排

- 学习周期：16天

![](https://files.mdnice.com/user/3656/f16d09f7-5ac8-462e-a009-7f6877053d6b.png)

---
# 6. 大话设计模式

- 航路开辟者：长琴、肖桐、碧涵、锐锋、鸿飞
- 领航员：六一
- 航海士：长琴、肖桐、碧涵、锐锋、鸿飞

## 基本信息

- 开源内容：https://github.com/datawhalechina/sweetalk-design-pattern
- 内容说明：基于《大话设计模式》学习设计原则和设计模式。
- 定位人群：不了解设计模式，想提升自己代码水平的工程师。
- 特别提示：需要有一定代码经验。


## 任务安排

- 学习周期：20天

![](https://files.mdnice.com/user/3656/c98ea039-df25-498e-8f49-e9982f8eb273.png)





---
# 7. 具体规则


## 打卡方法介绍

- 任务打卡入口：开源学习小程序 -> 组队学习 -> 自己所学的课程 -> 任务。
- 在任务打卡的“任务学习总结或心得”处写入自己的学习总结【必填】。
- 在CSDN、简书等平台撰写技术博客【鼓励】。
- 在任务打卡的“打卡链接”处填写自己原创内容的技术博客网址【选填】。
- 我们会邀请船长一起查看填写技术博客网址的博文并给予反馈。
- 打卡完成后，可以进入打卡圈，查看所有学习者的打卡内容，支持点赞，评论。

具体操作见：[如何使用开源学习小程序？](https://mp.weixin.qq.com/s/iPmzb72Yk0mhIA2NYezXDg)

![](https://img-blog.csdnimg.cn/2021090615022279.png)

## 打卡要求介绍

- 打卡内容包括但不限于对理论知识的理解、扩展、代码实现、公式推导等等，也可直播分享自己的学习过程。不需要复制粘贴教程原文。
- 如果笔记中需要引用教程内容或其他重要资料，希望注明出处，并附上来源链接，避免版权纠纷。
- 打卡截止时间是每次任务规定时间的第二天凌晨3：00，需要在睡觉前打卡，不是第二天起床后。
- 符合任意一条不规范打卡规定（字数少于50、复制粘贴教程内容、与本任务无关内容）或没有及时打卡的同学，将由领航员“抱出学习群”，监督金不予退还，并关闭后续打卡。



---
# 8. 组队学习的那些事


组队学习之余，来读读组队学习中发生的故事！

- [钱振：助教的那些事](https://mp.weixin.qq.com/s/kXlf8JhYov_Hj_kmecIj4A)
- [红星：队长的那些事](https://mp.weixin.qq.com/s/JxETJgtP52u3Uo0phEiLzw)
- [代硕：学习者的那些事](https://mp.weixin.qq.com/s/ePCwtAN8KZ8u_CETjCJ_3Q)
- [田瑶：学习者的那些事](https://mp.weixin.qq.com/s/veIWCkDrs3PDTpKhier3Ig)
- [红星：学习者的那些事](https://mp.weixin.qq.com/s/1IRfnjUhvNSaA7OW7LPPLQ)
- [姚行志：助教的那些事](https://mp.weixin.qq.com/s/Iyn6lox81740F8owUCPhdA)
- [姚昱君：助教的那些事](https://mp.weixin.qq.com/s/JF-fpFs80mDTzwYuQDusyg)
- [陈长沙：助教的那些事](https://mp.weixin.qq.com/s/styOhIM9M4IelXiyhTTHtA)
- [王彦淳：助教的那些事](https://mp.weixin.qq.com/s/UvONxUK1PlM0EemFIaHUlA)
- [肖明远：队长的那些事](https://mp.weixin.qq.com/s/brLXDT8ydVBNZ7YWLwKsXg)
- [王岳泽：学习者的那些事](https://mp.weixin.qq.com/s/OLRsHrVEYwJZ0eZ-tRVA_A)
- [范宸尧：学习者的那些事](https://mp.weixin.qq.com/s/kWnX7DYo7CFXQMpg1O3vJQ)
- [张海腾：学习者的那些事](https://mp.weixin.qq.com/s/TEvxkQCCw1PK1wzEHY2fNw)
- [迟语寒：学习者的那些事](https://mp.weixin.qq.com/s/-qGZuitg1G2EGRLv_s6J7Q)
- [邓林权：学习者的那些事](https://mp.weixin.qq.com/s/IgnqG1HEktZB7Y3XVMYIUA)
- [罗如意：课程设计者的那些事](https://mp.weixin.qq.com/s/-KPilWfdCIRzmaiy7KmSzA)


希望参与活动的学习者，来读读组队学习的注意事项！

- [学习者参考手册](https://mp.weixin.qq.com/s/wXSCQ9fcit7gL0fwAwbtCQ)
- [什么是组队学习的大航海模型？](https://mp.weixin.qq.com/s/D2SedoGaGYOfzsaXa2BIww)
- [如何进行作业的评审？](https://mp.weixin.qq.com/s/qnxokuoNLz62HwfJwY2SEQ)
- [如何使用开源学习小程序？](https://mp.weixin.qq.com/s/iPmzb72Yk0mhIA2NYezXDg)
- [如何提高国内访问 GitHub 的速度？](https://mp.weixin.qq.com/s/sHQ0yjqYNgEb1Bw_X0BxZg)



