# BlueWhale

## 课程介绍

BlueWhale设计课程是datawhale项目开发团队，基于datawhale社区成员学习交流、发布资料、分享笔记等实际需求，基于开源项目BlueWhale二次开发的网站开发设计课程。


## 基本信息

- 贡献人员：王晓亮、何锋丽、张少波、谢文昕、张梁
- 学习周期：16天
- 学习形式：自学 + 实操 + 交流
- 人群定位：有一定编程基础的同学，有实际开发经验更佳
- 难度系数：中


## 学习目标

- 熟悉datawhale项目开发流程
- 掌握REST前后端分离理念及OpenAPI文档编写
- 掌握Django后端开发技术架构
- 掌握Vue前端开发
- 掌握前后台端对接

## 任务安排

### Task00：环境搭建和初步了解（2天）

- 组队、修改群昵称
- 熟悉打卡规则
- 熟悉REST风格系统
- 熟悉OpenAPI规范
- 了解Django框架
- 了解Vue.js框架
- 独立完成数据库安装
- 独立完成代码运营

### Task01：熟悉后端代码结构及OpenAPI文档编写（2天）

- 后端代码目录结构
- 后端RESTful API URL定义
- 查看已实现的接口及内容
- 使用[swagger-editor](https://github.com/swagger-api/swagger-editor)编辑接口文档并补充遗漏的接口

### Task02：熟悉datawhale需求及编写新API文档（2天）

- 熟悉用户及权限管理需求
- 设计用户及权限管理相关RESTful API
- 补充[openapi.yaml](./openapi.yaml)并添加用户及权限管理相关入口
- 熟悉赛事管理需求
- 设计赛事管理相关RESTful API
- 补充[openapi.yaml](./openapi.yaml)并添加赛事管理相关入口

### Task03：熟悉首页需求并使用Vue实现首页功能（2天）

- 前端代码目录结构
- `vue-router`简介
- `vuex`状态管理
- 熟悉`vuetify` material design组件库并使用
- 基于交互图实现首页功能

### Task04：开发用户管理后端及前端（4天）

- [后端]修改已有Model并添加用户属性，同步数据表
- [后端]实现对应序列化类及View
- [后端]创建URL与View的映射
- [前端]创建用户列表页及用户详情页
- [前端]创建用户列表路由及用户详情路由
- 线上环境部署及集成测试

### Task05：开发赛事管理后端及前端（4天）

- [后端]新建赛事相关Model并初始化数据表
- [后端]实现对应序列化类及View
- [后端]创建URL与View的映射
- [前端]创建赛事列表页及赛事编辑页面
- [前端]创建赛事列表路由及赛事编辑路由
- 线上环境部署及集成测试

#### 参与贡献

1.  Fork 本仓库
2.  新建 Feat_xxx 分支
3.  提交代码
4.  新建 Pull Request


#### 其他说明

1.  xxxx
2.  xxxx
3.  xxxx
