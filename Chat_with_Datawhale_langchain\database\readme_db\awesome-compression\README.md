# Awesome Compression


## 项目简介

&emsp;&emsp;随着ChatGPT的出圈，大语言模型层出不穷，并展现出非凡的能力，可以有效地解决各种问题。然而，这些模型通常需要大量的计算资源和内存，导致运行时资源消耗较高，限制了其在某些场景下的应用，让很多研究者望而却步。本项目使用通俗易懂的语言介绍模型的剪枝、量化、知识蒸馏等压缩方法，让更多的小白能更快了解到模型压缩技术。

## 项目意义

&emsp;&emsp;目前网上关于模型压缩的相关资料比较驳杂，初学者很难找到一个简单优质的的中文入门教程来学习。本项目借鉴[MIT 6.5940 TinyML and Efficient Deep Learning Computing](https://hanlab.mit.edu/courses/2023-fall-65940)，提供模型压缩的入门教程，降低模型压缩的学习门槛。在教程中，您将了解不同的压缩方法，通过实践和示例，学习如何应用这些方法来压缩深度学习模型，以满足实际应用需求。


## 项目受众

&emsp;&emsp;本项目适合以下学习者：

- 深度学习研究人员；
- 嵌入式系统和移动应用开发者；
- 对AI硬件加速和部署感兴趣的开发者；
- 对模型压缩技术感兴趣的学生群体。

## 项目亮点

- 提供通俗易懂的理论内容来科普模型压缩技术；
- 提供实践代码，结合实际场景帮助学习者更好地理解理论内容。

### 目录

- 第1章 引言
- 第2章 深度学习基础
- 第3章 模型剪枝
- 第4章 模型量化
- 第5章 神经网络架构搜索
- 第6章 知识蒸馏
- 第7章 项目实践

## 课程知识思维导图


## Roadmap

- Step 1: 搭建内容框架并确认各章节负责人（1个月）；
- Step 2: 对章节内容进行撰写（3个月）；
- Step 3: 对整体内容进行修订与完善（1个月）。


## 参与贡献

- 如果你想参与到项目中来欢迎查看项目的 [Issue]() 查看没有被分配的任务。
- 如果你发现了一些问题，欢迎在 [Issue]() 中进行反馈🐛。
- 如果你对本项目感兴趣想要参与进来可以通过 [Discussion]() 进行交流💬。

如果你对 Datawhale 很感兴趣并想要发起一个新的项目，欢迎查看 [Datawhale 贡献指南](https://github.com/datawhalechina/DOPMC#%E4%B8%BA-datawhale-%E5%81%9A%E5%87%BA%E8%B4%A1%E7%8C%AE)。

## 贡献者名单

| 姓名 | 职责 | 简介 |
| :----| :---- | :---- |
| [陈玉立](https://github.com/ironartisan) | 项目负责人 | Datawhale成员-北京邮电大学研究生 |
| [姜蔚蔚](https://jwwthu.github.io) | 第x章贡献者 | 北京邮电大学助理教授 |

## 环境安装
### Node.js版本

Node v16

### 安装docsify
```shell
npm i docsify-cli -g
```


### 启动docsify
```shell
docsify serve ./docs
```

## 关注我们

<div align=center>
<p>扫描下方二维码关注公众号：Datawhale</p>
<img src="https://raw.githubusercontent.com/datawhalechina/pumpkin-book/master/res/qrcode.jpeg" width = "180" height = "180">
</div>

## LICENSE

<a rel="license" href="http://creativecommons.org/licenses/by-nc-sa/4.0/"><img alt="知识共享许可协议" style="border-width:0" src="https://img.shields.io/badge/license-CC%20BY--NC--SA%204.0-lightgrey" /></a><br />本作品采用<a rel="license" href="http://creativecommons.org/licenses/by-nc-sa/4.0/">知识共享署名-非商业性使用-相同方式共享 4.0 国际许可协议</a>进行许可。


