# 聪明办法学 Python 第二版

[![Binder](https://mybinder.org/badge_logo.svg)](https://nbviewer.org/github/datawhalechina/learn-python-the-smart-way-v2/tree/main/slides/)

## 课程简介

**课程主页**：[https://datawhalechina.github.io/learn-python-the-smart-way-v2/](https://datawhalechina.github.io/learn-python-the-smart-way-v2/)

**聪明办法学 Python 第二版**是 Datawhale 基于[第一版](https://github.com/datawhalechina/learn-python-the-smart-way)教程的一次大幅更新。我们尝试在教程中融入更多**计算机科学**与**人工智能**相关的内容，制作“面向人工智能的 Python 专项教程”。

我们的课程简称为 ***P2S***，有两个含义：

1. ***Learn Python The Smart Way V2***，“聪明办法学 Python 第二版”的缩写。
2. ***Prepare To Be Smart***， 我们希望同学们学习这个教程后能**学习到聪明的办法，从容的迈入人工智能的后续学习**。

## 课程目录

- 基础 (Chap0 - Chap6 制作完成)：
  - Chapter 0 安装 Installation
  - Chapter 1 启航 Getting Started
  - Chapter 2 数据类型和操作 Data Types and Operators
  - Chapter 3 变量与函数 Variables and Functions
  - Chapter 4 条件 Conditionals
  - Chapter 5 循环 Loop
  - Chapter 6 字符串 Strings
- 进阶 (Chap7 - Chap12 制作中，待定)：

<details>
<ul>
    <li>Chapter 7 Lists and Tuples</li>
    <li>Chapter 8 Sets</li>
    <li>Chapter 9 Dictionaries</li>
    <li>Chapter 10 Object Oriented Programming Part 1</li>
    <li>Chapter 11 Object Oriented Programming Part 2</li>
    <li>...</li>
</details>

## 教学团队

教学团队成员来自 Datawhale 高校联盟，详见：[课程背景](https://datawhalechina.github.io/learn-python-the-smart-way-v2/Index/background/)、[教学团队](https://datawhalechina.github.io/learn-python-the-smart-way-v2/Team/team/)

我们会依据贡献程度与活跃程度动态更新列表，并维护一份全体贡献者列表，如果你也想加入我们，请阅读[参与贡献](https://datawhalechina.github.io/learn-python-the-smart-way-v2/Contribute/contribute/)

## 镜像仓库

[![Epsilon Luoo/本仓库仅同步请访问Github_learn-python-the-smart-way-v2](https://gitee.com/anine09/learn-python-the-smart-way-v2/widgets/widget_card.svg?colors=ffffff,1e252b,323d47,455059,d7deea,99a0ae)](https://gitee.com/anine09/learn-python-the-smart-way-v2)

## 关注我们

<div align=center><img src="resources/datawhale_wechat_qrcode.jpeg" width = "250" height = "270" alt="Datawhale是一个专注AI领域的开源组织，以“for the learner，和学习者一起成长”为愿景，构建对学习者最有价值的开源学习社区。关注我们，一起学习成长。"></div>

## LICENSE

<a rel="license" href="http://creativecommons.org/licenses/by-nc-sa/4.0/"><img alt="知识共享许可协议" style="border-width:0" src="https://img.shields.io/badge/license-CC%20BY--NC--SA%204.0-lightgrey" /></a><br />本作品采用<a rel="license" href="http://creativecommons.org/licenses/by-nc-sa/4.0/">知识共享署名-非商业性使用-相同方式共享 4.0 国际许可协议</a>进行许可。
