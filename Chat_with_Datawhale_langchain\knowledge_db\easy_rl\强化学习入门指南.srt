1
00:00:01,000 --> 00:00:03,000
B站的小伙伴们好

2
00:00:03,000 --> 00:00:07,000
我是蘑菇书一语二语二强化学习教程的作者之一王奇

3
00:00:07,000 --> 00:00:11,000
今天来有给大家带来一个强化学习的入门指南

4
00:00:13,000 --> 00:00:18,000
本次入门指南基于蘑菇书一语二语二强化学习教程

5
00:00:18,000 --> 00:00:20,000
本书的作者目前都是Dell会员成员

6
00:00:20,000 --> 00:00:22,000
也都是数学在读

7
00:00:22,000 --> 00:00:24,000
下面去介绍每个作者

8
00:00:26,000 --> 00:00:27,000
我是王奇

9
00:00:27,000 --> 00:00:29,000
目前就留于中国科研院大学

10
00:00:29,000 --> 00:00:33,000
引用方向是深度学习、静态视觉以及数据挖掘

11
00:00:36,000 --> 00:00:38,000
杨玉云目前就读于清华大学

12
00:00:38,000 --> 00:00:39,000
他的引用方向为

13
00:00:39,000 --> 00:00:43,000
时空数据挖掘、智能冲砍系统以及深度学习

14
00:00:44,000 --> 00:00:46,000
张记目前就读于北京大学

15
00:00:46,000 --> 00:00:49,000
他的引用方向为强化学习记忆人

16
00:00:52,000 --> 00:00:54,000
接下来开始正式的分享

17
00:00:54,000 --> 00:00:55,000
本次分享分为三部分

18
00:00:55,000 --> 00:00:56,000
第一部分

19
00:00:56,000 --> 00:00:57,000
为什么要学强化学习

20
00:00:57,000 --> 00:00:58,000
第二部分

21
00:00:58,000 --> 00:01:00,000
为什么要用本书来学

22
00:01:00,000 --> 00:01:01,000
第三部分

23
00:01:01,000 --> 00:01:02,000
这本书怎么学最高效

24
00:01:04,000 --> 00:01:06,000
首先讲一下为什么要学强化学习

25
00:01:09,000 --> 00:01:11,000
我们先聊一下强化学习的基本概念

26
00:01:11,000 --> 00:01:15,000
强化学习用来学习如何做出一系列好的决策

27
00:01:15,000 --> 00:01:17,000
而人工智能的基本挑战是

28
00:01:17,000 --> 00:01:20,000
学习在不确定的情况下做出好的决策

29
00:01:20,000 --> 00:01:21,000
这边我举个例子

30
00:01:21,000 --> 00:01:23,000
比如你想让一个小孩学会走路

31
00:01:23,000 --> 00:01:26,000
他就需要通过不断尝试来发现

32
00:01:26,000 --> 00:01:27,000
怎么走比较好

33
00:01:27,000 --> 00:01:28,000
怎么走比较快

34
00:01:31,000 --> 00:01:34,000
强化学习的交互过程可以通过这张图来表示

35
00:01:34,000 --> 00:01:37,000
强化学习由智能体和环境两部分组成

36
00:01:37,000 --> 00:01:38,000
在强化学习过程中

37
00:01:38,000 --> 00:01:40,000
智能体与环境一直在交互

38
00:01:41,000 --> 00:01:44,000
智能体在环境中获取某个状态后

39
00:01:44,000 --> 00:01:46,000
它会利用刚刚的状态输出一个动作

40
00:01:46,000 --> 00:01:48,000
这个动作也被称为决策

41
00:01:48,000 --> 00:01:52,000
然后这个动作会被环境中执行

42
00:01:52,000 --> 00:01:55,000
环境会根据智能体采取的动作

43
00:01:55,000 --> 00:01:57,000
来输出下一个状态

44
00:01:57,000 --> 00:02:00,000
以及当前这个动作带来的奖励

45
00:02:00,000 --> 00:02:01,000
整体它的目的呢

46
00:02:01,000 --> 00:02:04,000
就是尽可能的多的

47
00:02:05,000 --> 00:02:08,000
尽可能多的在环境中获取奖励

48
00:02:15,000 --> 00:02:16,000
强化学习的应用非常广泛

49
00:02:16,000 --> 00:02:19,000
比如说我们可以使用强化学习来玩游戏

50
00:02:19,000 --> 00:02:21,000
玩游戏的话可以玩这种电子游戏

51
00:02:21,000 --> 00:02:23,000
也可以玩这种围棋游戏

52
00:02:24,000 --> 00:02:26,000
围棋游戏中比较出名的一个

53
00:02:26,000 --> 00:02:29,000
强化学习的算法就是AlphaGo

54
00:02:31,000 --> 00:02:32,000
此外我们可以使用强化学习

55
00:02:32,000 --> 00:02:33,000
来控制机器人

56
00:02:33,000 --> 00:02:35,000
以及来实现助力交通

57
00:02:35,000 --> 00:02:37,000
另外还可以使用强化学习

58
00:02:37,000 --> 00:02:39,000
来更好地给我们做推进

59
00:02:41,000 --> 00:02:42,000
接下来就到第二部分

60
00:02:42,000 --> 00:02:46,000
也就是为什么要使用本书来学习强化学习

61
00:02:46,000 --> 00:02:47,000
这部分其实也是讲

62
00:02:47,000 --> 00:02:50,000
这个蘑菇书它出版的一些故事

63
00:02:51,000 --> 00:02:53,000
当时我在学习强化学习的时候

64
00:02:53,000 --> 00:02:55,000
搜集了一些资料

65
00:02:55,000 --> 00:02:58,000
然后我发现这些资料

66
00:02:58,000 --> 00:02:59,000
都有点灰色难懂

67
00:02:59,000 --> 00:03:02,000
并不是那么容易地上手

68
00:03:03,000 --> 00:03:05,000
于是我开始到网上

69
00:03:06,000 --> 00:03:08,000
搜索一些公开课来学习

70
00:03:08,000 --> 00:03:10,000
首先我搜索到的是

71
00:03:10,000 --> 00:03:12,000
李宏毅老师的一些公开课

72
00:03:12,000 --> 00:03:15,000
很多人就是入门深度学习

73
00:03:15,000 --> 00:03:16,000
和基学低门课

74
00:03:16,000 --> 00:03:18,000
其实就是李宏毅老师的课

75
00:03:18,000 --> 00:03:20,000
李宏毅老师的课

76
00:03:22,000 --> 00:03:25,000
李宏毅老师的基础学习和深度学习公开课

77
00:03:25,000 --> 00:03:27,000
在编上有很高的播放量

78
00:03:28,000 --> 00:03:31,000
于是我搜索了李宏毅老师的强化学习课

79
00:03:31,000 --> 00:03:33,000
这门课叫顺强化学习

80
00:03:33,000 --> 00:03:35,000
这门课跟以往的李宏毅老师的

81
00:03:35,000 --> 00:03:37,000
课人的风格都是一样的

82
00:03:37,000 --> 00:03:39,000
就是非常的生动有趣

83
00:03:39,000 --> 00:03:41,000
李宏毅老师经常会用

84
00:03:41,000 --> 00:03:44,000
玩亚达利游戏的例子来讲强化学习

85
00:03:44,000 --> 00:03:46,000
这样强化学你就变得通透易懂

86
00:03:46,000 --> 00:03:48,000
然后就很多人都会

87
00:03:48,000 --> 00:03:51,000
把这门课作为自己的强化学习入门课

88
00:03:51,000 --> 00:03:54,000
这边我们念出了一些

89
00:03:54,000 --> 00:03:57,000
就是观众的一些好评

90
00:03:57,000 --> 00:04:00,000
比如说这边观众说

91
00:04:00,000 --> 00:04:02,000
国内这个讲得最好了

92
00:04:02,000 --> 00:04:04,000
然后李宏毅老师讲得真厉害

93
00:04:04,000 --> 00:04:07,000
还有这个评论比较搞笑

94
00:04:07,000 --> 00:04:09,000
这个视频每天晚上都有

95
00:04:09,000 --> 00:04:11,000
几个到十几个人在看

96
00:04:11,000 --> 00:04:13,000
因此我们可以发现

97
00:04:13,000 --> 00:04:19,000
这门课还是非常受人欢迎的

98
00:04:22,000 --> 00:04:24,000
后来我发现李宏毅老师的课

99
00:04:24,000 --> 00:04:25,000
不是那么全面

100
00:04:25,000 --> 00:04:27,000
他的课主要集中于讲解

101
00:04:27,000 --> 00:04:28,000
迅速强化学算法

102
00:04:28,000 --> 00:04:30,000
而忽略了全球强化学算法

103
00:04:30,000 --> 00:04:33,000
以及一些比较前沿的强化学算法

104
00:04:33,000 --> 00:04:34,000
因此我开始

105
00:04:34,000 --> 00:04:36,000
到网上搜索其他的强化学公开课

106
00:04:36,000 --> 00:04:38,000
当时是搜索到了

107
00:04:38,000 --> 00:04:40,000
周博恩老师的强化学纲要

108
00:04:40,000 --> 00:04:42,000
周博恩老师是

109
00:04:42,000 --> 00:04:45,000
人文智能底层的一个顶尖学者

110
00:04:45,000 --> 00:04:48,000
他在人文智能底层会议

111
00:04:48,000 --> 00:04:50,000
亲爱发表了五十余篇学术论文

112
00:04:50,000 --> 00:04:53,000
然后论文的作用量超过一万次

113
00:04:53,000 --> 00:04:57,000
周博恩老师的这门强化学纲要课

114
00:04:57,000 --> 00:04:58,000
就是理论严谨

115
00:04:58,000 --> 00:04:59,000
然后内容丰富

116
00:04:59,000 --> 00:05:01,000
全面的教训介绍了强化学领域

117
00:05:01,000 --> 00:05:03,000
并有相关代码实践

118
00:05:08,000 --> 00:05:09,000
在学习完

119
00:05:09,000 --> 00:05:14,000
女红衣和周博恩老师两门强化学课以后

120
00:05:14,000 --> 00:05:16,000
我发现还是有一点不足

121
00:05:16,000 --> 00:05:18,000
就是代码的实践还偏少

122
00:05:18,000 --> 00:05:22,000
于是我在网上搜索其他的强化学课

123
00:05:22,000 --> 00:05:24,000
当时就搜索了李克强老师的一个

124
00:05:24,000 --> 00:05:28,000
叫试验冠军代理丛林实践强化学习这门课

125
00:05:28,000 --> 00:05:30,000
这门课有个特别突出的优点

126
00:05:30,000 --> 00:05:32,000
就是实战性强

127
00:05:32,000 --> 00:05:35,000
突兵课只能会使用单单的代码来讲解强化学习

128
00:05:35,000 --> 00:05:37,000
这边也有很多好评

129
00:05:40,000 --> 00:05:44,000
有了这三门课以后

130
00:05:44,000 --> 00:05:47,000
我发现这三门课

131
00:05:47,000 --> 00:05:50,000
通过这些公开课来进学的话

132
00:05:50,000 --> 00:05:52,000
有些优点但也有缺点

133
00:05:52,000 --> 00:05:55,000
优点的话就是这些课都是非常经典的一些公开课

134
00:05:55,000 --> 00:05:58,000
然后他们这些课的这些老师

135
00:05:58,000 --> 00:06:00,000
也都是人文领域的一些大流

136
00:06:00,000 --> 00:06:03,000
然后他们的播放量非常高

137
00:06:03,000 --> 00:06:05,000
就比较受欢迎

138
00:06:05,000 --> 00:06:07,000
但是这些课呢也会存在一些问题

139
00:06:07,000 --> 00:06:09,000
就用这些公开课来进学的课会存在一些问题

140
00:06:09,000 --> 00:06:11,000
比如说它不便于实际的查询

141
00:06:11,000 --> 00:06:14,000
然后对于一些重点的知识点它会缺乏一些讲解

142
00:06:14,000 --> 00:06:17,000
此外这些知识点比较分散

143
00:06:17,000 --> 00:06:19,000
就每个老师他讲的知识点各有侧重

144
00:06:19,000 --> 00:06:23,000
此外就是视频的扩弱化比较严重

145
00:06:23,000 --> 00:06:27,000
因此基于这三门公开课

146
00:06:27,000 --> 00:06:31,000
就是我和杨亦远和江静

147
00:06:31,000 --> 00:06:33,000
三个DDR成员

148
00:06:33,000 --> 00:06:36,000
对这些三门课的内容进行整合补充优化

149
00:06:36,000 --> 00:06:39,000
然后写出了这本教材叫

150
00:06:39,000 --> 00:06:41,000
Easy IR 强化域教程

151
00:06:45,000 --> 00:06:47,000
这本教程当时是在

152
00:06:47,000 --> 00:06:49,000
一开始是在Github上发布的

153
00:06:49,000 --> 00:06:51,000
然后Github上呢当时发完以后

154
00:06:51,000 --> 00:06:52,000
就是有很多好评

155
00:06:52,000 --> 00:06:53,000
比如这边

156
00:06:53,000 --> 00:06:55,000
比如说有人的同学就说

157
00:06:55,000 --> 00:06:56,000
昨天非常棒

158
00:06:56,000 --> 00:06:57,000
超越有帮助感谢博主

159
00:06:57,000 --> 00:06:59,000
然后等等等等

160
00:06:59,000 --> 00:07:03,000
然后目前这本书对应的那个Github仓库的

161
00:07:03,000 --> 00:07:06,000
start数已经达48k

162
00:07:06,000 --> 00:07:08,000
然后

163
00:07:08,000 --> 00:07:10,000
它的增长就是它的

164
00:07:10,000 --> 00:07:12,000
start数的一个增长曲线

165
00:07:12,000 --> 00:07:14,000
也是曾经上升的一个态势

166
00:07:15,000 --> 00:07:18,000
后来我们在DataWare举办了一次组队学习

167
00:07:18,000 --> 00:07:20,000
然后组队学习的那个教材呢

168
00:07:20,000 --> 00:07:22,000
就是以我们这本书的

169
00:07:22,000 --> 00:07:25,000
Github的那个在线版

170
00:07:25,000 --> 00:07:27,000
作为一个教材

171
00:07:29,000 --> 00:07:31,000
在组队学习结束以后

172
00:07:31,000 --> 00:07:33,000
然后也是有很多的

173
00:07:33,000 --> 00:07:36,000
来自世界各地的学习的小伙伴

174
00:07:36,000 --> 00:07:37,000
给出好评

175
00:07:37,000 --> 00:07:38,000
比如这边我就

176
00:07:38,000 --> 00:07:40,000
列出了一个小伙伴的好评

177
00:07:42,000 --> 00:07:44,000
然后因为在Github上有很多的读者

178
00:07:44,000 --> 00:07:46,000
会通过issue来给我们反馈

179
00:07:46,000 --> 00:07:49,000
比如说他们会觉得我们来的地方

180
00:07:49,000 --> 00:07:51,000
翻译来电会有些问题

181
00:07:51,000 --> 00:07:52,000
他们会直接列出来

182
00:07:52,000 --> 00:07:55,000
然后我们也会给他们及时的反馈

183
00:07:55,000 --> 00:07:58,000
然后对我们的教程进行优化

184
00:07:59,000 --> 00:08:02,000
后来我们初入以后

185
00:08:02,000 --> 00:08:07,000
有幸得到了七位强行学习领域

186
00:08:07,000 --> 00:08:08,000
大开的亲笔推荐

187
00:08:08,000 --> 00:08:10,000
比如说台湾大学的宁欧英老师

188
00:08:10,000 --> 00:08:13,000
周伯伦老师以及李克强老师

189
00:08:13,000 --> 00:08:17,000
还有比如清华的宁生梦老师

190
00:08:17,000 --> 00:08:19,000
汪峥老师张伟老师

191
00:08:19,000 --> 00:08:21,000
胡玉静老师等等

192
00:08:22,000 --> 00:08:23,000
然后这边我们列出了

193
00:08:23,000 --> 00:08:25,000
宁欧英老师的一个推荐语

194
00:08:25,000 --> 00:08:27,000
因为我觉得宁欧英老师的

195
00:08:27,000 --> 00:08:30,000
推荐语非常的有趣

196
00:08:30,000 --> 00:08:31,000
比如他说

197
00:08:31,000 --> 00:08:32,000
他当时看到我们这个

198
00:08:32,000 --> 00:08:34,000
伊丽艾尔这本书的时候

199
00:08:34,000 --> 00:08:35,000
他第一个想法是

200
00:08:35,000 --> 00:08:37,000
这成员把强化学习的知识整理得真好

201
00:08:37,000 --> 00:08:39,000
不仅有理论说明

202
00:08:39,000 --> 00:08:40,000
还加上了诚实实力

203
00:08:40,000 --> 00:08:43,000
同学们以后可以直接阅读这份教程

204
00:08:43,000 --> 00:08:45,000
这样我以后上课

205
00:08:45,000 --> 00:08:48,000
就不用再讲强化学习的部分了

206
00:08:53,000 --> 00:08:54,000
可以发现宁欧英对我们

207
00:08:54,000 --> 00:08:56,000
这本书还是挺认可的

208
00:08:56,000 --> 00:08:57,000
然后再来讲一个问题

209
00:08:57,000 --> 00:08:59,000
这本书为什么叫蘑菇书呢

210
00:08:59,000 --> 00:09:01,000
难道是因为作者都是吃货

211
00:09:01,000 --> 00:09:04,000
之前有一本西瓜书

212
00:09:04,000 --> 00:09:05,000
还有一本南瓜书

213
00:09:05,000 --> 00:09:07,000
然后作为一个吃货的话

214
00:09:07,000 --> 00:09:09,000
还有一个传承一下叫蘑菇书

215
00:09:09,000 --> 00:09:12,000
就吃完西瓜南瓜再来热蘑菇

216
00:09:12,000 --> 00:09:14,000
但其实并不是

217
00:09:14,000 --> 00:09:17,000
蘑菇书真的寓意是玛里奥

218
00:09:17,000 --> 00:09:19,000
大家如果玩过超级玛里奥游戏

219
00:09:19,000 --> 00:09:21,000
就知道玛里奥吃了蘑菇以后

220
00:09:21,000 --> 00:09:22,000
会变得更加强大

221
00:09:22,000 --> 00:09:25,000
然后我们也希望读者

222
00:09:26,000 --> 00:09:29,000
在吃下这个蘑菇书以后

223
00:09:29,000 --> 00:09:32,000
能够养有兴致的探索强化学习

224
00:09:32,000 --> 00:09:34,000
然后像玛里奥那样越强大

225
00:09:34,000 --> 00:09:38,000
然后进而在人工智能领域里面

226
00:09:38,000 --> 00:09:41,000
获得一些奇葩收获

227
00:09:43,000 --> 00:09:45,000
这边我们放出了一个小彩蛋

228
00:09:45,000 --> 00:09:48,000
就是我们的作者之一杨亦远

229
00:09:48,000 --> 00:09:50,000
跟宁欧英老师的一个意外接触

230
00:09:51,000 --> 00:09:54,000
当时杨亦远在参加一个顶位的时候

231
00:09:54,000 --> 00:09:59,000
发现宁欧英老师也在参会者名单里面

232
00:09:59,000 --> 00:10:01,000
他给宁欧英老师发了封邮件

233
00:10:01,000 --> 00:10:04,000
介绍了我们当时GitHub的开源教程

234
00:10:05,000 --> 00:10:08,000
然后非常惊喜的就是

235
00:10:08,000 --> 00:10:11,000
宁欧英老师很快地给出了回信

236
00:10:11,000 --> 00:10:15,000
然后也是对我们这个教程给出了好评

237
00:10:18,000 --> 00:10:21,000
这边列出了蘑菇书获得的一些荣誉

238
00:10:21,000 --> 00:10:23,000
比如说人民邮件出版社的季度好书

239
00:10:24,000 --> 00:10:26,000
然后日读期期间

240
00:10:26,000 --> 00:10:29,000
当当计算机期中网的第一名

241
00:10:29,000 --> 00:10:31,000
然后上市时间以后

242
00:10:31,000 --> 00:10:34,000
获得的就是京东人工智能榜的第一名

243
00:10:34,000 --> 00:10:37,000
全网的推文阅读量破十万

244
00:10:37,000 --> 00:10:39,000
然后目前清华大学李淑木教授

245
00:10:39,000 --> 00:10:42,000
小米NLP首位科学家王斌老师

246
00:10:42,000 --> 00:10:45,000
以及百度高级研发工程师李克强老师

247
00:10:45,000 --> 00:10:51,000
还有一些等20家的一个大咖公众号

248
00:10:51,000 --> 00:10:54,000
以及微博大V社区他们进行的一个转发推荐

249
00:10:54,000 --> 00:10:58,000
然后这个也被推荐到华为电影大学的一个保定图书馆

250
00:10:59,000 --> 00:11:02,000
蘑菇书全书一共13章可以分两部分

251
00:11:02,000 --> 00:11:05,000
第一部分他介绍了强学的基础知识

252
00:11:05,000 --> 00:11:07,000
以及传统的强化学习算法

253
00:11:07,000 --> 00:11:09,000
第二部分他介绍了

254
00:11:09,000 --> 00:11:11,000
适用强化学习算法

255
00:11:11,000 --> 00:11:14,000
以及常见问题的解决方法

256
00:11:15,000 --> 00:11:18,000
咱们这本书还有一些突出的特点

257
00:11:18,000 --> 00:11:20,000
比如第一点

258
00:11:20,000 --> 00:11:23,000
我们会利用一些简单生动的例子来解释强化学概念

259
00:11:23,000 --> 00:11:26,000
比如说我们可以用这个玩视频游戏

260
00:11:26,000 --> 00:11:28,000
以及下围棋的例子

261
00:11:28,000 --> 00:11:31,000
来对强化学的一些基本概念做出解释

262
00:11:33,000 --> 00:11:35,000
然后还有一些其他特点

263
00:11:35,000 --> 00:11:38,000
比如说我们会对专业的一些公式

264
00:11:38,000 --> 00:11:40,000
进行详细的推导和分析

265
00:11:40,000 --> 00:11:42,000
这边我们以这个比尔曼方程的

266
00:11:42,000 --> 00:11:44,000
这个推导过程的例子

267
00:11:44,000 --> 00:11:46,000
我们会一步一步的把这个推导过程念出来

268
00:11:46,000 --> 00:11:47,000
不会跳步走

269
00:11:47,000 --> 00:11:49,000
然后此外呢

270
00:11:51,000 --> 00:11:52,000
我们还会对一些

271
00:11:52,000 --> 00:11:54,000
可能大家比较难以理解的地方

272
00:11:54,000 --> 00:11:56,000
我们会加入一些注解

273
00:11:56,000 --> 00:11:57,000
通过这些注解

274
00:11:57,000 --> 00:12:00,000
我们会让大家更容易理解一些相关的概念

275
00:12:03,000 --> 00:12:06,000
此外本书配有对应的一个观念词

276
00:12:06,000 --> 00:12:08,000
习题和面试题

277
00:12:08,000 --> 00:12:10,000
当读者读完一章以后

278
00:12:10,000 --> 00:12:13,000
大家可以通过观念词来快速的掌握重点

279
00:12:13,000 --> 00:12:16,000
然后一个通过习题和面试题

280
00:12:16,000 --> 00:12:18,000
来巩固对知识的理解

281
00:12:18,000 --> 00:12:20,000
然后这个习题和面试题

282
00:12:20,000 --> 00:12:22,000
也方便大家的一个程度补缺

283
00:12:22,000 --> 00:12:23,000
然后最后呢

284
00:12:23,000 --> 00:12:25,000
我们会有对应的代码实战

285
00:12:25,000 --> 00:12:27,000
大家学完这个理论以后

286
00:12:27,000 --> 00:12:30,000
还要通过动手来把这个代码进行

287
00:12:30,000 --> 00:12:32,000
来把这个算法进行一个实践

288
00:12:32,000 --> 00:12:35,000
就是大家把这个算法进行实践以后

289
00:12:35,000 --> 00:12:37,000
大家才算对这个算法

290
00:12:37,000 --> 00:12:39,000
有一个比较深入了解

291
00:12:41,000 --> 00:12:43,000
接下来到最后一部分

292
00:12:43,000 --> 00:12:45,000
叫这本书怎么学比较高效

293
00:12:46,000 --> 00:12:48,000
第一个

294
00:12:48,000 --> 00:12:49,000
当然你可以把这本书

295
00:12:49,000 --> 00:12:51,000
作为三门公开课的一个必要教材

296
00:12:51,000 --> 00:12:54,000
就是当你在看这三门课的时候

297
00:12:54,000 --> 00:12:58,000
如果你发现有哪些概念不是很清楚的情况下

298
00:12:58,000 --> 00:13:00,000
你是可以直接翻到

299
00:13:00,000 --> 00:13:02,000
本书对应的知识点进行学习

300
00:13:02,000 --> 00:13:05,000
当然本书也是完全图形于三门教材的

301
00:13:05,000 --> 00:13:08,000
大家可以直接预读本书进行学习

302
00:13:10,000 --> 00:13:12,000
本书在GitHub上面

303
00:13:12,000 --> 00:13:13,000
配有对应的代码

304
00:13:13,000 --> 00:13:15,000
这个代码也会适宜的更新

305
00:13:15,000 --> 00:13:19,000
大家如果只是想很快的

306
00:13:19,000 --> 00:13:21,000
把这个算法给应用上

307
00:13:21,000 --> 00:13:22,000
大家可以直接去GitHub上

308
00:13:22,000 --> 00:13:24,000
跑对应的代码

309
00:13:26,000 --> 00:13:31,000
此外本书还有相关的一个刊物和修订

310
00:13:31,000 --> 00:13:32,000
这些刊物和修订

311
00:13:32,000 --> 00:13:34,000
也会根据大家的反馈的一些意见

312
00:13:34,000 --> 00:13:36,000
进行一些适宜的更新

313
00:13:38,000 --> 00:13:40,000
然后这边也要讲一下

314
00:13:40,000 --> 00:13:43,000
本书它在GitHub有个叫PK版

315
00:13:43,000 --> 00:13:46,000
然后它还有这种纸质书的版本叫纸质版

316
00:13:46,000 --> 00:13:47,000
它们有什么区别呢

317
00:13:47,000 --> 00:13:50,000
就GitHub的那种PK版本是本书的一个初稿

318
00:13:50,000 --> 00:13:53,000
然后在本书的作者已经憋进来

319
00:13:53,000 --> 00:13:54,000
不断的修改中

320
00:13:55,000 --> 00:13:58,000
我们最后有了纸质版

321
00:13:59,000 --> 00:14:01,000
大家看到我们

322
00:14:01,000 --> 00:14:04,000
对这个PK版本里面有了大量的修订

323
00:14:04,000 --> 00:14:06,000
所以说纸质书的一个质量

324
00:14:06,000 --> 00:14:09,000
相对于PK版本是要高不少的

325
00:14:11,000 --> 00:14:13,000
讲到这里大家可能会有疑问

326
00:14:15,000 --> 00:14:18,000
可能是如果我目前我涉及的工作

327
00:14:18,000 --> 00:14:19,000
不涉及到强化学习

328
00:14:19,000 --> 00:14:21,000
那我还有必要学强化学习吗

329
00:14:23,000 --> 00:14:25,000
对于这个问题我想用乔布斯的观点

330
00:14:25,000 --> 00:14:26,000
叫黏生命的点

331
00:14:26,000 --> 00:14:27,000
或者叫应用相连

332
00:14:29,000 --> 00:14:31,000
这边就举一下乔布斯他本人的例子

333
00:14:32,000 --> 00:14:35,000
乔布斯当时在大学学了一门书法课

334
00:14:35,000 --> 00:14:38,000
这门课在很多人眼里都是没有用处的

335
00:14:38,000 --> 00:14:40,000
而乔布斯他本人也是出于兴趣

336
00:14:40,000 --> 00:14:41,000
才学这门课

337
00:14:44,000 --> 00:14:48,000
他也没有对这门课抱有很大的期望

338
00:14:48,000 --> 00:14:50,000
但是后来发现他这门课

339
00:14:50,000 --> 00:14:53,000
在设计苹果电脑字体的时候

340
00:14:53,000 --> 00:14:54,000
取到了极大作用

341
00:14:55,000 --> 00:14:58,000
如果乔布斯当时没有学这门课的话

342
00:14:58,000 --> 00:14:59,000
大家可能就看不到

343
00:14:59,000 --> 00:15:05,000
苹果电脑中这些优美丰富赏意悦目的字体

344
00:15:06,000 --> 00:15:10,000
而对强化学习来说

345
00:15:10,000 --> 00:15:13,000
强化学习这种应用非常广泛的技术

346
00:15:13,000 --> 00:15:17,000
即使现在可能跟你的工作没有很大关联

347
00:15:17,000 --> 00:15:18,000
但说不定某一天

348
00:15:18,000 --> 00:15:21,000
可能你的工作就要涉及到相关的

349
00:15:21,000 --> 00:15:22,000
涉及到强化学习

350
00:15:22,000 --> 00:15:24,000
这时候如果你在之前

351
00:15:24,000 --> 00:15:27,000
对强化学习有一个基本了解

352
00:15:27,000 --> 00:15:28,000
或者说入门的强化学习

353
00:15:28,000 --> 00:15:31,000
这时候可能对你的工作

354
00:15:32,000 --> 00:15:35,000
会起到一个意想不到的助理

355
00:15:39,000 --> 00:15:42,000
最后我们念出了蘑菇书

356
00:15:42,000 --> 00:15:45,000
在京东以及档案的一个购买的链接

357
00:15:45,000 --> 00:15:46,000
以及对应的二维码

358
00:15:47,000 --> 00:15:49,000
然后大家可以在这两个

359
00:15:51,000 --> 00:15:53,000
可以在京东和档案上

360
00:15:53,000 --> 00:15:55,000
购买对应的直述进行学习

361
00:15:56,000 --> 00:15:58,000
然后我们念一下slogan

362
00:15:58,000 --> 00:16:00,000
就是我们这本书的一个标语

363
00:16:00,000 --> 00:16:01,000
标语叫EAR

364
00:16:01,000 --> 00:16:04,000
像柴蘑菇一样轻松入门强化学习

365
00:16:04,000 --> 00:16:05,000
然后这个标语

366
00:16:05,000 --> 00:16:08,000
也代表了我们作者的编写这本书的一个目的

367
00:16:08,000 --> 00:16:10,000
就想让大家学习强化学习的时候

368
00:16:10,000 --> 00:16:11,000
更加轻松

369
00:16:11,000 --> 00:16:14,000
不要像当时作者学习强化学习那样

370
00:16:14,000 --> 00:16:15,000
有很多的困难

371
00:16:15,000 --> 00:16:19,000
然后最后祝大家都能够轻松入门强化学习

372
00:16:21,000 --> 00:16:23,000
然后学习强化学习的过程都很顺利

