# datawhale-operational-research
###### Datawhale运筹小组

在线阅读 https://datawhalechina.github.io/undingable-optimization/

## 项目初衷

近些年，以机器学习为代表的人工智能技术逐渐被大家认识并在很多方面得到普及，深度学习技术在学术界和工业界取得了广泛的成功，受到高度重视，并掀起新一轮的人工智能热潮。运筹学作为一个看似古老，但近期兴起的学科，主要由最优化、图论、组合优化...很多内容构成，最优化作为很重要的一部分内容，其中的优化求解器大家都熟练地使用着，但是对于其中真正的原理，又有多少人真的了解和关心呢。这里我们就会带着大家认真细致得学习一遍运筹学相关的内容。

本课程讲授和讨论运筹学的基本方法，主要内容有博弈论，线性规划，非线性规划，复杂性理论，组合优化，以及随机过程，马尔可夫决策过程，排队论和库存论等。关于最优化的算法理论和建模，主要内容有最优化基础，包括无约束最优性理论、对偶理论、约束最优性理论，和无约束优化算法，包括梯度类算法、牛顿类算法、拟牛顿类算法，以及约束优化算法，包括罚函数等。

通过本课程的学习，希望能够掌握运筹学和最优化算法的理论和算法实现，提高人工智能技术进行科学研究与应用开发的能力，其中关于代码的部分使用python或者matlab。

学习的先修要求：了解一些基本的高数知识和数理统计知识.



## 内容设置

### （第一期）Datawhale运筹-优化与深度学习篇

1. 优化与深度学习（Yang）
1. 优化算法概述（Yang）
2. 梯度下降与随机梯度下降（振东）
3. 小批量随机梯度下降（小豪）
4. 动量法（柯琴）
5. AdaGrad（建国）
8. Adam（Yang）
8. Nesterov加速（Yang）



### （第二期）Datawhale运筹-优化基础算法篇

1. 无约束优化基础(Yang)
2. 线性搜索方法(小豪)
3. 信赖域方法( )
4. 共轭梯度法(琴)
5. 拟牛顿法(lj)
6. 约束优化基础(振东)
7. 罚函数法(lj)
8. 增广拉格朗日法(建国)



### Datawhale运筹-线性规划篇

1. 凸分析基础
2. 单纯形算法
3. 对偶理论
4. 应用案例



### Datawhale运筹-图论篇

1. 图的基本概念；最短路问题；
2. 树及其基本性质；生成树与最小生成树；图的中心与中位点；图的矩阵表示；
3. 割点和割边；连通度和边连通度；2-连通图的性质；
4. 匹配与最大匹配；完美匹配；二部图的匹配；二部图中最大匹配和最大权匹配的算法；
5. Euler图；中国邮递员问题；Hamilton图；旅行商问题。



### Datawhale运筹-组合优化篇

1. 动态规划
2. 分支定界
3. 贪婪算法
4. 整数规划
5. 在线算法



### Datawhale运筹-矩阵分析

[在线阅读网页](https://liu-yang-maker.github.io/matrix-analysis/)



### 专题：

#### Datawhale运筹-复杂性理论篇

1. 历史
2. 图灵机
3. 复杂性分类
4. 复杂性判定与证明

#### Datawhale运筹-随机过程篇（包括排队论）

1. 基础知识
2. 泊松过程
3. 离散时间Markov 
4. 连续时间Markov
5. 排队论
6. 随机游走与鞅的简介



#### Datawhale运筹-库存论篇



#### Datawhale运筹-博弈论篇

1. 零和博弈
2. 非零和博弈
3. 稳定匹配
4. 公平分配
5. 网络博弈
6. 合作博弈
7. 贝叶斯博弈



#### Datawhale运筹-马尔可夫决策过程篇

1. 预备知识（关于随机过程）
2. 排队论
3. MDP绪论
4. 有限阶段MDP
5. MDP折扣模型
6. MDP平均模型



#### Datawhale运筹-应用篇

...



## 人员安排(排名不分先后)

| 成员   | 个人简介                                      | 个人主页                                                     |
| ------ | --------------------------------------------- | ------------------------------------------------------------ |
| 刘洋   | datawhale成员，中国科学院数学与系统科学研究院 | [知乎主页](https://www.zhihu.com/people/ming-ren-19-34)<br />公众号：鸣也的小屋 |
| 管柯琴 | datawhale成员，厦门大学管理学院               | [个人主页](https://www.yangsuoly.com/)                       |
| 刘斯豪 | 广东工业大学数学与统计学院                    | 公众号：Lau Chaos                                            |
| 黄建国 | 上海科技大学信息学院                          | [知乎](https://www.zhihu.com/people/ding-jian-cai-niao-91)   |
| 李振东 | 上海大学钱伟长学院                            | [知乎主页](https://www.zhihu.com/people/li-zhen-dong-50-19) <br />[个人主页](https://dmax13.ltd) |
| 林景   | 武汉理工大学理学院数学系                      | [CSDN](https://blog.csdn.net/linjing_zyq)<br />[Github](https://github.com/linjing-lab) |
| 胡锐锋 | datawhale成员，国网大数据平台工程师           | [Github](https://github.com/Relph1119)                       |

#### 其他



## 关注我们

<div align=center>
<p>扫描下方二维码关注公众号：Datawhale</p>
<img src="https://raw.githubusercontent.com/datawhalechina/pumpkin-book/master/res/qrcode.jpeg" width = "180" height = "180">
</div>

## LICENSE

<a rel="license" href="http://creativecommons.org/licenses/by-nc-sa/4.0/"><img alt="知识共享许可协议" style="border-width:0" src="https://img.shields.io/badge/license-CC%20BY--NC--SA%204.0-lightgrey" /></a><br />本作品采用<a rel="license" href="http://creativecommons.org/licenses/by-nc-sa/4.0/">知识共享署名-非商业性使用-相同方式共享 4.0 国际许可协议</a>进行许可。
