

# Paper Collection of RL and Its Applications
This repo mainly collects paper of RL (Reinforcement Learning) and Its Applications, and also tools including datasets , envs and frameworks usually used in RL.

## Paper lists

[RL-basics](./RL-basics.md): basic papers of RL, if you want to learn RL, you must not miss these.

[MARL-basics](./MARL-basics.md):  basic papers of multi-agent reinforcement learning(MARL), if you want to learn RL, you must not miss these.

[RL4RS](): RL for recommendation systems

[RL4Game](./RL4Game.md): RL for game theory

[RL4Traffics]()

[RL4Policy-Diversity]()

[RL4DrugDiscovery](./RL4DrugDiscovery.md): Drug discovery is a challenging multi-objective optimization problem where multiple pharmaceutical objectives need to be satisfied. Recently, utilizing reinforcement learning to generate molecules with desired physicochemical properties such as solubility has been acknowledged as a promising strategy for drug design. 

[RL4QD](./RL4QD.md): Quality-Diversity methods are evolutionary based algorithms to return the collection contains several working solutions, and also deal with the exploration-exploitation trade-off. 

[RL4IL](./RL4IL.md): RL for imitation learning

[RL4Robot](./RL4Robot.md): RL for Robot. According to the classification of robot types, papers of the same category are arranged in chronological order, and papers that have been physically verified are preferred.

[RL4IIoT](./RL4IIoT.md): With the technological breakthrough of 5G, more and more Internet of Things (IoT) technologies are being used in industrial scenarios. Industrial IoT (IIoT), which refers to the integrating industrial manufacturing systems and the Internet of Things (IoT), has received accumulating attention. These emerging IIoT applications and have higher requirements on quality of experience (QoE) which cannot be easily satisfied by heuristic algorithms. Recently, some research use RL to learn algorithms for IIoT tasks through exploiting the potential feature of the IIoT environment,

[LFHF](./LFHF.md): Learn From Human Feedback，ChatGPT的核心技术之一。

## Tools

[Tools](./Tools.md):  including datasets , envs and frameworks



## Main Contributors

<table border="0">
  <tbody>
    <tr align="center" >
      <td>
         <a href="https://github.com/cr-bh"><img width="70" height="70" src="https://github.com/cr-bh.png?s=40" alt="pic"></a><br>
         <a href="https://github.com/cr-bh">Ariel Chen</a>
         <p> MARL-basics <br> THU </p>
      </td>
      <td>
         <a href="https://github.com/L3Y1Q2"><img width="70" height="70" src="https://github.com/L3Y1Q2.png?s=40" alt="pic"></a><br>
         <a href="https://github.com/L3Y1Q2">Yongqi Li</a>
         <p> RL4Robotics&MRS <br> SUSTech </p>
      </td>
      <td>
         <a href="https://github.com/curryliu30"><img width="70" height="70" src="https://github.com/curryliu30.png?s=40" alt="pic"></a><br>
         <a href="https://github.com/curryliu30">Erlong Liu</a>
         <p> QD&ERL <br> NJU </p>
      </td>
      <td>
         <a href="https://github.com/clorisqiu1"><img width="70" height="70" src="https://github.com/clorisqiu1.png?s=40" alt="pic"></a><br>
         <a href="https://github.com/clorisqiu1">Wen Qiu</a>
         <p> DQN&PG&Exploration <br> KIT </p>
      </td>
      <td>
         <a href="https://github.com/shikejianalan"><img width="70" height="70" src="https://github.com/shikejianalan.png?s=40" alt="pic"></a><br>
         <a href="https://github.com/shikejianalan">Kejian Shi</a>
         <p> RL&Robotics <br> IC </p>
      </td>
      <td>
         <a href="https://github.com/JohnJim0816"><img width="70" height="70" src="https://github.com/JohnJim0816.png?s=40" alt="pic"></a><br>
         <a href="https://github.com/JohnJim0816">John Jim</a>
         <p> offline RL <br> PKU </p>
      </td>
    </tr>
  </tbody>
</table>
