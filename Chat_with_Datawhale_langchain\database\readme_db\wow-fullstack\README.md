# wow-全栈

好的教程可以一路运行。
我们要做一个能够一路拷贝代码运行的全栈开发教程。集成了许多优秀代码片段，在开发过程中可以随时复制。同时搭配了一个练手项目，一个用来做时间管理的项目。前端采用ts+vue3+vite架构，后端采用fastapi架构。开源项目和开源教程可以打配合。开源项目里用到的知识写入开源教程。开源教程所用到的例子来源于开源项目。互相印证。

# 使用方法

- TS：进入tutorial文件夹，再进入[TypeScript](https://github.com/datawhalechina/wow-fullstack/tree/main/tutorial/TypeScript)文件夹，自行阅读各个章节。文档都是Markdown，点开后可直接阅读。代码的输出结果已经写在相应代码下方了。需要什么代码尽管拷贝。
- Vue3：进入tutorial文件夹，再进入[Vue3](https://github.com/datawhalechina/wow-fullstack/tree/main/tutorial/Vue3)文件夹，自行阅读各个章节。文档都是Markdown，点开后可直接阅读。代码的输出结果已经写在相应代码下方了。需要什么代码尽管拷贝。
- FastAPI：进入tutorial文件夹，再进入[FastAPI](https://github.com/datawhalechina/wow-fullstack/tree/main/tutorial/FastAPI)文件夹。文档都是jupyter notebook，需要把ipynb文件下载到本地去运行。直接运行每个代码格子就可以。


## 参与贡献

- 如果你想参与到项目中来欢迎查看项目的 [Issue]() 查看没有被分配的任务。
- 如果你发现了一些问题，欢迎在 [Issue]() 中进行反馈🐛。
- 如果你对本项目感兴趣想要参与进来可以通过 [Discussion]() 进行交流💬。

如果你对 Datawhale 很感兴趣并想要发起一个新的项目，欢迎查看 [Datawhale 贡献指南](https://github.com/datawhalechina/DOPMC#%E4%B8%BA-datawhale-%E5%81%9A%E5%87%BA%E8%B4%A1%E7%8C%AE)。

## 贡献者名单

需要开发的产品和对应的大写首字母有：  
1.时间管理课程 Course  
2.技术教程 Tutorial  
3.用户使用指南 Guide  
4.开发文档 Docs  
5.前端代码 Frontend  
6.后端代码 Backend  

开发者需要根据自己的技能和兴趣选定一个开发产品作为主产品。  
目前的开发人员安排：  
Course：[Susan](https://github.com/Susan2048)  
Tutorial：[Hoshino-wind](https://github.com/Hoshino-wind)、[KMnO4-zx](https://github.com/KMnO4-zx)  
Guide：张某  、[WuXiaoMing](https://xlight5.github.io)  
Docs：[Kailigithub](https://github.com/Kailigithub)  、[WuXiaoMing](https://xlight5.github.io)  
Frontend：[lime](https://github.com/yyhhxx)  、[WuXiaoMing](https://xlight5.github.io)  
Backend：[Tom.Yang](https://github.com/7n8fail)、[lish](https://github.com/kevin-light)、[wu](https://github.com/AIzealotwu)  、[WuXiaoMing](https://xlight5.github.io)


## 关注我们

<div align=center>
<p>扫描下方二维码关注公众号：Datawhale</p>
<img src="https://raw.githubusercontent.com/datawhalechina/pumpkin-book/master/res/qrcode.jpeg" width = "180" height = "180">
</div>

## LICENSE

<a rel="license" href="http://creativecommons.org/licenses/by-nc-sa/4.0/"><img alt="知识共享许可协议" style="border-width:0" src="https://img.shields.io/badge/license-CC%20BY--NC--SA%204.0-lightgrey" /></a><br />本作品采用<a rel="license" href="http://creativecommons.org/licenses/by-nc-sa/4.0/">MIT</a>进行许可。

*注：默认使用CC 4.0协议，也可根据自身项目情况选用其他协议*
