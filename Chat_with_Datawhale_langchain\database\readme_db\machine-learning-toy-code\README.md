# [西瓜书代码实战](https://github.com/datawhalechina/machine-learning-toy-code)

本项目以西瓜书以及[南瓜书](https://datawhalechina.github.io/pumpkin-book/#/)为主要参考，其他资料为辅助，来进行常见机器学习代码的实战。主要特色为力求数码结合，即数学公式与相关代码的形神对应，能够帮助读者加深对公式的理解以及代码的熟练。

目前已完成基于sklearn包的相关用法撰写，基于numpy包实现算法部分正在筹备中...

## 算法进度

|   算法名称               | 相关材料 | 进度 | 备注 |
| :----------:            | :------: | :--: | ---- |
| 01-LinearRegression     |sklearn   |  OK  |      |
| 02-LogisticRegression   |sklearn   |  OK  |      |
| 03-DecisionTree         |sklearn   |  OK  |      |
| 04-MLP                  |sklearn   |   OK |      |
| 05-SVM                  |sklearn   |  OK  |      |
| 06-Bayes                |sklearn   |  OK  |      |
| 07-Random Forest        |sklearn   |  OK  |      |
| 08-AdaBoos              |sklearn   |  OK  |      |
| 09-K-means              |sklearn   | OK   |      |
| 10-kNN                  |sklearn   | OK   |      |
| 11-PCA                  |sklearn   | OK   |      |
| 12-HMM                  |hmmlearn  | OK   |      |
| 13-Visualization        |sklearn   | OK   |      |

## 算法项目实战

学习完了西瓜书，手动实现相关的算法后，接下来就是到了实战的环节，datawhale开源的数据竞赛项目给大家施展自己coding的平台

- [数据挖掘实践（二手车价格预测）](https://github.com/datawhalechina/team-learning-data-mining/tree/master/SecondHandCarPriceForecast)

- [数据挖掘实践（金融风控）](https://github.com/datawhalechina/team-learning-data-mining/tree/master/FinancialRiskControl)
- [数据挖掘实践（心跳信号分类）](https://github.com/datawhalechina/team-learning-data-mining/tree/master/HeartbeatClassification)


## 贡献者

<table border="0">
  <tbody>
    <tr align="center" >
      <td>
         <a href="https://github.com/JohnJim0816">
            <img width="70" height="70" src="https://github.com/JohnJim0816.png?s=40" alt="pic">
         </a><br>
            <a href="https://github.com/JohnJim0816">John Jim</a>
            <p>算法实战<br> 北京大学</p>
      </td>
      <td>
         <a href="https://github.com/muxiaoxiong"><img width="70" height="70" src="https://github.com/muxiaoxiong.png?s=40" alt="pic"></a><br>
         <a href="https://github.com/muxiaoxiong">牧小熊</a>
         <p>chap 13<br> 华中农业大学</p>
      </td>
      <td>
         <a href="https://github.com/leungkafai"><img width="70" height="70" src="https://github.com/leungkafai.png?s=40" alt="pic"></a><br>
         <a href="https://github.com/leungkafai">梁家晖</a>
         <p>chap 1-4<br> 广州城市理工学院</p>
      </td>
      <td>
         <a href="https://github.com/zhangruoxuan"><img width="70" height="70" src="https://github.com/zhangruoxuan.png?s=40" alt="pic"></a><br>
         <a href="https://github.com/zhangruoxuan">张若萱</a>
         <p>chap 5-8<br> 天津理工大学</p>
      </td>
      <td>
         <a href="https://github.com/yueqianhaobo"><img width="70" height="70" src="https://github.com/yueqianhaobo.png?s=40" alt="pic"></a><br>
         <a href="https://github.com/yueqianhaobo">孙子涵</a>
         <p>chap 9-12<br> 太原理工大学</p>
      </td>
    </tr>
  </tbody>
</table>




## Refs


统计学习方法、西瓜书、机器学习实战
