# 聪明办法学Python

大家好，我是小雨姑娘。双非本科自学数据挖掘，曾两次获得数据挖掘比赛冠军，被选入2020数据挖掘竞赛十大开源贡献者，人工智能开源组织Datawhale成员，现于北美攻读计算机博士学位。创作本教程的初心是提供一个更快捷有效的 Python 入门途径，让大家在最短时间内补充需要的知识技能，从而更快完成自己的目标。

**学习完本教程你可以收获什么？**

1. 尝试使用Python完成项目解决问题的能力。
2. 对必备Python知识点的掌握。
3. 对经典理论与算法的了解。

**本教程适合哪类同学学习？**

1. 想要从0开始系统学习 Python 的同学。
2. 需要在最短时间内学习 Python 解决当下问题的同学。
3. 对 Python 感兴趣想要了解的同学。

**本教程不适合哪类同学学习？**

1. 希望精进 Python 高阶用法的同学。
2. 希望全面学习掌握 Python 知识点的同学。

*在开发本教程的同时，作者也在构思一套有上述需求同学适合的教程。*

**本教程相对市面其他材料的特点？**

1. 简明。专注 Python 编程的核心能力与技术，不涉及冷门/非必要相关知识。
2. 系统知识体系。从无到有一点点构建知识体系，拒绝琐碎知识点的堆叠。
3. 注重实践。通过代码讲解理论，辅以实例加以巩固。

柏拉图曾经说过：“良好的开端等于成功的一半”。大家选择本教程作为建起万丈高楼的地基，对于我而言便是莫大的荣幸。衷心希望大家在本教程陪伴下迈向未来的同时，也能享受学习过程中带来的乐趣。

## 如何使用这套教程？
1. 在 Github 网页版直接打开 ipynb 文件按顺序查看 (推荐)
2. 本地下载安装 jupyter notebook 运行 ipynb 文件，安装使用方法可参考这篇 [博客](https://www.jianshu.com/p/91365f343585/)
3. 在 [和鲸社区](https://www.heywhale.com) 创建项目，上传 .ipynb 文件可在线运行
4. 在 [Google Colab](https://www.colab.google.com) 上传 .ipynb 在线运行 （需要科学上网）

## 联系方式
知乎 [@小雨姑娘](https://www.zhihu.com/people/xuechuanyu)

Email: <EMAIL>

## 关注我们
<div align=center><img src="https://raw.githubusercontent.com/datawhalechina/pumpkin-book/master/res/qrcode.jpeg" width = "250" height = "270" alt="Datawhale是一个专注AI领域的开源组织，以“for the learner，和学习者一起成长”为愿景，构建对学习者最有价值的开源学习社区。关注我们，一起学习成长。"></div>

## LICENSE
<a rel="license" href="http://creativecommons.org/licenses/by-nc-sa/4.0/"><img alt="知识共享许可协议" style="border-width:0" src="https://img.shields.io/badge/license-CC%20BY--NC--SA%204.0-lightgrey" /></a><br />本作品采用<a rel="license" href="http://creativecommons.org/licenses/by-nc-sa/4.0/">知识共享署名-非商业性使用-相同方式共享 4.0 国际许可协议</a>进行许可。
