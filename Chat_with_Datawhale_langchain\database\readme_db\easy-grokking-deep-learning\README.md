# Easy-Grokking-Deep-Learning

---

## 项目介绍

### 项目简介

Easy-Grokking-Deep-Learning，意为轻松摸索深度学习，英文缩写可简称为EGDL。
本项目基于《Grokking Deep Learning》原书和清华出版社译版《深度学习图解》为主要内容，结合其他实用概念、常用知识以及通用常识和图表等素材进行解读与扩展，旨在使得读者和入门者能够更加轻松地对于本书的内容进行阅读吸收，从而对于深度学习及其相关概念有一个更清晰的认识，也可作为其他进阶知识内容做一个基础衔接铺垫。



### 立项理由

发起本项目的理由如下：

- 1.《Grokking Deep Leaning》出版时间在2018年，其译版《深度学习图解》出版于2020年，考虑到近年来AI领域的发展，因此希望在本项目将相关性的进展与研究内容作为一个扩展补充。
- 2.《深度学习图解》与原书中的图像均为静态图像，本项目希望借助开源项目的优势，将深度学习的图解过程转换制作成动态的gif图并附注相应的过程说明，这将有利于帮助读者思考和理解其概念。
- 3.本项目将发挥图解图表的优势，将众多图表概念和知识内容制作为相应的知识卡片，类似一张图教会一个概念这样的形式，后续可考虑单独将其整理为一个图册，或者整理为本项目的wiki，最终将其对外开源发布
- 4.本着开源共享的目的，本项目希望能够有更多的读者和开发者参与进行，通过Issue的形式进行提问，同时以PR的形式参与进来共建项目，从而扩大项目和开源的影响力。

### 项目受众

考虑到本项目的内容与难度，因此面向受众主要以深度学习入门者和想要深入学习的读者为主，同时也不限于其他高校学生和对本书感兴趣的企业开发者。



## 在线阅读地址

https://datawhalechina.github.io/easy-grokking-deep-learning/#/

## 进度安排

| 章节 | 内容 | 负责人 |
| ---- | ---- | ------ |
|      |      |        |
|      |      |        |
|      |      |        |
|      |      |        |
|      |      |        |
|      |      |        |
|      |      |        |
|      |      |        |
|      |      |        |
|      |      |        |
|      |      |        |
|      |      |        |
|      |      |        |



## 人员安排(持续招募中)

| 成员 | 个人简介             | 个人主页                              |
| ---- | -------------------- | ------------------------------------- |
| 林旭 | 某机器人厂算法架构师 | [isLinXu](https://github.com/isLinXu) |
|      |                      |                                       |
|      |                      |                                       |
|      |                      |                                       |



## 更新计划及进度安排(持续更新中)

| 内容           | 更新时间   | 负责人 |
| -------------- | ---------- | ------ |
| 项目初始化构建 | 2022.09.26 | 林旭   |
| 总体结构设计   | 2022.09.29 | 林旭   |
|                |            |        |
|                |            |        |



### ChangeLog

- ...
-  
- 总体结构设计  2022-09-29 [isLinXu](https://github.com/isLinXu)
- 项目初始化构建 2022-09-26 [isLinXu](https://github.com/isLinXu)



## 关注我们
<div align=center>
<p>扫描下方二维码关注公众号：Datawhale</p>
<img src="docs/_static/qrcode.jpeg" width = "180" height = "180">
</div>

&emsp;&emsp;Datawhale，一个专注于AI领域的学习圈子。初衷是for the learner，和学习者一起成长。目前加入学习社群的人数已经数千人，组织了机器学习，深度学习，数据分析，数据挖掘，爬虫，编程，统计学，Mysql，数据竞赛等多个领域的内容学习，微信搜索公众号Datawhale可以加入我们。

## LICENSE
<a rel="license" href="http://creativecommons.org/licenses/by-nc-sa/4.0/"><img alt="知识共享许可协议" style="border-width:0" src="https://img.shields.io/badge/license-CC%20BY--NC--SA%204.0-lightgrey" /></a><br />本作品采用<a rel="license" href="http://creativecommons.org/licenses/by-nc-sa/4.0/">知识共享署名-非商业性使用-相同方式共享 4.0 国际许可协议</a>进行许可。
