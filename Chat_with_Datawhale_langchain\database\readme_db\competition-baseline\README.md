# 数据竞赛Baseline & Topline分享

假如你是数据竞赛的初学者、爱好者，比赛的baseline不仅是比赛思路分享，同时也是一类数据问题的方法总结。本Repo想做的就是将收集并整理并分享各种比赛的baseline方案。

你可能会问为什么是baseline，而不是获胜者的代码分享？相比于获胜者的代码baseline代码都比较简单，容易整理和学习；其次baseline代码更加实用和简洁，适合入门学习。

## 数据竞赛

竞赛日历：http://coggle.club/

最新的竞赛信息和baseline推送，请关注：
- 竞赛公众号：[**Coggle数据科学**](https://t.zsxq.com/Eyn6EQr)
- 知乎专栏：[机器学习理论与数据竞赛实战](https://zhuanlan.zhihu.com/DataAI)

## 竞赛分享

每个比赛的详细分享请见[competition文件夹](https://github.com/datawhalechina/competition-baseline/tree/master/competition)；

如果本仓库访问速度慢，可以访问国内备份：https://gitee.com/coggle/competition-baseline

---

### 2023科大讯飞AI开发者大赛

2023科大讯飞AI开发者大赛正式启动，现开始报名。本次大赛在“36道应用赛+72道算法赛”的基础上，还开设了高校认知大模型场景创新赛和机器人设计赛，覆盖大模型、智能语音、计算机视觉、自然语言、先进智造、VR等人工智能热门技术，涵盖多行业领域，总奖金池累计超400万。

赛题报名链接：[https://challenge.xfyun.cn/?ch=vWxQGFU](https://challenge.xfyun.cn/?ch=vWxQGFU)

---

### 2022年人民网算法挑战赛

 为推进人工智能领域的学术交流、人才培养、技术发展，鼓励广大学生积极学习和研发符合我国主流价值观的优秀算法， 2022年11-12月举办“2022人民网人工智能算法大赛”，赛事由人民网股份有限公司主办，传播内容认知全国重点实验室承办。 

[http://data.sklccc.com/2022](http://data.sklccc.com/2022)

- 赛题一：对话生成，[https://aistudio.baidu.com/aistudio/projectdetail/5563450](https://aistudio.baidu.com/aistudio/projectdetail/5563450)
- 赛题二：微博话题识别，[https://aistudio.baidu.com/aistudio/projectdetail/5563970](https://aistudio.baidu.com/aistudio/projectdetail/5563970)
- 赛题三：微博流行度预测，[https://aistudio.baidu.com/aistudio/projectdetail/5567567](https://aistudio.baidu.com/aistudio/projectdetail/5567567)
- 赛题四：微博转发行为预测
- 赛题五：社交媒体机器人识别，[https://aistudio.baidu.com/aistudio/projectdetail/5563450](https://aistudio.baidu.com/aistudio/projectdetail/5563450)


---

### 2022年度 iFLYTEK A.I. 开发者大赛

2022年度 iFLYTEK A.I. 开发者大赛来了。本届大赛的总奖金池已升级到了超420万元，除此外还将进一步开放海量数据与核心技术，汇聚更多人工智能开发者，提供创孵平台，培育优质团队，给予扶持政策等。

本届大赛按照算法、应用、编程赛、虚拟形象选拔、辩论赛、创意集市创意赛等等方向设置众多赛道；覆盖了智能语音、视觉、自然语言、图文识别等AI热门技术；涵盖了元宇宙、遗址文化、生物与环保、医疗健康、智能家居、电商销售等众多领域。大赛地址：[https://challenge.xfyun.cn/?ch=ds22-dw-sq04](https://challenge.xfyun.cn/?ch=ds22-dw-sq04)


---
### AIWIN 秋季竞赛


- 赛题1- 手写体 OCR 识别竞赛

手写体 OCR 识别竞赛由交通银行命题，设立两个任务，其中任务一由第四范式提供开放数据集，特别针对金额和日期做识别，任务二要求在指定训练环境完成不可下载训练集的训练，增加了银行机构的文本内容。任务一适合新手，并配套学习营和特别的学习奖励。

比赛地址：http://ailab.aiwin.org.cn/competitions/65

baseline地址：https://aistudio.baidu.com/aistudio/projectdetail/2612313

- 赛题2- 心电图智能诊断竞赛

心电图智能诊断竞赛由数创医疗和复旦大学附属中山医院共同命题，设立两个任务，其中任务一诊断心电图的正常异常与否，任务二对10+种不同症状予以判断综合分类。任务一同步设有学习营和配套的学习奖励，欢迎新手参与。

比赛地址：http://ailab.aiwin.org.cn/competitions/64

baseline地址：https://aistudio.baidu.com/aistudio/projectdetail/2653802


---

### 2021阿里云供应链大赛——需求预测及单级库存优化

报名链接：https://tianchi.aliyun.com/competition/entrance/531934/introduction

比赛baseline:https://github.com/datawhalechina/competition-baseline/tree/master/competition/2021阿里云供应链大赛——需求预测及单级库存优化

---

### CCF BDCI 2021

baseline汇总：https://github.com/datawhalechina/competition-baseline/tree/master/competition/DataFountain-CCFBDI-2021

- 基于飞桨实现花样滑冰选手骨骼点动作识别，计算机视觉、姿态估计
- 千言-问题匹配鲁棒性评测，自然语言处理、文本匹配
- 基于MindSpore AI框架实现零售商品识别，计算机视觉、图像分类
- 产品评论观点提取，自然语言处理、实体抽取
- 个贷违约预测，结构化数据挖掘、金融风控
- 剧本角色情感识别，自然语言处理、实体抽取
- 基于UEBA的用户上网异常行为分析，结构化数据挖掘、异常检测
- POI名称生成，计算机视觉、OCR
- 客服通话文本摘要提取，自然语言处理、文本摘要
- 系统认证风险预测，结构化数据挖掘、风险检测
- 泛在感知数据关联融合计算，结构化数据挖掘、相似度计算
- openLooKeng跨域数据分析性能提升，数据仓储SQL优化
- 大规模金融仿真图数据中金融交易环路查询的设计与性能优化，金融交易图谱挖掘
- 基于BERT的大模型容量挑战赛，深度学习模型优化


---

### 华为DIGIX2021：全球校园AI算法精英大赛

报名链接：https://developer.huawei.com/consumer/cn/activity/digixActivity/digixdetail/201621215957378831?ha_source=gb_sf&ha_sourceId=89000073

- 赛题1：基于多目标多视图的用户留存周期预测
- 赛题2：基于多模型迁移预训练文章质量判别
- 赛题3：基于多目标优化的视频推荐
- 赛题4：基于多模态多语言的搜索排序
- 赛题5：小样本菜单识别

比赛baseline和学习资料：https://github.com/datawhalechina/competition-baseline/tree/master/competition/DIGIX2021

---

### 科大讯飞AI开发者大赛2021

- [中文问题相似度挑战赛](http://challenge.xfyun.cn/topic/info?type=chinese-question-similarity&ch=dw-sq-1), [学习资料](https://github.com/datawhalechina/competition-baseline/blob/master/competition/%E7%A7%91%E5%A4%A7%E8%AE%AF%E9%A3%9EAI%E5%BC%80%E5%8F%91%E8%80%85%E5%A4%A7%E8%B5%9B2021/%E4%B8%AD%E6%96%87%E9%97%AE%E9%A2%98%E7%9B%B8%E4%BC%BC%E5%BA%A6%E6%8C%91%E6%88%98%E8%B5%9B/bert-nsp-xunfei.ipynb)
- [线下商店销量预测挑战赛](http://challenge.xfyun.cn/topic/info?type=offline-store-sales-forecast&ch=dw-sq-1), [学习资料](https://github.com/datawhalechina/competition-baseline/tree/master/competition/%E7%A7%91%E5%A4%A7%E8%AE%AF%E9%A3%9EAI%E5%BC%80%E5%8F%91%E8%80%85%E5%A4%A7%E8%B5%9B2021/%E7%A7%91%E5%A4%A7%E8%AE%AF%E9%A3%9E%E5%95%86%E5%BA%97%E9%94%80%E9%87%8F%E9%A2%84%E6%B5%8B)
- [电商图像检索挑战赛](http://challenge.xfyun.cn/topic/info?type=e-commerce-image-retrieval&ch=dw-sq-1), [学习资料](https://github.com/datawhalechina/competition-baseline/tree/master/competition/%E7%A7%91%E5%A4%A7%E8%AE%AF%E9%A3%9EAI%E5%BC%80%E5%8F%91%E8%80%85%E5%A4%A7%E8%B5%9B2021/%E7%94%B5%E5%95%86%E5%9B%BE%E5%83%8F%E6%A3%80%E7%B4%A2%E6%8C%91%E6%88%98%E8%B5%9B)
- [人脸情绪识别挑战赛](http://challenge.xfyun.cn/topic/info?type=facial-emotion-recognition&ch=dw-sq-1), [学习资料](https://github.com/datawhalechina/competition-baseline/tree/master/competition/%E7%A7%91%E5%A4%A7%E8%AE%AF%E9%A3%9EAI%E5%BC%80%E5%8F%91%E8%80%85%E5%A4%A7%E8%B5%9B2021/%E4%BA%BA%E8%84%B8%E6%83%85%E7%BB%AA%E8%AF%86%E5%88%AB%E6%8C%91%E6%88%98%E8%B5%9B)
- [学术论文分类挑战赛](http://challenge.xfyun.cn/topic/info?type=academic-paper-classification&ch=dw-sq-1), [学习资料](https://github.com/datawhalechina/competition-baseline/tree/master/competition/%E7%A7%91%E5%A4%A7%E8%AE%AF%E9%A3%9EAI%E5%BC%80%E5%8F%91%E8%80%85%E5%A4%A7%E8%B5%9B2021/%E5%AD%A6%E6%9C%AF%E8%AE%BA%E6%96%87%E5%88%86%E7%B1%BB%E6%8C%91%E6%88%98%E8%B5%9B)
- [车辆贷款违约预测挑战赛](http://challenge.xfyun.cn/topic/info?type=car-loan&ch=dw-sq-1), [学习资料](https://github.com/datawhalechina/competition-baseline/tree/master/competition/%E7%A7%91%E5%A4%A7%E8%AE%AF%E9%A3%9EAI%E5%BC%80%E5%8F%91%E8%80%85%E5%A4%A7%E8%B5%9B2021/%E8%BD%A6%E8%BE%86%E8%B4%B7%E6%AC%BE%E8%BF%9D%E7%BA%A6%E9%A2%84%E6%B5%8B%E6%8C%91%E6%88%98%E8%B5%9B)
- [广告图片素材分类算法挑战赛](http://challenge.xfyun.cn/topic/info?type=ad-2021&ch=dw-sq-1)，[基础的分类思路](https://github.com/datawhalechina/competition-baseline/tree/master/competition/%E7%A7%91%E5%A4%A7%E8%AE%AF%E9%A3%9EAI%E5%BC%80%E5%8F%91%E8%80%85%E5%A4%A7%E8%B5%9B2021)
- [农作物生长情况识别挑战赛](http://challenge.xfyun.cn/topic/info?type=crop&ch=dw-sq-1), [keras](https://github.com/datawhalechina/competition-baseline/blob/master/competition/%E7%A7%91%E5%A4%A7%E8%AE%AF%E9%A3%9EAI%E5%BC%80%E5%8F%91%E8%80%85%E5%A4%A7%E8%B5%9B2021/%E4%B8%AD%E5%9B%BD%E5%86%9C%E4%B8%9A%E5%A4%A7%E5%AD%A6_%E5%86%9C%E4%BD%9C%E7%89%A9%E7%94%9F%E9%95%BF%E6%83%85%E5%86%B5%E8%AF%86%E5%88%AB%E6%8C%91%E6%88%98%E8%B5%9B.ipynb)
- [引导拍照挑战赛](http://challenge.xfyun.cn/topic/info?type=guide-photo&ch=dw-sq-1), [keras](https://github.com/datawhalechina/competition-baseline/blob/master/competition/%E7%A7%91%E5%A4%A7%E8%AE%AF%E9%A3%9EAI%E5%BC%80%E5%8F%91%E8%80%85%E5%A4%A7%E8%B5%9B2021/%E4%B8%AD%E5%9B%BD%E5%86%9C%E4%B8%9A%E5%A4%A7%E5%AD%A6_%E5%BC%95%E5%AF%BC%E6%8B%8D%E7%85%A7%E6%8C%91%E6%88%98%E8%B5%9B.ipynb)
- [脑部PETMR图像疾病预测挑战赛](http://challenge.xfyun.cn/topic/info?type=PET&ch=dw-sq-1), [keras](https://github.com/datawhalechina/competition-baseline/blob/master/competition/%E7%A7%91%E5%A4%A7%E8%AE%AF%E9%A3%9EAI%E5%BC%80%E5%8F%91%E8%80%85%E5%A4%A7%E8%B5%9B2021/%E5%AE%89%E5%BE%BD%E5%A4%A7%E5%AD%A6-%E8%84%91%E9%83%A8PETMR%E5%9B%BE%E5%83%8F%E7%96%BE%E7%97%85%E9%A2%84%E6%B5%8B%E6%8C%91%E6%88%98%E8%B5%9B.ipynb)
- [智能硬件语音控制的时频图分类挑战赛](http://challenge.xfyun.cn/topic/info?type=time-frequency&ch=dw-sq-1), [pytorch](https://github.com/datawhalechina/competition-baseline/blob/master/competition/%E7%A7%91%E5%A4%A7%E8%AE%AF%E9%A3%9EAI%E5%BC%80%E5%8F%91%E8%80%85%E5%A4%A7%E8%B5%9B2021/%E6%99%BA%E8%83%BD%E7%A1%AC%E4%BB%B6%E8%AF%AD%E9%9F%B3%E6%8E%A7%E5%88%B6%E7%9A%84%E6%97%B6%E9%A2%91%E5%9B%BE%E5%88%86%E7%B1%BB%E6%8C%91%E6%88%98%E8%B5%9B.ipynb)
- [基于用户画像的商品推荐挑战赛](http://challenge.xfyun.cn/topic/info?type=user-portrait&ch=dw-sq-1), [LSTM-0.6786](https://mp.weixin.qq.com/s/KDH_klH_74726S8gX4FEyQ)
- [蛋白质结构预测挑战赛算法](http://challenge.xfyun.cn/topic/info?type=protein&ch=dw-sq-1), [CNN-0.21](https://mp.weixin.qq.com/s/0oGWD0O5ARokxrAiW2T9uQ), [XGB基础代码](https://github.com/datawhalechina/competition-baseline/blob/master/competition/%E7%A7%91%E5%A4%A7%E8%AE%AF%E9%A3%9EAI%E5%BC%80%E5%8F%91%E8%80%85%E5%A4%A7%E8%B5%9B2021/%E4%B8%8A%E6%B5%B7%E6%B5%B7%E4%BA%8B%E5%A4%A7%E5%AD%A6_%E8%9B%8B%E7%99%BD%E8%B4%A8%E7%BB%93%E6%9E%84%E9%A2%84%E6%B5%8B%E8%B5%9B.ipynb)
- [环境空气质量评价挑战赛](http://challenge.xfyun.cn/topic/info?type=air-quality&ch=dw-sq-1), [LR-0.04385](https://mp.weixin.qq.com/s/9gZJ6ScwW1urRMc-6p6n5A)
- [猪只盘点挑战赛](http://challenge.xfyun.cn/topic/info?type=pig-check&ch=dw-sq-1), [预训练模型](https://github.com/datawhalechina/competition-baseline/blob/master/competition/%E7%A7%91%E5%A4%A7%E8%AE%AF%E9%A3%9EAI%E5%BC%80%E5%8F%91%E8%80%85%E5%A4%A7%E8%B5%9B2021/%E7%A7%91%E5%A4%A7%E8%AE%AF%E9%A3%9E%E8%82%A1%E4%BB%BD%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8_%E7%8C%AA%E5%8F%AA%E7%9B%98%E7%82%B9%E6%8C%91%E6%88%98%E8%B5%9B.ipynb)
- [新冠肺炎声音诊断挑战赛](http://challenge.xfyun.cn/topic/info?type=covid-19&ch=dw-sq-1)，[baseline 0.53532 Top20](https://github.com/zfs1998/data-science/blob/main/IFLYTEK/%E4%B8%AD%E5%9B%BD%E7%A7%91%E5%AD%A6%E6%8A%80%E6%9C%AF%E5%A4%A7%E5%AD%A6_%E6%96%B0%E5%86%A0%E8%82%BA%E7%82%8E%E5%A3%B0%E9%9F%B3%E8%AF%8A%E6%96%AD%E6%8C%91%E6%88%98%E8%B5%9B.ipynb)

---

### [腾讯广告算法大赛](https://algo.qq.com/)

#### 2021年度腾讯赛

本届从广告应用场景痛点出发，开设“视频广告秒级语义解析”和“多模态视频广告标签”两大赛道，兼具算法挑战性和商业应用价值。

- [TI-ONE 产品使用教程](https://cloud.tencent.com/developer/article/1807916)
- [如何使用 Notebook 功能完成赛事训练](https://cloud.tencent.com/developer/article/1807942?from=10680)
- [腾讯广告算法大赛参赛手册](https://algo-1256087447.cos.ap-nanjing.myqcloud.com/admin/20210430/cf48d04caf878b9d2773fda0e60ba8a8.pdf)
- [腾讯广告算法大赛FAQ](
https://docs.qq.com/doc/DV1hFUGpMV1l3eVdV)


#### 2020年度腾讯赛

本届以用户在广告系统中的交互行为作为输入来预测用户的人口统计学属性。

- [冠军分享](https://mp.weixin.qq.com/s/-lizDyP2y357plcG1M64TA)，[开源代码](https://github.com/guoday/Tencent2020_Rank1st
)
- [亚军分享](https://mp.weixin.qq.com/s/UWt4hZitX9bW1Y_RNyCJCg)
- [季军分享](https://mp.weixin.qq.com/s/rkhwLsCKTIDzUkjVIEj3LQ)
- [第5名分享](https://zhuanlan.zhihu.com/p/170603281)，[开源代码](https://github.com/zhangqibot/Tencent2020_Top5)
- 第11名：[开源代码1](https://github.com/wujiekd/2020-Tencent-advertising-algorithm-contest-rank11), [开源代码2](https://github.com/llllllyu/Tencent2020_Rank11)
- 第12名：[开源代码](https://github.com/LogicJake/Tencent_Ads_Algo_2020_TOP12)
- 第19名：[开源代码](https://github.com/PerpetualSmile/2020-Tencent-Advertisement-Algorithm-Competition-Rank19)

---

### [2021世界人工智能创新大赛](http://ailab.aiwin.org.cn/)
- [互联网舆情企业风险事件的识别和预警](https://github.com/datawhalechina/competition-baseline/tree/master/competition/AIWIN2021), NLP类型比赛
- [保险文本视觉认知问答竞赛](https://github.com/datawhalechina/competition-baseline/tree/master/competition/AIWIN2021), CV/NLP，多模态类型比赛

---

### [CCF BDCI2020大数据与计算智能大赛](https://www.datafountain.cn/special/BDCI2020)
- 通用音频分类, [LGB](https://github.com/zjuzpw/baseline/blob/CCF2020BDCI/baseline_lgb.ipynb), [CNN, 0.9+](https://blog.csdn.net/wherewegogo/article/details/110369729)
- 遥感影像地块分割, [U-Net](https://aistudio.baidu.com/aistudio/projectdetail/1090790)
- 房产行业聊天问答匹配, [Bert](https://github.com/syzong/2020_ccf_qa_match), [RoBERTa](https://github.com/LogicJake/competition_baselines/tree/master/competitions/property_chat_pair)
- 小学数学应用题自动解题, [规则思路](https://discussion.datafountain.cn/questions/3169?new=0)
- 路况状态时空预测, [OTTO Lab](https://github.com/juzstu/ccf2020_didi), [异度侵入](https://mp.weixin.qq.com/s/1vJDOInUOdBgXtLdVcpsEA)
- 企业非法集资风险预测, [第一次打比赛](https://github.com/LogicJake/competition_baselines/tree/master/competitions/fund_raising_risk_prediction), [DLLXW](https://github.com/DLLXW/data-science-competition/tree/main/datafountain), [阿水](https://github.com/datawhalechina/competition-baseline/tree/master/competition/DataFountain-%E4%BC%81%E4%B8%9A%E9%9D%9E%E6%B3%95%E9%9B%86%E8%B5%84%E9%A3%8E%E9%99%A9%E9%A2%84%E6%B5%8B)
- 大数据时代的Serverless工作负载预测, [第一次打比赛（A榜0.208）](https://github.com/LogicJake/competition_baselines/tree/master/competitions/serverless_load_prediction), [siguo（A榜0.211）](https://blog.csdn.net/qq_48081601/article/details/109338443), [CNN-LSTM](https://zhuanlan.zhihu.com/p/301092469),[鱼佬(0.285)](https://mp.weixin.qq.com/s/Ovb1pic2nleQhTObIaj2Ww), [siliconx(0.311)](https://github.com/siliconx/serverless)
- 重点区域人群密度预测, [第1名方案](https://github.com/agave233/2020-CCF-Crowd-Flow-Prediction)

---

### [第四届工业大数据创新竞赛——算法赛道](http://www.industrial-bigdata.com/Challenge/title?competitionId=GKLEW707XP2O58KZNLO4UPYKCOIEQONH)
- 学习手册：https://coggle.club/learn/industrial-bigdata-4th/
- [注塑成型工艺的虚拟量测和调机优化](https://github.com/datawhalechina/competition-baseline/blob/master/competition/%E7%AC%AC%E5%9B%9B%E5%B1%8A%E5%B7%A5%E4%B8%9A%E5%A4%A7%E6%95%B0%E6%8D%AE%E5%88%9B%E6%96%B0%E7%AB%9E%E8%B5%9B%EF%BC%9A%E7%AE%97%E6%B3%95%E8%B5%9B%E9%81%93/%E6%B3%A8%E5%A1%91%E6%88%90%E5%9E%8B%E8%B5%9B%E9%81%93baseline.ipynb)

---

### [2020数字中国创新大赛大数据赛道](https://data.xm.gov.cn/opendata-competition/index.html#/)
- 入门注册手册：https://mp.weixin.qq.com/s/NurvUDyGwVC4sSwzEzDrwg
- 高德地图城市交通健康榜：https://report.amap.com/diagnosis/index.do
- 赛题1数据分析：[链接](https://github.com/datawhalechina/competition-baseline/tree/master/competition/2020DCIC-%E5%88%9B%E6%96%B0%E5%A4%A7%E8%B5%9B%E5%A4%A7%E6%95%B0%E6%8D%AE%E8%B5%9B%E9%81%93)
- 赛题2学习内容：[链接](https://coggle.club/learn/DCIC2020/), [录屏](https://www.bilibili.com/video/BV1tz4y1f7Wg/)

---

### [科大讯飞AI开发者大赛（2020年度）](http://challenge.xfyun.cn/)

- [脑PET图像分析和疾病预测挑战赛算法挑战大赛](https://github.com/datawhalechina/competition-baseline/tree/master/competition/%E7%A7%91%E5%A4%A7%E8%AE%AF%E9%A3%9EAI%E5%BC%80%E5%8F%91%E8%80%85%E5%A4%A7%E8%B5%9B-%E8%84%91PET%E5%9B%BE%E5%83%8F%E5%88%86%E6%9E%90%E5%92%8C%E7%96%BE%E7%97%85%E9%A2%84%E6%B5%8B%E6%8C%91%E6%88%98%E8%B5%9B%E7%AE%97%E6%B3%95%E6%8C%91%E6%88%98%E5%A4%A7%E8%B5%9B), CV类型比赛
- [温室温度预测挑战赛](https://github.com/datawhalechina/competition-baseline/tree/master/competition/%E7%A7%91%E5%A4%A7%E8%AE%AF%E9%A3%9EAI%E5%BC%80%E5%8F%91%E8%80%85%E5%A4%A7%E8%B5%9B-%E6%B8%A9%E5%AE%A4%E6%B8%A9%E5%BA%A6%E9%A2%84%E6%B5%8B%E6%8C%91%E6%88%98%E8%B5%9B), 结构化数据比赛
- [婴儿啼哭声识别挑战赛](https://github.com/datawhalechina/competition-baseline/tree/master/competition/%E7%A7%91%E5%A4%A7%E8%AE%AF%E9%A3%9EAI%E5%BC%80%E5%8F%91%E8%80%85%E5%A4%A7%E8%B5%9B-%E5%A9%B4%E5%84%BF%E5%95%BC%E5%93%AD%E5%A3%B0%E8%AF%86%E5%88%AB%E6%8C%91%E6%88%98%E8%B5%9B), 语音赛题比赛
- [事件抽取挑战赛](https://github.com/datawhalechina/competition-baseline/tree/master/competition/%E7%A7%91%E5%A4%A7%E8%AE%AF%E9%A3%9EAI%E5%BC%80%E5%8F%91%E8%80%85%E5%A4%A7%E8%B5%9B-%E4%BA%8B%E4%BB%B6%E6%8A%BD%E5%8F%96%E6%8C%91%E6%88%98), NLP类型比赛

---

### 结构化比赛
- [第三届 Apache Flink 极客挑战赛暨AAIG CUP](https://tianchi.aliyun.com/s/ea4fbcbaadab849b7389354501f38e2e), [TF2 baseline](https://gitee.com/coggle/tianchi-3rd-AAIG-CUP)
- [山东省第二届数据应用创新创业大赛-临沂分赛场-供水管网压力预测](http://data.sd.gov.cn/cmpt/cmptDetail.html?id=24), [ChallengeHub](https://github.com/China-ChallengeHub/ChallengeHub-Baselines/tree/main/shandong_shuiguan/code)
- [山东省第二届数据应用创新创业大赛-济南分赛场-健康医疗](http://data.sd.gov.cn/cmpt/cmptDetail.html?id=22), [ChallengeHub](https://github.com/China-ChallengeHub/ChallengeHub-Baselines/tree/main/%E5%B1%B1%E4%B8%9C%E5%81%A5%E5%BA%B7%E5%8C%BB%E7%96%97)
- [山东省第二届数据应用创新创业大赛-日照分赛场-公积金贷款逾期预测](http://data.sd.gov.cn/cmpt/cmptDetail.html?id=26), [ChallengeHub](https://github.com/China-ChallengeHub/ChallengeHub-Baselines/tree/main/shandong_gongjijin/code)
- [2020厦门国际银行数创金融杯建模大赛](https://www.dcjingsai.com/v2/cmptDetail.html?id=439&=76f6724e6fa9455a9b5ef44402c08653), [第一次打比赛](https://github.com/LogicJake/competition_baselines/tree/master/competitions/xiamen_international_bank_2020), [OTTO Lab](https://github.com/cXPromise/Datacasle_2020XM_Baseline), [0.46](https://github.com/BirderEric/XianmenBank)
- [2019厦门国际银行“数创金融杯”数据建模大赛](https://m.dcjingsai.com/cmptDetail.html?id=319), [yanqiangmiffy](https://github.com/yanqiangmiffy/Data-Finance-Cup), [shenxiangzhuang](https://github.com/shenxiangzhuang/Bank-Competition)
- [天池-零基础入门数据挖掘 - 二手车交易价格预测](https://tianchi.aliyun.com/competition/entrance/231784/introduction), [baseline链接](https://github.com/yangjiada/used_cars)
- [天池-2020数字中国创新大赛—算法赛：智慧海洋建设](https://github.com/datawhalechina/competition-baseline/tree/master/competition/Tianchi-2020%E6%95%B0%E5%AD%97%E4%B8%AD%E5%9B%BD%E5%88%9B%E6%96%B0%E5%A4%A7%E8%B5%9B%E2%80%94%E7%AE%97%E6%B3%95%E8%B5%9B%EF%BC%9A%E6%99%BA%E6%85%A7%E6%B5%B7%E6%B4%8B%E5%BB%BA%E8%AE%BE), 结构化数据比赛
- [DataFountain-乘用车细分市场销量预测](https://github.com/datawhalechina/competition-baseline/tree/master/competition/DataFountain-%E4%B9%98%E7%94%A8%E8%BD%A6%E7%BB%86%E5%88%86%E5%B8%82%E5%9C%BA%E9%94%80%E9%87%8F%E9%A2%84%E6%B5%8B), 结构化
数据比赛
- [DataFountain-离散制造过程中典型工件的质量符合率预测](https://github.com/datawhalechina/competition-baseline/tree/master/competition/DataFountain-%E7%A6%BB%E6%95%A3%E5%88%B6%E9%80%A0%E8%BF%87%E7%A8%8B%E4%B8%AD%E5%85%B8%E5%9E%8B%E5%B7%A5%E4%BB%B6%E7%9A%84%E8%B4%A8%E9%87%8F%E7%AC%A6%E5%90%88%E7%8E%87%E9%A2%84%E6%B5%8B), 结构化数据比赛
- [腾讯-2018腾讯广告算法大赛 Rank11](https://github.com/datawhalechina/competition-baseline/tree/master/competition/%E8%85%BE%E8%AE%AF-2018%E8%85%BE%E8%AE%AF%E5%B9%BF%E5%91%8A%E7%AE%97%E6%B3%95%E5%A4%A7%E8%B5%9B)，结构化数据比赛
- [腾讯-2018腾讯广告算法大赛 冠军](https://github.com/datawhalechina/competition-baseline/tree/master/competition/%E8%85%BE%E8%AE%AF-2019%E8%85%BE%E8%AE%AF%E5%B9%BF%E5%91%8A%E7%AE%97%E6%B3%95%E5%A4%A7%E8%B5%9B)，结构化数据比赛
- [天池-安泰杯跨境电商智能算法大赛](https://github.com/datawhalechina/competition-baseline/tree/master/competition/Tianchi-%E5%AE%89%E6%B3%B0%E6%9D%AF%E8%B7%A8%E5%A2%83%E7%94%B5%E5%95%86%E6%99%BA%E8%83%BD%E7%AE%97%E6%B3%95%E5%A4%A7%E8%B5%9B)，结构化数据比赛，**冠军法国南部**分享
- [点石-Retention Rate of Baidu Hao Kan APP Users](https://github.com/datawhalechina/competition-baseline/tree/master/competition/%E7%82%B9%E7%9F%B3-Retention%20Rate%20of%20Baidu%20Hao%20Kan%20APP%20Users)，结构化数据比赛
- [kaggle-two-sigma-connect-rental-listing-inquiries](https://github.com/datawhalechina/competition-baseline/tree/master/competition/kaggle-two-sigma-connect-rental-listing-inquiries)，结构化数据比赛
- [kaggle-allstate-claims-severity](https://github.com/datawhalechina/competition-baseline/tree/master/competition/kaggle-allstate-claims-severity)，结构化数据比赛
- [AI研习社-白葡萄酒品质预测](https://github.com/datawhalechina/competition-baseline/tree/master/competition/yanxishe-%E7%99%BD%E8%91%A1%E8%90%84%E9%85%92%E5%93%81%E8%B4%A8%E9%A2%84%E6%B5%8B)，结构化数据比赛
- [AI研习社-肌肉活动电信号推测手势](https://github.com/datawhalechina/competition-baseline/tree/master/competition/yanxishe-%E8%82%8C%E8%82%89%E6%B4%BB%E5%8A%A8%E7%94%B5%E4%BF%A1%E5%8F%B7%E6%8E%A8%E6%B5%8B%E6%89%8B%E5%8A%BF)，结构化数据比赛

---

### CV类型比赛
- [“英特尔创新大师杯”深度学习挑战赛 赛道1：通用场景OCR文本识别任务](https://tianchi.aliyun.com/competition/entrance/531902/introduction), OCR比赛, [baseline](https://gitee.com/coggle/tianchi-intel-PaddleOCR)
- [2021全国数字生态创新大赛-智能算法赛](https://tianchi.aliyun.com/competition/entrance/531860/introduction), 语义分割比赛, [34.5-Unet](https://tianchi.aliyun.com/notebook-ai/detail?postId=169396), [38.5-Unet++](https://github.com/DLLXW/data-science-competition/tree/main/%E5%A4%A9%E6%B1%A0/2021%E5%85%A8%E5%9B%BD%E6%95%B0%E5%AD%97%E7%94%9F%E6%80%81%E5%88%9B%E6%96%B0%E5%A4%A7%E8%B5%9B-%E9%AB%98%E5%88%86%E8%BE%A8%E7%8E%87%E9%81%A5%E6%84%9F%E5%BD%B1%E5%83%8F%E5%88%86%E5%89%B2)
- [DC竞赛-AI助疫·口罩佩戴检测大赛](https://github.com/datawhalechina/competition-baseline/tree/master/competition/DC%E7%AB%9E%E8%B5%9B-AI%E5%8A%A9%E7%96%AB%C2%B7%E5%8F%A3%E7%BD%A9%E4%BD%A9%E6%88%B4%E6%A3%80%E6%B5%8B%E5%A4%A7%E8%B5%9B), CV类型比赛
- [Kesci-中国华录杯人群密度检测](https://github.com/datawhalechina/competition-baseline/tree/master/competition/Kesci-%E4%B8%AD%E5%9B%BD%E5%8D%8E%E5%BD%95%E6%9D%AF%E4%BA%BA%E7%BE%A4%E5%AF%86%E5%BA%A6%E6%A3%80%E6%B5%8B), CV类型比赛
- [天池-心电人机智能大赛心电异常事件预测](https://github.com/datawhalechina/competition-baseline/tree/master/competition/Tianchi-%E5%BF%83%E7%94%B5%E4%BA%BA%E6%9C%BA%E6%99%BA%E8%83%BD%E5%A4%A7%E8%B5%9B%E5%BF%83%E7%94%B5%E5%BC%82%E5%B8%B8%E4%BA%8B%E4%BB%B6%E9%A2%84%E6%B5%8B), CV类型比赛
- [DataFountain-多人种人脸识别](https://github.com/datawhalechina/competition-baseline/tree/master/competition/DataFountain-%E5%A4%9A%E4%BA%BA%E7%A7%8D%E4%BA%BA%E8%84%B8%E8%AF%86%E5%88%AB), CV类型比赛
- [DataFountain-基于OCR的身份证要素提取](https://github.com/datawhalechina/competition-baseline/tree/master/competition/DataFountain-%E5%9F%BA%E4%BA%8EOCR%E7%9A%84%E8%BA%AB%E4%BB%BD%E8%AF%81%E8%A6%81%E7%B4%A0%E6%8F%90%E5%8F%96), CV类型比赛
- [DataFountain-视频版权检测算法](https://github.com/datawhalechina/competition-baseline/tree/master/competition/DataFountain-%E8%A7%86%E9%A2%91%E7%89%88%E6%9D%83%E6%A3%80%E6%B5%8B%E7%AE%97%E6%B3%95)，CV类型比赛
- [kaggle-quickdraw-doodle-recognition](https://github.com/datawhalechina/competition-baseline/tree/master/competition/kaggle-quickdraw-doodle-recognition)，CV类型比赛
- [TinyMind人民币面值&冠字号编码识别挑战赛](https://github.com/datawhalechina/competition-baseline/tree/master/competition/TinyMind%E4%BA%BA%E6%B0%91%E5%B8%81%E9%9D%A2%E5%80%BC%26%E5%86%A0%E5%AD%97%E5%8F%B7%E7%BC%96%E7%A0%81%E8%AF%86%E5%88%AB%E6%8C%91%E6%88%98%E8%B5%9B)，CV类型比赛
- [AI研习社-胸腔X光肺炎检测](https://github.com/datawhalechina/competition-baseline/tree/master/competition/yanxishe-%E8%83%B8%E8%85%94X%E5%85%89%E8%82%BA%E7%82%8E%E6%A3%80%E6%B5%8B)，CV类型比赛
- [AI研习社-肺炎X光病灶识别](https://github.com/datawhalechina/competition-baseline/tree/master/competition/yanxishe-%E8%82%BA%E7%82%8EX%E5%85%89%E7%97%85%E7%81%B6%E8%AF%86%E5%88%AB)，CV类型比赛
- [AI研习社-人脸年龄识别](https://github.com/datawhalechina/competition-baseline/tree/master/competition/yanxishe-%E4%BA%BA%E8%84%B8%E5%B9%B4%E9%BE%84%E8%AF%86%E5%88%AB)，CV类型比赛
- [AI研习社-美食识别挑战（1）：豆腐VS土豆](https://github.com/datawhalechina/competition-baseline/tree/master/competition/yanxishe-%E7%BE%8E%E9%A3%9F%E8%AF%86%E5%88%AB%E6%8C%91%E6%88%98%EF%BC%881%EF%BC%89%EF%BC%9A%E8%B1%86%E8%85%90VS%E5%9C%9F%E8%B1%86)，CV类型比赛
- [AI研习社-猫脸关键点检测](https://github.com/datawhalechina/competition-baseline/tree/master/competition/yanxishe-%E5%96%B5%E8%84%B8%E5%85%B3%E9%94%AE%E7%82%B9%E6%A3%80%E6%B5%8B)，CV类型比赛

---

### NLP类型比赛

- [AI研习社-IMDB评论剧透检测](https://github.com/datawhalechina/competition-baseline/tree/master/competition/yanxishe-IMDB%E8%AF%84%E8%AE%BA%E5%89%A7%E9%80%8F%E6%A3%80%E6%B5%8B)，NLP类型比赛
- [DataFountain-金融信息负面及主体判定](https://github.com/datawhalechina/competition-baseline/tree/master/competition/DataFountain-%E9%87%91%E8%9E%8D%E4%BF%A1%E6%81%AF%E8%B4%9F%E9%9D%A2%E5%8F%8A%E4%B8%BB%E4%BD%93%E5%88%A4%E5%AE%9A), NLP类型比赛
- [DataFountain-互联网金融新实体发现](https://github.com/datawhalechina/competition-baseline/tree/master/competition/DataFountain-%E4%BA%92%E8%81%94%E7%BD%91%E9%87%91%E8%9E%8D%E6%96%B0%E5%AE%9E%E4%BD%93%E5%8F%91%E7%8E%B0)，NLP类型比赛
- [DataFountain-技术需求与技术成果项目之间关联度计算模型](https://github.com/datawhalechina/competition-baseline/tree/master/competition/DataFountain-%E6%8A%80%E6%9C%AF%E9%9C%80%E6%B1%82%E4%B8%8E%E6%8A%80%E6%9C%AF%E6%88%90%E6%9E%9C%E9%A1%B9%E7%9B%AE%E4%B9%8B%E9%97%B4%E5%85%B3%E8%81%94%E5%BA%A6%E8%AE%A1%E7%AE%97%E6%A8%A1%E5%9E%8B)，NLP类型比赛
- [DataFountain-互联网新闻情感分析](https://github.com/datawhalechina/competition-baseline/tree/master/competition/DataFountain-%E4%BA%92%E8%81%94%E7%BD%91%E6%96%B0%E9%97%BB%E6%83%85%E6%84%9F%E5%88%86%E6%9E%90)，NLP类型比赛
- [biendata-智源&计算所-互联网虚假新闻检测挑战赛](https://github.com/datawhalechina/competition-baseline/tree/master/competition/biendata-%E6%99%BA%E6%BA%90%26%E8%AE%A1%E7%AE%97%E6%89%80-%E4%BA%92%E8%81%94%E7%BD%91%E8%99%9A%E5%81%87%E6%96%B0%E9%97%BB%E6%A3%80%E6%B5%8B%E6%8C%91%E6%88%98%E8%B5%9B)，NLP类型比赛
- [Tianchi-第三届阿里云安全算法挑战赛](https://github.com/datawhalechina/competition-baseline/tree/master/competition/Tianchi-%E7%AC%AC%E4%B8%89%E5%B1%8A%E9%98%BF%E9%87%8C%E4%BA%91%E5%AE%89%E5%85%A8%E7%AE%97%E6%B3%95%E6%8C%91%E6%88%98%E8%B5%9B)，NLP类型比赛

---

### 其他类型

- [DataFountain-企业网络资产及安全事件分析与可视化](https://github.com/datawhalechina/competition-baseline/tree/master/competition/DataFountain-%E4%BC%81%E4%B8%9A%E7%BD%91%E7%BB%9C%E8%B5%84%E4%BA%A7%E5%8F%8A%E5%AE%89%E5%85%A8%E4%BA%8B%E4%BB%B6%E5%88%86%E6%9E%90%E4%B8%8E%E5%8F%AF%E8%A7%86%E5%8C%96)
- [DataFountain-三角形图计算算法设计及性能优化](https://github.com/datawhalechina/competition-baseline/tree/master/competition/DataFountain-%E4%B8%89%E8%A7%92%E5%BD%A2%E5%9B%BE%E8%AE%A1%E7%AE%97%E7%AE%97%E6%B3%95%E8%AE%BE%E8%AE%A1%E5%8F%8A%E6%80%A7%E8%83%BD%E4%BC%98%E5%8C%96), 计算优化
- [DataFountain-云计算时代的大数据查询分析优化](https://github.com/datawhalechina/competition-baseline/tree/master/competition/DataFountain-%E4%BA%91%E8%AE%A1%E7%AE%97%E6%97%B6%E4%BB%A3%E7%9A%84%E5%A4%A7%E6%95%B0%E6%8D%AE%E6%9F%A5%E8%AF%A2%E5%88%86%E6%9E%90%E4%BC%98%E5%8C%96), 查询优化

其他链接：

- [Smile整理的竞赛优胜者代码分享](https://github.com/Smilexuhc/Data-Competition-TopSolution)
- [chizhu开源的高校赛2019 文本点击预测](https://github.com/chizhu/BDC2019)

## 贡献者(按照贡献ID排序)

- [阿水](https://www.zhihu.com/people/finlayliu/)
- [DOTA](https://www.zhihu.com/people/yuconan/)
- [Rain](https://www.zhihu.com/people/kingdoms/activities)
- [鱼遇雨欲语与余](https://www.zhihu.com/people/wang-he-13-93/)
- [yphacker](https://github.com/yphacker)

## 协作规范

欢迎大家fork并贡献代码，但请大家遵守以下规范和建议：

1. 代码请按照比赛的形式进行整理，写明比赛的网址、数据类型和解题赛题；

2. 代码请注明运行的环境，以及机器最低配置，如：
   - 操作系统：Linux，内存16G，硬盘无要求；
   - Python环境：Python2/3
   - Pytorch版本：0.4.0
   
3. baseline代码只能提供可运行的代码和思路，**请不要提供直接可以提交的结果文件；**

4. 代码提供者应对代码版权和共享权负责；

5. 如果发现Repo存在版权等相关问题，请邮件联系****************
  
## 关注我们

<div align=center><img src="https://cdn.coggle.club/dw_qrcode.jpeg" width = "250" height = "270" alt="Datawhale是一个专注AI领域的开源组织，以“for the learner，和学习者一起成长”为愿景，构建对学习者最有价值的开源学习社区。关注我们，一起学习成长。"></div>

## LICENSE

[GNU General Public License v3.0](https://github.com/datawhalechina/competition-baseline/blob/master/LICENSE)

## Star History

[![Star History Chart](https://api.star-history.com/svg?repos=datawhalechina/competition-baseline&type=Date)](https://star-history.com/#datawhalechina/competition-baseline&Date)

