# Sora原理与技术实战

[Datawhale](https://datawhale.club/home) x [魔搭社区（阿里巴巴达摩院模型开源社区modelscope）](https://modelscope.cn/)，硬核Sora原理与技术实战课程，旨在帮助开发者了解原理，动手去做，在实践中学习，更好地在风浪中去噪，从容迎接变革时刻到来。

## 学习简介

### 面向对象

- 有一定Python基础
- 能熟练阅读和理解AI项目源代码
- 有足够的精力投入，积极参与开源，热爱分享

### 学习亮点

- 学习内容：
  - AI圈最火热的Sora的硬核课程
  - Sora技术路径详解和核心技术解析
  - 开源模型和代码上手实战
- 学习支持：
  - 原理解析，技术专家和学术大神助力学习，知其然，知其所以然
  - 实战练习，开源模型代码实战，动手第一
  - 组队学习，遇见志同道合的伙伴，让学习不再孤独

## 学习内容

Sora技术路径详解，并针对Sora原理中的核心技术点，包括基于diffusion视频生成技术，diffusion Transformers技术解析，声音生成TTS技术解析与实战，video caption技术解析，视频的编解码压缩进行原理介绍，并结合开源模型和代码上手实战。

## 课程大纲

| 学习阶段 | 学习内容 |
| :----| :---- |
| chapter1 Sora技术路径 | 深入了解Sora技术路径，系统认知 |
| chapter2 技术解析与实战 | Stable diffusion技术解析，基于diffusion的视频生成技术介绍+实战 <br/> Transformers技术解析+实战(LLM) <br/> 多种Transformers diffusion模型技术图像生成技术+实战 <br/> 基于Transformers diffusion的视频生成技术解析+实战 <br/> 声音生成TTS技术解析与实战 <br/> |
| chapter3 Sora训练与出片 | 训练一个sora模型的准备工作，video caption和算力评估 <br/> 用自己训练的模型，做一个自己的AI短片吧 |

## 学习资料

| 课程 | 嘉宾 | 资料 |
| :----| :---- | :---- |
| 开营仪式：课程介绍 | 玉鑫：Datawhale成员 | [录播回放](https://www.bilibili.com/video/BV1wm411f7gf) <br/> [PPT下载](https://datawhaler.feishu.cn/file/YAK8bkpu1oG2FdxkEYvcIErXn0g)|
| 第一讲：Sora技术原理详解 | 杨知铮：厦门大学平潭研究院研究员 | [录播回放](https://www.bilibili.com/video/BV1wm411f7gf) <br/> [PPT下载](https://datawhaler.feishu.cn/file/KntHbV3QGoEPruxEql2c9lrsnOb)|
| 第二讲：文生图片技术路径、原理与SD实战 | 成晨：魔搭社区技术运营负责人 | [录播回放](https://www.bilibili.com/video/BV1ZS421A7sR/?vd_source=79686b80ce91d6c3977b2e269db5e8b8) <br/>[文字教程](./docs/chapter2/chapter2_1/chapter2_1.md) |
| 第三讲：Transformers技术解析+实战(LLM) | 长琴：Datawhale成员、HuggingLLM负责人 | [录播回放](https://www.bilibili.com/video/BV17Z421a71d/) <br/>[文字教程](./docs/chapter2/chapter2_2/chapter2_2.md) |
| 第四讲：基于Transformers diffusion的视频生成技术解析+实战介绍 | 聂同学： U-ViT作者 <br/> 成晨：魔搭社区技术运营负责人 | [录播回放](https://www.bilibili.com/video/BV1px421y7qU/?vd_source=79686b80ce91d6c3977b2e269db5e8b8) <br/>[文字教程](./docs/chapter2/chapter2_3/chapter2_3.md) |

## 参与贡献

- 如果你想参与到项目中来欢迎查看项目的 [Issue](https://github.com/datawhalechina/sora-tutorial/issues) 查看没有被分配的任务。
- 如果你发现了一些问题，欢迎在 [Issue](https://github.com/datawhalechina/sora-tutorial/issues) 中进行反馈。
- 如果你对本项目感兴趣想要参与进来可以联系仓库的负责人进行交流💬。

如果你对 Datawhale 很感兴趣并想要发起一个新的项目，欢迎查看 [Datawhale 贡献指南](https://github.com/datawhalechina/DOPMC#%E4%B8%BA-datawhale-%E5%81%9A%E5%87%BA%E8%B4%A1%E7%8C%AE)。

## 关注我们

<div align=center>
<p>扫描下方二维码关注公众号：Datawhale</p>
<img src="https://raw.githubusercontent.com/datawhalechina/pumpkin-book/master/res/qrcode.jpeg" width = "180" height = "180">
</div>

## LICENSE

<a rel="license" href="http://creativecommons.org/licenses/by-nc-sa/4.0/"><img alt="知识共享许可协议" style="border-width:0" src="https://img.shields.io/badge/license-CC%20BY--NC--SA%204.0-lightgrey" /></a><br />本作品采用<a rel="license" href="http://creativecommons.org/licenses/by-nc-sa/4.0/">知识共享署名-非商业性使用-相同方式共享 4.0 国际许可协议</a>进行许可。
