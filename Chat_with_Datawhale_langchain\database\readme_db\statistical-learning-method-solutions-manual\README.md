<div align=center>
<h1>统计学习方法习题解答</h1>
</div>

&emsp;&emsp;李航老师的《统计学习方法》和《机器学习方法》是机器学习领域的经典入门教材之一。本书分为监督学习、无监督学习和深度学习，全面系统地介绍了机器学习的主要方法。

- 第1篇主要介绍监督学习的主要方法，包括感知机、k近邻法、朴素贝叶斯法、决策树、逻辑斯谛回归与最大熵模型、支持向量机、提升方法、EM算法、隐马尔可夫模型和条件随机场等；
- 第2篇主要介绍无监督学习的主要方法，包括聚类方法、奇异值分解、主成分分析、潜在语义分析、概率潜在语义分析、马尔科夫链蒙特卡罗法、潜在狄利克雷分配和PageRank算法等；
- 第3篇主要介绍深度学习的主要方法，包括前馈神经网络、卷积神经网络、循环神经网络、序列到序列模型、预训练语言模型和生成对抗网络等。 

## 使用说明
&emsp;&emsp;统计学习方法习题解答，主要完成了该书的所有习题，并提供代码和运行之后的截图，里面的内容是以统计学习方法的内容为前置知识，该习题解答的最佳使用方法是以李航老师的《机器学习方法》为主线，并尝试完成课后习题，如果遇到不会的，再来查阅习题解答。  
&emsp;&emsp;如果觉得解答不详细，可以[点击这里](https://github.com/datawhalechina/statistical-learning-method-solutions-manual/issues)提交你希望补充推导或者习题编号，我们看到后会尽快进行补充。

### 在线阅读地址
在线阅读地址：https://datawhalechina.github.io/statistical-learning-method-solutions-manual

## 选用的《机器学习方法》版本
<img src="images/statistical-learning-method-book.png?raw=true" width="336" height= "500">

> 书名：机器学习方法  
> 作者：李航  
> 出版社：清华大学出版社  
> 版次：2022年3月第1版  

## Notebook运行环境配置
1. Python版本  
   请使用python3.10.X，如使用其他版本，requirements.txt中所列的依赖包可能不兼容。
   
2. 安装相关的依赖包
    ```shell
    pip install -r requirements.txt
    ```
   
3. 安装graphviz（用于展示决策树）  
    可参考博客：https://blog.csdn.net/HNUCSEE_LJK/article/details/86772806

4. 安装PyTorch
访问[PyTorch官网](https://pytorch.org/get-started/locally/)，选择合适的版本安装PyTorch，有条件的小伙伴可以下载GPU版本
```shell
pip3 install torch==1.12.1+cu116 torchvision==0.13.1+cu116 torchaudio==0.12.1+cu116 -f https://download.pytorch.org/whl/torch_stable.html
```

5. docsify框架运行
    ```shell
    docsify serve ./docs
    ```

## 协作规范
1. 由于习题解答中需要有程序和执行结果，采用jupyter notebook的格式进行编写（文件路径：notebook/notes），然后将其导出成markdown格式，再覆盖到docs对应的章节下。
2. 可按照Notebook运行环境配置，配置相关的运行环境。
3. 习题解答编写中，需要尽量使用初学者（有高数基础）能理解的数学概念，如果涉及公式定理的推导和证明，可附上参考链接。
4. 当前进度

| 章节号 |           标题           |  进度  | 负责人 | 审核人 |
| :---: | :----------------------: | :----: | :----: | :----: |
|  1 | 统计学习方法概论         | 已完成 | 胡锐锋、毛鹏志 | 王维嘉、毛鹏志、范佳慧 |
|  2 | 感知机                 | 已完成 | 胡锐锋 | 毛鹏志、范佳慧、王天富、王茸茸 |
|  3 | k近邻法                | 已完成 | 胡锐锋 | 王维嘉、毛鹏志、王茸茸 |
|  4 | 朴素贝叶斯法            | 已完成 | 胡锐锋、王维嘉 | 王瀚翀、王天富、王茸茸 |
|  5 | 决策树                 | 已完成 | 胡锐锋、王维嘉 |  王瀚翀、王天富、王茸茸 |
|  6 | 逻辑斯谛回归与最大熵模型 | 已完成 | 胡锐锋 | 毛鹏志、范佳慧、王瀚翀 |
|  7 | 支持向量机              | 已完成 | 胡锐锋、王茸茸 | 王维嘉、王瀚翀、王天富 |
|  8 | 提升方法               | 已完成 | 胡锐锋、王茸茸 | 王维嘉、毛鹏志、王瀚翀 |
|  9 | EM算法及其推广          | 已完成 | 胡锐锋 | 毛鹏志、范佳慧、王瀚翀、王茸茸 |
| 10 | 隐马尔可夫模型          | 已完成 | 胡锐锋、王瀚翀 | 王维嘉、范佳慧、王天富、王茸茸 |
| 11 | 条件随机场             | 已完成 | 胡锐锋、王瀚翀 | 王维嘉、范佳慧、王天富 |
| 14 | 聚类方法               | 已完成 | 胡锐锋、刘晓东 | 毛鹏志、汪健麟、王天富 |
| 15 | 奇异值分解             | 已完成 | 胡锐锋、李拥祺 | 张宇明、刘晓东、兰坤 |
| 16 | 主成分分析             | 已完成 | 胡锐锋、王茸茸 | 张宇明、刘晓东、范致远、兰坤 |
| 17 | 潜在语义分析            | 已完成 | 胡锐锋 | 汪健麟、王天富、兰坤 |
| 18 | 概率潜在语义分析        | 已完成 | 胡锐锋 | 毛鹏志、兰坤、汪健麟、张宇明 |
| 19 | 马尔可夫链蒙特卡罗法     | 已完成 | 胡锐锋、王天富 | 毛鹏志、刘晓东、范致远、汪健麟 |
| 20 | 潜在狄利克雷分配        | 已完成 | 胡锐锋、薛博阳 | 毛鹏志、刘晓东、范致远、王天富 |
| 21 | PageRank算法         | 已完成 | 胡锐锋、毛鹏志 | 张宇明、范致远、王天富 |
| 23 | 前馈神经网络           | 已完成 | 胡锐锋、毛鹏志 | 王天富、李拥祺、王昊文、胡磊 |
| 24 | 卷积神经网络           | 已完成 | 胡锐锋、王天富 | 王昊文、李拥祺、胡磊、李拙 |
| 25 | 循环神经网络           | 已完成 | 胡锐锋、王昊文 | 毛鹏志、李拥祺、王天富、李拙 |
| 26 | 序列到序列模型         | 待完善 | 胡锐锋、薛博阳 | 毛鹏志、王昊文、胡磊、李拙 |
| 27 | 预训练语言模型         | 已完成 | 胡锐锋、范致远 | 毛鹏志、胡磊、王天富、李拙 |
| 28 | 生成对抗网络           | 已完成 | 胡锐锋、胡磊 | 毛鹏志、王昊文、王天富、李拥祺 |

## 项目结构
<pre>
codes----------------------------------------------习题代码
|   +---ch02-----------------------------------------第2章习题解答代码
|   |   +---perceptron.py------------------------------习题2.2（构建从训练数据求解感知机模型的例子）
|   +---ch03-----------------------------------------第3章习题解答代码
|   |   +---k_neighbors_classifier.py------------------习题3.1（k近邻算法关于k值的模型比较）
|   |   +---kd_tree_demo.py----------------------------习题3.2（kd树的构建与求最近邻点）
|   |   +---my_kd_tree.py------------------------------习题3.3（用kd树的k邻近搜索算法）
|   +---ch05-----------------------------------------第5章习题解答代码
|   |   +---k_neighbors_classifier.py------------------习题5.1（调用sklearn的DecisionTreeClassifier类使用C4.5算法生成决策树）
|   |   +---my_decision_tree.py------------------------习题5.1（自编程实现C4.5生成算法）
|   |   +---my_least_squares_regression_tree.py--------习题5.2（最小二乘回归树生成算法）
|   +---ch06-----------------------------------------第6章习题解答代码
|   |   +---my_logistic_regression.py------------------习题6.2（实现Logistic回归模型学习的梯度下降法）
|   |   +---maxent_dfp.py------------------------------习题6.3（最大熵模型学习的DFP算法）
|   +---ch07-----------------------------------------第7章习题解答代码
|   |   +---svm_demo.py--------------------------------习题7.2（根据题目中的数据训练SVM模型，并在图中画出分离超平面、间隔边界及支持向量）
|   +---ch08-----------------------------------------第8章习题解答代码
|   |   +---adaboost_demo.py---------------------------习题8.1（使用sklearn的AdaBoostClassifier分类器实现）
|   |   +---my_adaboost.py-----------------------------习题8.1（自编程实现AdaBoost算法）
|   +---ch09-----------------------------------------第9章习题解答代码
|   |   +---three_coin_EM.py---------------------------习题9.1（三硬币模型的EM算法）
|   |   +---gmm_demo.py--------------------------------习题9.3（使用GaussianMixture求解两个分量高斯混合模型的6个参数）
|   |   +---my_gmm.py----------------------------------习题9.3（自编程实现求两个分量的高斯混合模型的5个参数）
|   +---ch10-----------------------------------------第10章习题解答代码
|   |   +---hidden_markov_backward.py------------------习题10.1（隐马尔可夫模型的后向算法）
|   |   +---hidden_markov_forward_backward.py----------习题10.2（隐马尔可夫模型的前向后向算法）
|   |   +---hidden_markov_viterbi.py-------------------习题10.3（隐马尔可夫模型的维特比算法）
|   +---ch11-----------------------------------------第11章习题解答代码
|   |   +---crf_matrix.py------------------------------习题11.4（使用条件随机场矩阵形式，计算所有路径状态序列的概率及概率最大的状态序列）
|   +---ch14-----------------------------------------第14章习题解答代码
|   |   +---divisive_clustering.py---------------------习题14.1（分裂聚类算法）
|   +---ch15-----------------------------------------第15章习题解答代码
|   |   +---my_svd.py----------------------------------习题15.1（自编程实现奇异值分解）
|   |   +---outer_product_expansion.py-----------------习题15.2（外积展开式）
|   +---ch16-----------------------------------------第16章习题解答代码
|   |   +---pca_svd.py---------------------------------习题16.1（样本矩阵的奇异值分解的主成分分析算法）
|   +---ch17-----------------------------------------第17章习题解答代码
|   |   +---lsa_svd.py---------------------------------习题17.1（用矩阵奇异值分解进行潜在语义分析）
|   |   +---divergence_nmf_lsa.py----------------------习题17.2（损失函数是散度损失时的非负矩阵分解算法）
|   +---ch18-----------------------------------------第18章习题解答代码
|   |   +---em_plsa.py---------------------------------习题18.1（基于生成模型的EM算法的概率潜在语义分析）
|   +---ch19-----------------------------------------第19章习题解答代码
|   |   +---monte_carlo_method.py----------------------习题19.1（蒙特卡洛法积分计算）
|   |   +---metropolis_hastings.py---------------------习题19.7（使用Metropolis-Hastings算法求后验概率分布的均值和方差）
|   |   +---gibbs_sampling.py--------------------------习题19.8（使用吉布斯抽样算法估计参数的均值和方差）
|   +---ch20-----------------------------------------第20章习题解答代码
|   |   +---gibbs_sampling_lda.py----------------------习题20.2（LDA吉布斯抽样算法）
|   +---ch21-----------------------------------------第21章习题解答代码
|   |   +---page_rank.py-------------------------------习题21.2（基本定义的PageRank的迭代算法）
|   +---ch23-----------------------------------------第23章习题解答代码
|   |   +---feedforward_nn_backpropagation.py----------习题23.3（自编程实现前馈神经网络的反向传播算法）
|   +---ch24-----------------------------------------第24章习题解答代码
|   |   +---cnn-text-classification.py-----------------习题24.7（基于CNN的自然语言句子分类模型）
|   +---ch26-----------------------------------------第26章习题解答代码
|   |   +---lstm_seq2seq.py----------------------------习题26.1（4层LSTM组成的序列到序列的基本模型）
|   |   +---cnn_seq2seq.py-----------------------------习题26.4（基于CNN的序列到序列模型）
|   +---ch27-----------------------------------------第27章习题解答代码
|   |   +---bi-lstm-text-classification.py-------------习题27.1（基于双向LSTM的预训练语言模型）
|   |   +---auto_encoder.py----------------------------习题27.3（2层卷积神经网络编码器和2层卷积神经网络解码器组成的自动编码器）
|   +---ch28-----------------------------------------第28章习题解答代码
|   |   +---zero_sum_game.py---------------------------习题28.2（零和博弈的代码验证）
docs-----------------------------------------------习题解答
notebook-------------------------------------------习题解答JupyterNotebook格式
requirements.txt-----------------------------------运行环境依赖包
</pre>

## 致谢

**核心贡献者**
- [胡锐锋-项目负责人](https://github.com/Relph1119) （Datawhale成员-华东交通大学-系统架构设计师）
- [王维嘉](https://github.com/king-vega) （中国石油大学（北京））
- [王茸茸](https://www.zhihu.com/people/naruto-80-25) （北京邮电大学-风控算法工程师）
- [王瀚翀](https://github.com/ericwang970322) （华东师范大学-推荐系统方向）
- [毛鹏志](https://github.com/NorthblueM) （Datawhale成员-中科院计算所-信息检索与生物信息方向）
- [刘晓东](https://blog.csdn.net/weixin_44212633) （中科院自动化研究所-意图识别与人机交互方向）
- [李拥祺](https://github.com/L3Y1Q2) （南方科技大学-运动规划与控制决策方向）
- [王天富](http://geminilight.cn/) （中国科学技术大学-数据挖掘与强化学习方向）
- [薛博阳](https://amourwaltz.github.io) （香港中文大学-语言模型与语音识别方向）
- [胡磊](https://github.com/DBinary) （北京科技大学-表征学习与生物信息方向）
- [范致远](https://github.com/Elvisambition) （Datawhale成员-中科院自动化研究所-信息抽取与大模型可信推理方向）
- [王昊文](https://github.com/whw199833) （帝国理工学院-算法工程师）

**其他**
1. 特别感谢 [@Sm1les](https://github.com/Sm1les)、[@LSGOMYP](https://github.com/LSGOMYP) 对本项目的帮助与支持；
2. 感谢[@GYHHAHA](https://github.com/GYHHAHA)，指出了第7章习题7.4的解答问题，并完善了该题的解答；
3. 感谢范佳慧、汪健麟、张宇明、兰坤、李拙等同学对项目提供的完善性建议；
4. 感谢张帆同学对习题27.1解答的帮助，解决了ELMo预训练模型的代码问题。

## 参考文献
1. [李航《统计学习方法笔记》中的代码、notebook、参考文献、Errata](https://github.com/SmirkCao/Lihang)  
2. [CART剪枝详解](https://blog.csdn.net/wjc1182511338/article/details/76793164)
3. [CART剪枝算法详解](http://www.pianshen.com/article/1752163397/)

## 关注我们
<div align=center>
<p>扫描下方二维码关注公众号：Datawhale</p>
<img src="images/qrcode.jpeg" width = "180" height = "180">
</div>
&emsp;&emsp;Datawhale，一个专注于AI领域的学习圈子。初衷是for the learner，和学习者一起成长。目前加入学习社群的人数已经数千人，组织了机器学习，深度学习，数据分析，数据挖掘，爬虫，编程，统计学，Mysql，数据竞赛等多个领域的内容学习，微信搜索公众号Datawhale可以加入我们。

## LICENSE
<a rel="license" href="http://creativecommons.org/licenses/by-nc-sa/4.0/"><img alt="知识共享许可协议" style="border-width:0" src="https://img.shields.io/badge/license-CC%20BY--NC--SA%204.0-lightgrey" /></a><br />本作品采用<a rel="license" href="http://creativecommons.org/licenses/by-nc-sa/4.0/">知识共享署名-非商业性使用-相同方式共享 4.0 国际许可协议</a>进行许可。