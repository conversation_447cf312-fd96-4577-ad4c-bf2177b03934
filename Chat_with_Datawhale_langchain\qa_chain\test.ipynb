{"cells": [{"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["from Chat_QA_chain_self import Chat_QA_chain_self #带历史记录的问答链\n", "from QA_chain_self import QA_chain_self       #不带历史记录的问答链"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["import os\n", "# os.environ"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 带历史记录的问答链  "]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["# 定义参数\n", "# model可选值：[\"gpt-3.5-turbo\", \"gpt-3.5-turbo-16k-0613\", \"gpt-3.5-turbo-0613\", \"gpt-4\", \"gpt-4-32k\"]，[\"ERNIE-Bot\", \"ERNIE-Bot-4\", \"ERNIE-Bot-turbo\"]，\n", "# [\"Spark-1.5\", \"Spark-2.0\"]，[\"chatglm_pro\", \"chatglm_std\", \"chatglm_lite\"]\n", "model:str = \"chatglm_std\"\n", "temperature:float=0.0\n", "top_k:int=4 \n", "chat_history:list=[] \n", "file_path:str = \"/Users/<USER>/Desktop/llm-universe/data_base/knowledge_db\"\n", "persist_path:str = \"/Users/<USER>/Desktop/llm-universe/data_base/vector_db/chroma\"\n", "appid:str=None \n", "api_key:str = \"\"   #or 从本地环境读取\n", "api_secret:str=None \n", "embedding = \"m3e\"     # [\"openai\",\"zhipuai\", \"m3e\"]  默认m3e\n", "embedding_key = \"\""]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<Chat_QA_chain_self.Chat_QA_chain_self object at 0x2a0310b80>\n"]}], "source": ["qa_chain = Chat_QA_chain_self(model=model, temperature=temperature, top_k=top_k, chat_history=chat_history, file_path=file_path, persist_path=persist_path, api_key=api_key, embedding = embedding, embedding_key=embedding_key)\n", "print(qa_chain)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 第一轮"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["question = \"我可以学习到关于强化学习的知识吗？\"\n", "#answer,chat_history = qa_chain.answer(question)\n", "answer = qa_chain.answer(question)\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[('我可以学习到关于强化学习的知识吗？', '根据您提供的信息，强化学习是一种机器学习的子领域，主要研究在不确定的情况下如何做出好的决策。强化学习应用广泛，如玩游戏、控制机器人、自动驾驶等。您提到的《Easy RL：强化学习教程》是一本关于强化学习的教材，该书通过一些简单生动的例子来解释强化学习概念，配有对应的概念词、习题和面试题，以及代码实战。这本书得到了一些专业人士的好评，并被推荐用于学习强化学习。\\\\n\\\\n另外，您还提到了一个名为rl-papers的仓库，该仓库收集了强化学习及其应用领域的论文，涵盖了基础强化学习、多智体强化学习、推荐系统、游戏理论、交通系统等多个方面。通过阅读这些论文，您也可以了解到关于强化学习的知识。\\\\n\\\\n综上所述，您可以学习到关于强化学习的知识，可以通过阅读《Easy RL：强化学习教程》和参考rl-papers仓库中的论文来进一步了解强化学习。')]\n", "[('我可以学习到关于强化学习的知识吗？', '根据您提供的信息，强化学习是一种机器学习的子领域，主要研究在不确定的情况下如何做出好的决策。强化学习应用广泛，如玩游戏、控制机器人、自动驾驶等。您提到的《Easy RL：强化学习教程》是一本关于强化学习的教材，该书通过一些简单生动的例子来解释强化学习概念，配有对应的概念词、习题和面试题，以及代码实战。这本书得到了一些专业人士的好评，并被推荐用于学习强化学习。\\\\n\\\\n另外，您还提到了一个名为rl-papers的仓库，该仓库收集了强化学习及其应用领域的论文，涵盖了基础强化学习、多智体强化学习、推荐系统、游戏理论、交通系统等多个方面。通过阅读这些论文，您也可以了解到关于强化学习的知识。\\\\n\\\\n综上所述，您可以学习到关于强化学习的知识，可以通过阅读《Easy RL：强化学习教程》和参考rl-papers仓库中的论文来进一步了解强化学习。')]\n"]}], "source": ["print(answer)\n", "print(chat_history)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 第二轮"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["question = \"为什么这门课需要教这方面的知识？\"\n", "answer,chat_history = qa_chain.answer(question)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('我可以学习到关于强化学习的知识吗？', '根据您提供的信息，强化学习是一种机器学习的子领域，主要研究在不确定的情况下如何做出好的决策。强化学习应用广泛，如玩游戏、控制机器人、自动驾驶等。您提到的《Easy RL：强化学习教程》是一本关于强化学习的教材，该书通过一些简单生动的例子来解释强化学习概念，配有对应的概念词、习题和面试题，以及代码实战。这本书得到了一些专业人士的好评，并被推荐用于学习强化学习。\\\\n\\\\n另外，您还提到了一个名为rl-papers的仓库，该仓库收集了强化学习及其应用领域的论文，涵盖了基础强化学习、多智体强化学习、推荐系统、游戏理论、交通系统等多个方面。通过阅读这些论文，您也可以了解到关于强化学习的知识。\\\\n\\\\n综上所述，您可以学习到关于强化学习的知识，可以通过阅读《Easy RL：强化学习教程》和参考rl-papers仓库中的论文来进一步了解强化学习。')\n", "('为什么这门课需要教这方面的知识？', '强化学习作为人工智能的一个重要分支，主要研究在不确定的情况下如何做出好的决策。现实生活中许多问题都具有不确定性和动态性，因此强化学习在很多领域都有广泛的应用，如自动驾驶、机器人控制、智能交通、游戏AI等。学习强化学习可以帮助学生更好地理解和解决这些问题，为我国人工智能领域的发展贡献力量。此外，强化学习也是目前学术界和工业界的研究热点，掌握这门课程的知识可以帮助学生在未来的工作和研究中更好地适应和引领这个领域的发展。')\n"]}], "source": ["print(answer)\n", "print(chat_history)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 改变历史记录的长度"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[('为什么这门课需要教这方面的知识？', '强化学习作为人工智能的一个重要分支，主要研究在不确定的情况下如何做出好的决策。现实生活中许多问题都具有不确定性和动态性，因此强化学习在很多领域都有广泛的应用，如自动驾驶、机器人控制、智能交通、游戏AI等。学习强化学习可以帮助学生更好地理解和解决这些问题，为我国人工智能领域的发展贡献力量。此外，强化学习也是目前学术界和工业界的研究热点，掌握这门课程的知识可以帮助学生在未来的工作和研究中更好地适应和引领这个领域的发展。')]\n"]}], "source": ["history_len = 1 \n", "\n", "### 改变历史记录的长度,使用的是history_len参数来改变保留历史记录的长度\n", "chat_history = qa_chain.change_history_length(history_len)\n", "print(chat_history)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 清除历史记录"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["None\n"]}], "source": ["chat_history = qa_chain.clear_history()\n", "print(chat_history)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 不带历史记录的问答链"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["# 定义参数\n", "# model可选值：[\"gpt-3.5-turbo\", \"gpt-3.5-turbo-16k-0613\", \"gpt-3.5-turbo-0613\", \"gpt-4\", \"gpt-4-32k\"]，[\"ERNIE-Bot\", \"ERNIE-Bot-4\", \"ERNIE-Bot-turbo\"]，\n", "# [\"Spark-1.5\", \"Spark-2.0\"]，[\"chatglm_pro\", \"chatglm_std\", \"chatglm_lite\"]\n", "model:str = \"chatglm_std\"\n", "temperature:float=0.0\n", "top_k:int=4 \n", "file_path:str = \"/Users/<USER>/Desktop/llm-universe/data_base/knowledge_db\"\n", "persist_path:str = \"/Users/<USER>/Desktop/llm-universe/data_base/vector_db/chroma\"\n", "appid:str=None \n", "api_key:str = \"\"   #or从本地环境读取\n", "api_secret:str=None \n", "embedding = \"m3e\"\n", "embedding_key = \"\""]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<QA_chain_self.QA_chain_self object at 0x2f5afdf10>\n"]}], "source": ["#星火输入参数用法\n", "#qa_chain = QA_chain_self(model=model, temperature=temperature, top_k=top_k, file_path=file_path, persist_path=persist_path, appid=appid,api_key=api_key,Spark_api_secret=Spark_api_secret, embedding = embedding,embedding_key=embedding_key)\n", "\n", "##百度文心输入参数用法\n", "#qa_chain = QA_chain_self(model=model, temperature=temperature, top_k=top_k, file_path=file_path, persist_path=persist_path, appid=appid,api_key=api_key,Wenxin_api_secret=Wenxin_api_secret, embedding = embedding,embedding_key=embedding_key)\n", "\n", "#智谱(或OpenAI)输入参数用法\n", "qa_chain = QA_chain_self(model=model, temperature=temperature, top_k=top_k, file_path=file_path, persist_path=persist_path, appid=appid,api_key=api_key, embedding = embedding,embedding_key=embedding_key)\n", "print(qa_chain)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["question = \"什么是蘑菇书（easyrl）？\"\n", "answer = qa_chain.answer(question)"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["蘑菇书（easy-rl）是一本关于强化学习的教程，主要包含李宏毅老师的强化学习视频内容、经典资料整理、章节习题和算法实战等，旨在帮助读者学习强化学习并探索人工智能领域。谢谢你的提问！\n"]}], "source": ["print(answer)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["------------------\n", "> 其他模型可参考上面的实例进行使用，embedding 目前支持 openAI 和智谱以及m3e的模型，其他类型敬请期待！"]}], "metadata": {"kernelspec": {"display_name": "xuhu", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}