<div align=center>
<img src="images/logo.png" width="400">
</div>

# 妙趣横生大数据 Juicy Big Data

[![](https://img.shields.io/github/watchers/datawhalechina/juicy-bigdata.svg?style=flat)](https://github.com/datawhalechina/juicy-bigdata/watchers)
[![](https://img.shields.io/github/stars/datawhalechina/juicy-bigdata.svg?style=flat)](https://github.com/datawhalechina/juicy-bigdata/stargazers)
[![](https://img.shields.io/github/forks/datawhalechina/juicy-bigdata.svg?style=flat)](https://github.com/datawhalechina/juicy-bigdata/network/members)
[![](https://img.shields.io/github/issues-pr-closed-raw/datawhalechina/juicy-bigdata.svg?style=flat)](https://github.com/datawhalechina/juicy-bigdata/issues)
![](https://img.shields.io/github/repo-size/datawhalechina/juicy-bigdata.svg?style=flat)

本项目《妙趣横生大数据》是Datawhale:whale:大数据技术相关内容的导论课程。

当今的时代处处充斥着大数据的影子，大数据技术也为信息技术发展带来了重大的变革，并深刻影响着人们生活的方方面面。而我们旨在带领大家走进大数据时代的浪潮中，理解并能够使用目前主流的大数据处理架构Hadoop解决相应的问题，从小白到实践者。

## 食用方法

:whale: 推荐使用 [**Big Data Github在线阅读**](https://datawhalechina.github.io/juicy-bigdata/) 进行学习。

**项目结构：**

<pre>
├─docs---------------------------------------------章节主要内容（理论+实验）  
├─experiments--------------------------------------每个章节配套的补充小实验  
└─resources----------------------------------------相关资源  
</pre>

以下是目录，还等什么，学就完事了～

## 大数据处理技术导论目录

* [第一章：大数据概述](https://datawhalechina.github.io/juicy-bigdata/#/ch01-bigdata-introduction)
* [第二章：Hadoop](https://datawhalechina.github.io/juicy-bigdata/#/ch02-Hadoop)
* [第三章：HDFS](https://datawhalechina.github.io/juicy-bigdata/#/ch03-HDFS)
* [第四章：HBase](https://datawhalechina.github.io/juicy-bigdata/#/ch04-HBase)
* [第五章：MapReduce](https://datawhalechina.github.io/juicy-bigdata/#/ch05-MapReduce)
* [第六章：期中大作业](https://datawhalechina.github.io/juicy-bigdata/#/ch06-Homework01)
* [第七章：Hive](https://datawhalechina.github.io/juicy-bigdata/#/ch07-Hive)
* [第八章：Spark](https://datawhalechina.github.io/juicy-bigdata/#/ch08-Spark)
* [第九章：大数据处理技术总结](https://datawhalechina.github.io/juicy-bigdata/#/ch09-bigdata-summary)
* [第十章：期末大作业](https://datawhalechina.github.io/juicy-bigdata/#/ch10-Homework02)

> 第二章以后的章节都配置有实验内容，使用Linux操作系统进行编程实战。

## 课程收获

通过本教程的学习，你将能够学习到：

- 了解并描述大数据技术的应用场景，真实世界的大数据问题和方法的例子。
- 解释大数据4V的特性以及各种特性如何影响到数据的收集、监控、存储、分析和报告的方方面面。
- 识别大数据问题，并能够将问题转化为数据科学问题。
- 总结HDFS文件系统和MapReduce编程模型的特点和意义，以及它们与大数据的关系。
- 识别和利用数据科学生命周期和相关数据流中的各种组件，如HBase，Hive等。

## 参考教程

1. 《大数据处理技术原理与应用 第三版》——林子雨
2. Big Data US SanDiego
3. 《Hadoop权威指南》
4. 《Hive编程指南》
5. 《维度建模权威指南(第3版)》
6. 《大数据处理之道》
7. 《Spark快速大数据分析》
8. 牛客网部分面试题

## 环境安装

- Java 8
- Hadoop 3.3.1
- HBase 2.3.5 or 2.4.8
- Mysql 8.0.32
- Hive 2.3.9
- Spark 3.2.0

**安装包下载地址**：https://datawhale.feishu.cn/drive/folder/fldcnvODsgRWbyqVW9ApavEVEJg   密码: hO38

## 致谢

感谢以下Datawhale成员对项目推进作出的贡献(排名不分先后)：

<table align="center" style="width:80%;">
  <caption><b>贡献者名单</b></caption>
<thead>
  <tr>
    <th>成员</th>
    <th>个人简介及贡献</th>
    <th>个人主页</th>
  </tr>
</thead>
<tbody>
  <tr>
    <td><span style="font-weight:normal;font-style:normal;text-decoration:none">沈豪</span></td>
    <td><span style="font-weight:normal;font-style:normal;text-decoration:none">复旦大学网安博士，项目负责人，参与前五章内容构建</span></td>
    <td>
        <a href="https://www.zhihu.com/people/shenhao-63">知乎</a>,
        <a href="https://github.com/shenhao-stu">Github</a>
    </td>
  </tr>
  <tr>
    <td><span style="font-weight:normal;font-style:normal;text-decoration:none">王洲烽</span></td>
    <td><span style="font-weight:normal;font-style:normal;text-decoration:none">国防科技大学计算机研究生，Datawhale成员，主要贡献者</span></td>
    <td>
        <a href="https://blog.csdn.net/wangzhouf">CSDN</a>,
        <a href="https://github.com/wzfer">Github</a>
    </td>
  </tr>
  <tr>
    <td><span style="font-weight:normal;font-style:normal;text-decoration:none">蒋志政</span></td>
    <td><span style="font-weight:normal;font-style:normal;text-decoration:none">电子科技大学计算机研究生，主要贡献者</span></td>
    <td>
        <a href="https://github.com/gezelligheid0314">Github</a>
    </td>
  </tr>  
  <tr>
    <td><span style="font-weight:normal;font-style:normal;text-decoration:none">王嘉鹏</span></td>
    <td><span style="font-weight:normal;font-style:normal;text-decoration:none">小米大数据开发工程师，Datawhale成员，主要贡献者</span></td>
    <td>
        <a href="https://blog.csdn.net/qq_29027865">CSDN</a>,
        <a href="https://github.com/ditingdapeng">Github</a>
    </td>
  </tr>
  <tr>
    <td><span style="font-weight:normal;font-style:normal;text-decoration:none">刘洋</span></td>
    <td><span style="font-weight:normal;font-style:normal;text-decoration:none">中科院数学与系统科学研究院研究生，Datawhale成员，主要贡献者</span></td>
    <td>
        <a href="https://www.zhihu.com/people/ming-ren-19-34">知乎</a>,
        <a href="https://github.com/liu-yang-maker">Github</a>
    </td>
  </tr>
  <tr>
    <td><span style="font-weight:normal;font-style:normal;text-decoration:none">胡锐锋</span></td>
    <td><span style="font-weight:normal;font-style:normal;text-decoration:none">大数据平台研发工程师，Datawhale成员，主要贡献者</span></td>
    <td>
        <a href="https://github.com/Relph1119">Github</a>
    </td>
  </tr>
</tbody>
</table> 
最后，也感谢伊小雪、毛自翔、萌弟、边圣陶参与本课程的内部评审！


## 关注我们
<div align=center>
<p>扫描下方二维码关注公众号：Datawhale</p>
<img src="images/datawhale_qrcode.jpeg" width = "180" height = "180">
</div>
&emsp;&emsp;Datawhale是一个专注AI领域的开源组织，以“for the learner，和学习者一起成长”为愿景，构建对学习者最有价值的开源学习社区。关注我们，一起学习成长。

## LICENSE
<a rel="license" href="http://creativecommons.org/licenses/by-nc-sa/4.0/"><img alt="知识共享许可协议" style="border-width:0" src="https://img.shields.io/badge/license-CC%20BY--NC--SA%204.0-lightgrey" /></a><br />本作品采用<a rel="license" href="http://creativecommons.org/licenses/by-nc-sa/4.0/">知识共享署名-非商业性使用-相同方式共享 4.0 国际许可协议</a>进行许可。
