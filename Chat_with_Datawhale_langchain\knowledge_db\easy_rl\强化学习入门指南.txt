B站的小伙伴们好
我是蘑菇书一语二语二强化学习教程的作者之一王奇
今天来有给大家带来一个强化学习的入门指南
本次入门指南基于蘑菇书一语二语二强化学习教程
本书的作者目前都是Dell会员成员
也都是数学在读
下面去介绍每个作者
我是王奇
目前就留于中国科研院大学
引用方向是深度学习、静态视觉以及数据挖掘
杨玉云目前就读于清华大学
他的引用方向为
时空数据挖掘、智能冲砍系统以及深度学习
张记目前就读于北京大学
他的引用方向为强化学习记忆人
接下来开始正式的分享
本次分享分为三部分
第一部分
为什么要学强化学习
第二部分
为什么要用本书来学
第三部分
这本书怎么学最高效
首先讲一下为什么要学强化学习
我们先聊一下强化学习的基本概念
强化学习用来学习如何做出一系列好的决策
而人工智能的基本挑战是
学习在不确定的情况下做出好的决策
这边我举个例子
比如你想让一个小孩学会走路
他就需要通过不断尝试来发现
怎么走比较好
怎么走比较快
强化学习的交互过程可以通过这张图来表示
强化学习由智能体和环境两部分组成
在强化学习过程中
智能体与环境一直在交互
智能体在环境中获取某个状态后
它会利用刚刚的状态输出一个动作
这个动作也被称为决策
然后这个动作会被环境中执行
环境会根据智能体采取的动作
来输出下一个状态
以及当前这个动作带来的奖励
整体它的目的呢
就是尽可能的多的
尽可能多的在环境中获取奖励
强化学习的应用非常广泛
比如说我们可以使用强化学习来玩游戏
玩游戏的话可以玩这种电子游戏
也可以玩这种围棋游戏
围棋游戏中比较出名的一个
强化学习的算法就是AlphaGo
此外我们可以使用强化学习
来控制机器人
以及来实现助力交通
另外还可以使用强化学习
来更好地给我们做推进
接下来就到第二部分
也就是为什么要使用本书来学习强化学习
这部分其实也是讲
这个蘑菇书它出版的一些故事
当时我在学习强化学习的时候
搜集了一些资料
然后我发现这些资料
都有点灰色难懂
并不是那么容易地上手
于是我开始到网上
搜索一些公开课来学习
首先我搜索到的是
李宏毅老师的一些公开课
很多人就是入门深度学习
和基学低门课
其实就是李宏毅老师的课
李宏毅老师的课
李宏毅老师的基础学习和深度学习公开课
在编上有很高的播放量
于是我搜索了李宏毅老师的强化学习课
这门课叫顺强化学习
这门课跟以往的李宏毅老师的
课人的风格都是一样的
就是非常的生动有趣
李宏毅老师经常会用
玩亚达利游戏的例子来讲强化学习
这样强化学你就变得通透易懂
然后就很多人都会
把这门课作为自己的强化学习入门课
这边我们念出了一些
就是观众的一些好评
比如说这边观众说
国内这个讲得最好了
然后李宏毅老师讲得真厉害
还有这个评论比较搞笑
这个视频每天晚上都有
几个到十几个人在看
因此我们可以发现
这门课还是非常受人欢迎的
后来我发现李宏毅老师的课
不是那么全面
他的课主要集中于讲解
迅速强化学算法
而忽略了全球强化学算法
以及一些比较前沿的强化学算法
因此我开始
到网上搜索其他的强化学公开课
当时是搜索到了
周博恩老师的强化学纲要
周博恩老师是
人文智能底层的一个顶尖学者
他在人文智能底层会议
亲爱发表了五十余篇学术论文
然后论文的作用量超过一万次
周博恩老师的这门强化学纲要课
就是理论严谨
然后内容丰富
全面的教训介绍了强化学领域
并有相关代码实践
在学习完
女红衣和周博恩老师两门强化学课以后
我发现还是有一点不足
就是代码的实践还偏少
于是我在网上搜索其他的强化学课
当时就搜索了李克强老师的一个
叫试验冠军代理丛林实践强化学习这门课
这门课有个特别突出的优点
就是实战性强
突兵课只能会使用单单的代码来讲解强化学习
这边也有很多好评
有了这三门课以后
我发现这三门课
通过这些公开课来进学的话
有些优点但也有缺点
优点的话就是这些课都是非常经典的一些公开课
然后他们这些课的这些老师
也都是人文领域的一些大流
然后他们的播放量非常高
就比较受欢迎
但是这些课呢也会存在一些问题
就用这些公开课来进学的课会存在一些问题
比如说它不便于实际的查询
然后对于一些重点的知识点它会缺乏一些讲解
此外这些知识点比较分散
就每个老师他讲的知识点各有侧重
此外就是视频的扩弱化比较严重
因此基于这三门公开课
就是我和杨亦远和江静
三个DDR成员
对这些三门课的内容进行整合补充优化
然后写出了这本教材叫
Easy IR 强化域教程
这本教程当时是在
一开始是在Github上发布的
然后Github上呢当时发完以后
就是有很多好评
比如这边
比如说有人的同学就说
昨天非常棒
超越有帮助感谢博主
然后等等等等
然后目前这本书对应的那个Github仓库的
start数已经达48k
然后
它的增长就是它的
start数的一个增长曲线
也是曾经上升的一个态势
后来我们在DataWare举办了一次组队学习
然后组队学习的那个教材呢
就是以我们这本书的
Github的那个在线版
作为一个教材
在组队学习结束以后
然后也是有很多的
来自世界各地的学习的小伙伴
给出好评
比如这边我就
列出了一个小伙伴的好评
然后因为在Github上有很多的读者
会通过issue来给我们反馈
比如说他们会觉得我们来的地方
翻译来电会有些问题
他们会直接列出来
然后我们也会给他们及时的反馈
然后对我们的教程进行优化
后来我们初入以后
有幸得到了七位强行学习领域
大开的亲笔推荐
比如说台湾大学的宁欧英老师
周伯伦老师以及李克强老师
还有比如清华的宁生梦老师
汪峥老师张伟老师
胡玉静老师等等
然后这边我们列出了
宁欧英老师的一个推荐语
因为我觉得宁欧英老师的
推荐语非常的有趣
比如他说
他当时看到我们这个
伊丽艾尔这本书的时候
他第一个想法是
这成员把强化学习的知识整理得真好
不仅有理论说明
还加上了诚实实力
同学们以后可以直接阅读这份教程
这样我以后上课
就不用再讲强化学习的部分了
可以发现宁欧英对我们
这本书还是挺认可的
然后再来讲一个问题
这本书为什么叫蘑菇书呢
难道是因为作者都是吃货
之前有一本西瓜书
还有一本南瓜书
然后作为一个吃货的话
还有一个传承一下叫蘑菇书
就吃完西瓜南瓜再来热蘑菇
但其实并不是
蘑菇书真的寓意是玛里奥
大家如果玩过超级玛里奥游戏
就知道玛里奥吃了蘑菇以后
会变得更加强大
然后我们也希望读者
在吃下这个蘑菇书以后
能够养有兴致的探索强化学习
然后像玛里奥那样越强大
然后进而在人工智能领域里面
获得一些奇葩收获
这边我们放出了一个小彩蛋
就是我们的作者之一杨亦远
跟宁欧英老师的一个意外接触
当时杨亦远在参加一个顶位的时候
发现宁欧英老师也在参会者名单里面
他给宁欧英老师发了封邮件
介绍了我们当时GitHub的开源教程
然后非常惊喜的就是
宁欧英老师很快地给出了回信
然后也是对我们这个教程给出了好评
这边列出了蘑菇书获得的一些荣誉
比如说人民邮件出版社的季度好书
然后日读期期间
当当计算机期中网的第一名
然后上市时间以后
获得的就是京东人工智能榜的第一名
全网的推文阅读量破十万
然后目前清华大学李淑木教授
小米NLP首位科学家王斌老师
以及百度高级研发工程师李克强老师
还有一些等20家的一个大咖公众号
以及微博大V社区他们进行的一个转发推荐
然后这个也被推荐到华为电影大学的一个保定图书馆
蘑菇书全书一共13章可以分两部分
第一部分他介绍了强学的基础知识
以及传统的强化学习算法
第二部分他介绍了
适用强化学习算法
以及常见问题的解决方法
咱们这本书还有一些突出的特点
比如第一点
我们会利用一些简单生动的例子来解释强化学概念
比如说我们可以用这个玩视频游戏
以及下围棋的例子
来对强化学的一些基本概念做出解释
然后还有一些其他特点
比如说我们会对专业的一些公式
进行详细的推导和分析
这边我们以这个比尔曼方程的
这个推导过程的例子
我们会一步一步的把这个推导过程念出来
不会跳步走
然后此外呢
我们还会对一些
可能大家比较难以理解的地方
我们会加入一些注解
通过这些注解
我们会让大家更容易理解一些相关的概念
此外本书配有对应的一个观念词
习题和面试题
当读者读完一章以后
大家可以通过观念词来快速的掌握重点
然后一个通过习题和面试题
来巩固对知识的理解
然后这个习题和面试题
也方便大家的一个程度补缺
然后最后呢
我们会有对应的代码实战
大家学完这个理论以后
还要通过动手来把这个代码进行
来把这个算法进行一个实践
就是大家把这个算法进行实践以后
大家才算对这个算法
有一个比较深入了解
接下来到最后一部分
叫这本书怎么学比较高效
第一个
当然你可以把这本书
作为三门公开课的一个必要教材
就是当你在看这三门课的时候
如果你发现有哪些概念不是很清楚的情况下
你是可以直接翻到
本书对应的知识点进行学习
当然本书也是完全图形于三门教材的
大家可以直接预读本书进行学习
本书在GitHub上面
配有对应的代码
这个代码也会适宜的更新
大家如果只是想很快的
把这个算法给应用上
大家可以直接去GitHub上
跑对应的代码
此外本书还有相关的一个刊物和修订
这些刊物和修订
也会根据大家的反馈的一些意见
进行一些适宜的更新
然后这边也要讲一下
本书它在GitHub有个叫PK版
然后它还有这种纸质书的版本叫纸质版
它们有什么区别呢
就GitHub的那种PK版本是本书的一个初稿
然后在本书的作者已经憋进来
不断的修改中
我们最后有了纸质版
大家看到我们
对这个PK版本里面有了大量的修订
所以说纸质书的一个质量
相对于PK版本是要高不少的
讲到这里大家可能会有疑问
可能是如果我目前我涉及的工作
不涉及到强化学习
那我还有必要学强化学习吗
对于这个问题我想用乔布斯的观点
叫黏生命的点
或者叫应用相连
这边就举一下乔布斯他本人的例子
乔布斯当时在大学学了一门书法课
这门课在很多人眼里都是没有用处的
而乔布斯他本人也是出于兴趣
才学这门课
他也没有对这门课抱有很大的期望
但是后来发现他这门课
在设计苹果电脑字体的时候
取到了极大作用
如果乔布斯当时没有学这门课的话
大家可能就看不到
苹果电脑中这些优美丰富赏意悦目的字体
而对强化学习来说
强化学习这种应用非常广泛的技术
即使现在可能跟你的工作没有很大关联
但说不定某一天
可能你的工作就要涉及到相关的
涉及到强化学习
这时候如果你在之前
对强化学习有一个基本了解
或者说入门的强化学习
这时候可能对你的工作
会起到一个意想不到的助理
最后我们念出了蘑菇书
在京东以及档案的一个购买的链接
以及对应的二维码
然后大家可以在这两个
可以在京东和档案上
购买对应的直述进行学习
然后我们念一下slogan
就是我们这本书的一个标语
标语叫EAR
像柴蘑菇一样轻松入门强化学习
然后这个标语
也代表了我们作者的编写这本书的一个目的
就想让大家学习强化学习的时候
更加轻松
不要像当时作者学习强化学习那样
有很多的困难
然后最后祝大家都能够轻松入门强化学习
然后学习强化学习的过程都很顺利
