# sweetalk-data-structure

&emsp;&emsp;本项目主要基于《大话数据结构》这本书，对该书涉及的线性表、栈与队列、串、树、图、查找与排序进行解读，原书采用C90语言编写，本项目使用C++11、Python3和Java进行代码重写，供大家参考不同语言对数据结构的编写。

&emsp;&emsp;《大话数据结构》这本书，以一个计算机教师教学为场景，讲解数据结构和相关算法的知识。通篇以一种趣味方式来叙述，大量引用了各种各样的生活知识来类比，并充分运用图形语言来体现抽象内容，对数据结构所涉及到的一些经典算法做到逐行分析、多算法比较。与市场上的同类数据结构图书相比，本书内容趣味易读，算法讲解细致深刻，是一本非常适合自学的读物。

## 使用说明

&emsp;&emsp;结合《大话数据结构》这本书，总结了数据结构和算法的知识并配套了相关习题和解答，习题采用C++11、Python3和Java的代码。

&emsp;&emsp;如果觉得本项目中有错误，可以[点击这里](https://github.com/datawhalechina/sweetalk-data-structure/issues)提交你希望补充的内容，我们看到后会尽快进行补充。  

### 在线阅读地址
https://datawhalechina.github.io/sweetalk-data-structure

## 基本信息

* 学习周期：9 天，每天平均花费时间 1 小时-3 小时不等，根据个人学习接受能力强弱有所浮动。
* 学习形式：理论学习 + 练习
* 人群定位：有一定编程基础，对学习数据结构和算法有需求的学员。
* 先修内容：Python、C、Java 等编程语言
* 难度系数：中

## 学习目标

结合《大话数据结构》，通过学习 9 天的打卡学习，掌握线性表、栈与队列、串等知识点，能够独立实现数据结构，并完成相关习题。


## 任务安排

### Task01：学习线性表完成以下三个题目并打卡（3 天）

* [询问学号-简单](https://datawhalechina.github.io/sweetalk-data-structure/#/ch03/ch03?id=_351-p3156-%e3%80%90%e6%b7%b1%e5%9f%ba15%e4%be%8b1%e3%80%91%e8%af%a2%e9%97%ae%e5%ad%a6%e5%8f%b7-%e7%ae%80%e5%8d%95)
* [寄包柜-简单](https://datawhalechina.github.io/sweetalk-data-structure/#/ch03/ch03?id=_352-p3613-%e3%80%90%e6%b7%b1%e5%9f%ba15%e4%be%8b2%e3%80%91%e5%af%84%e5%8c%85%e6%9f%9c-%e7%ae%80%e5%8d%95)
* [校门外的树-简单](https://datawhalechina.github.io/sweetalk-data-structure/#/ch03/ch03?id=_353-p1047-noip2005-%e6%99%ae%e5%8f%8a%e7%bb%84-%e6%a0%a1%e9%97%a8%e5%a4%96%e7%9a%84%e6%a0%91-%e7%ae%80%e5%8d%95)

### Task02：学习栈与队列完成以下三个题目并打卡（3 天）

* [栈-简单](https://datawhalechina.github.io/sweetalk-data-structure/#/ch04/ch04?id=%e4%b9%a0%e9%a2%981-%e6%a0%88%ef%bc%88%e6%b4%9b%e8%b0%b7noip2003%e6%99%ae%e5%8f%8a%e7%bb%84-%e7%ae%80%e5%8d%95%ef%bc%89)
* [后缀表达式-简单](https://datawhalechina.github.io/sweetalk-data-structure/#/ch04/ch04?id=%e4%b9%a0%e9%a2%982-%e5%90%8e%e7%bc%80%e8%a1%a8%e8%be%be%e5%bc%8f%ef%bc%88%e6%b4%9b%e8%b0%b7p149-%e7%ae%80%e5%8d%95%ef%bc%89)
* [滑动窗口的最大值-中等](https://datawhalechina.github.io/sweetalk-data-structure/#/ch04/ch04?id=%e4%b9%a0%e9%a2%983-%e6%bb%91%e5%8a%a8%e7%aa%97%e5%8f%a3%e7%9a%84%e6%9c%80%e5%a4%a7%e5%80%bc%ef%bc%88%e7%89%9b%e5%ae%a2-%e4%b8%ad%e7%ad%89%ef%bc%89)

### Task03：学习串完成以下三个题目并打卡（3 天）

* [字符串语句逆序-简单](https://datawhalechina.github.io/sweetalk-data-structure/#/ch05/ch05?id=%e4%b9%a0%e9%a2%981-%e5%ad%97%e7%ac%a6%e4%b8%b2%e8%af%ad%e5%8f%a5%e9%80%86%e5%ba%8f%ef%bc%88%e7%ae%80%e5%8d%95%ef%bc%89)
* [字符出现情况-简单](https://datawhalechina.github.io/sweetalk-data-structure/#/ch05/ch05?id=%e4%b9%a0%e9%a2%982-%e5%ad%97%e7%ac%a6%e5%87%ba%e7%8e%b0%e6%83%85%e5%86%b5%ef%bc%88%e7%ae%80%e5%8d%95%ef%bc%89)
* [第一个出现两次的字符-简单](https://datawhalechina.github.io/sweetalk-data-structure/#/ch05/ch05?id=%e4%b9%a0%e9%a2%983-%e7%ac%ac%e4%b8%80%e4%b8%aa%e5%87%ba%e7%8e%b0%e4%b8%a4%e6%ac%a1%e7%9a%84%e5%ad%97%e7%ac%a6%ef%bc%88%e7%ae%80%e5%8d%95%ef%bc%89)

### 内容导航

| 章节 | 习题        |
| :----- | :------------ |
| [第一章 数据结构概论](https://datawhalechina.github.io/sweetalk-data-structure/#/ch01/ch01)     | 第一章 习题 |
| [第二章 算法](https://datawhalechina.github.io/sweetalk-data-structure/#/ch02/ch02)     | 第二章 习题 |
| [第三章 线性表](https://datawhalechina.github.io/sweetalk-data-structure/#/ch03/ch03)     | [第三章 习题](https://datawhalechina.github.io/sweetalk-data-structure/#/ch03/ch03?id=_35-%e4%b9%a0%e9%a2%98) |
| [第四章 栈与队列](https://datawhalechina.github.io/sweetalk-data-structure/#/ch04/ch04)     | [第四章 习题](https://datawhalechina.github.io/sweetalk-data-structure/#/ch04/ch04?id=_46-%e4%b9%a0%e9%a2%98)            |
| [第五章 串](https://datawhalechina.github.io/sweetalk-data-structure/#/ch05/ch05)     | [第五章 习题](https://datawhalechina.github.io/sweetalk-data-structure/#/ch05/ch05?id=_56-%e4%b9%a0%e9%a2%98)            |
| [第六章 树](https://datawhalechina.github.io/sweetalk-data-structure/#/ch06/ch06)     | [第六章 习题](https://datawhalechina.github.io/sweetalk-data-structure/#/ch06/ch06?id=_69-%e4%b9%a0%e9%a2%98)            |
| [第七章 图](https://datawhalechina.github.io/sweetalk-data-structure/#/ch07/ch07)     | 第七章 习题 |
| [第八章 查找](https://datawhalechina.github.io/sweetalk-data-structure/#/ch08/ch08)     | 第八章 习题 |
| [第九章 排序](https://datawhalechina.github.io/sweetalk-data-structure/#/ch09/ch09)     | [第九章 习题](https://datawhalechina.github.io/sweetalk-data-structure/#/ch09/ch09?id=_910-%e4%b9%a0%e9%a2%98) |

### 协作规范

1. 采用`markdown`的格式进行编写。
2. 编写完成后，需要进行全部校对审核。
3. 校对过程中，关于数据结构与算法的思想补充，尽量使用初学者能理解的语言。
4. 当前进度

| 章节号 |     标题     |  进度  | 负责人 | 审核人 |
| :------: | :------------: | :------: | :------: | :------: |
|   1   | 数据结构概论 | 已完成 | 李柯辰 |   胡锐锋   |
|   2   |     算法     | 已完成 | 李柯辰 |   胡锐锋   |
|   3   |    线性表    | 已完成 | 李柯辰 |   胡锐锋   |
|   4   |   栈与队列   | 已完成 | 葛云峰 |   胡锐锋   |
|   5   |      串      | 已完成 | 崔腾松 |   胡锐锋   |
|   6   |      树      | 已完成| 安欣锐 |   胡锐锋   |
|   7   |      图      | 已完成 | 李柯辰 |   李柯辰、胡锐锋   |
|   8   |     查找     | 已完成 | 马燕鹏 |   李柯辰、胡锐锋   |
|   9   |     排序     | 已完成 |  王鑫  |   胡锐锋   |


## 致谢

**核心贡献者**

* [李柯辰](https://github.com/Joe-2002)（项目负责人）
* 崔腾松
* 王鑫
* 马燕鹏
* 葛云峰
* 安欣锐

**其他**

1. 特别感谢 [胡锐锋](https://github.com/Relph1119)、[Sm1les](https://github.com/Sm1les) 等对[sweetalk-data-structure](https://github.com/datawhalechina/sweetalk-data-structure)的帮助与支持!
2. 如果有任何想法可以联系我们 DataWhale 也欢迎大家多多提出 issue；
3. 


### 关注我们

<div align=center>
<p>扫描下方二维码关注公众号：Datawhale</p>
<img src="resources/qrcode.jpeg" width = "180" height = "180">
</div>
&emsp;&emsp;Datawhale，一个专注于AI领域的学习圈子。初衷是for the learner，和学习者一起成长。目前加入学习社群的人数已经数千人，组织了机器学习，深度学习，数据分析，数据挖掘，爬虫，编程，统计学，Mysql，数据竞赛等多个领域的内容学习，微信搜索公众号Datawhale可以加入我们。

## LICENSE
<a rel="license" href="http://creativecommons.org/licenses/by-nc-sa/4.0/"><img alt="知识共享许可协议" style="border-width:0" src="https://img.shields.io/badge/license-CC%20BY--NC--SA%204.0-lightgrey" /></a><br />本作品采用<a rel="license" href="http://creativecommons.org/licenses/by-nc-sa/4.0/">知识共享署名-非商业性使用-相同方式共享 4.0 国际许可协议</a>进行许可。
