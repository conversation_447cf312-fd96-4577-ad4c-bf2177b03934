{"cells": [{"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["from langchain.vectorstores import Chroma\n", "from langchain.document_loaders import PyMuPDFLoader\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "from langchain.embeddings.openai import OpenAIEmbeddings\n", "from langchain.embeddings.huggingface import HuggingFaceEmbeddings\n", "from langchain.llms import OpenAI\n", "from langchain.llms import HuggingFacePipeline\n", "from langchain_community.embeddings.sentence_transformer import SentenceTransformerEmbeddings"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["import os\n", "import openai\n", "\n", "openai.api_key  = os.environ['OPENAI_API_KEY']\n", "embedding = SentenceTransformerEmbeddings(model_name=\"moka-ai/m3e-base\")\n", "# embedding = OpenAIEmbeddings()"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["向量库中存储的数量：1215\n"]}], "source": ["persist_directory = '../vector_db/chroma'\n", "vectordb = Chroma(\n", "    persist_directory=persist_directory,\n", "    embedding_function=embedding\n", ")\n", "print(f\"向量库中存储的数量：{vectordb._collection.count()}\")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["检索到的内容数：10\n"]}], "source": ["question=\"llm_universe\"\n", "sim_docs = vectordb.similarity_search(question,k=10)\n", "print(f\"检索到的内容数：{len(sim_docs)}\")"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["检索到的第0个内容: \n", "llm-universe Summary\n", "\n", "这个仓库名是 llm-universe. 这个仓库主要是关于动手学习大模型应用开发的教程，介绍了大模型的简介、API调用、开发流程、数据库搭建、Prompt设计、验证迭代、前后端开发等内容，旨在帮助小白开发者快速掌握大模型应用开发的基础技能。\n", "--------------\n", "检索到的第1个内容: \n", "llms-from-scratch-cn Summary\n", "\n", "这个仓库名是 llms-from-scratch-cn. 这仓库内容主要是一个详细教程，教你如何从头开始实现类似ChatGPT的大语言模型（LLM）。内容涵盖了编码、预训练和微调过程，适合有编程基础的人深入了解LLM工作原理，并愿意从零开始构建和训练自己的LLM。总体而言，这是一个系统化学习路径，强调实践和深入理解模型原理的项目。\n", "--------------\n", "检索到的第2个内容: \n", "llm-research Summary\n", "\n", "这个仓库名是LLM Research，内容主要是建立LLM领域经典论文解读笔记仓库，旨在方便研究人员快速了解大型语言模型（LLM）领域的重要论文，包括TLDR简版和精读版的解读，以帮助研究人员深入学习和掌握LLM领域的前沿知识。同时，项目团队计划在接下来的三到四个月内撰写和整理大量质量过关的论文笔记。\n", "--------------\n", "检索到的第3个内容: \n", "llm-deploy Summary\n", "\n", "这个仓库名是 llm-deploy. 此仓库内容主要是项目介绍、规划和贡献者名单，欢迎参与贡献、反馈问题和交流。涉及项目背景、目录、规划以及贡献者信息等内容。\n", "--------------\n", "检索到的第4个内容: \n", "self-llm Summary\n", "\n", "这个仓库名是 self-llm. 这个仓库内容主要是关于开源大模型食用指南的全流程教程，包括环境配置、部署使用、微调等技能指导，旨在帮助更多人了解和应用开源大模型。\n", "--------------\n", "检索到的第5个内容: \n", "prompt-engineering-for-developers Summary\n", "\n", "这个仓库名是 prompt-engineering-for-developers，主要是一个面向开发者的 LLM 入门教程项目。通过翻译吴恩达老师的大模型系列课程内容，并复现范例代码，指导开发者如何基于 LLM 快速、高效开发具备强大能力的应用程序。包括面向开发者的 Prompt Engineering、搭建基于 ChatGPT 的问答系统、使用 LangChain 开发应用程序等内容。项目旨在帮助具备基础Python能力的开发者入门 LLM。\n", "--------------\n", "检索到的第6个内容: \n", "在本模块，我们将与读者分享提升大语言模型应用效果的各种技巧和最佳实践。书中内容涵盖广泛，包括软件开发提示词设计、文本总结、推理、转换、扩展以及构建聊天机器人等语言模型典型应用场景。我们衷心希望该课程能激发读者的想象力，开发出更出色的语言模型应用。\n", "\n", "随着 LLM 的发展，其大致可以分为两种类型，后续称为基础 LLM 和指令微调（Instruction Tuned）LLM。基础LLM是基于文本训练数据，训练出预测下一个单词能力的模型。其通常通过在互联网和其他来源的大量数据上训练，来确定紧接着出现的最可能的词。例如，如果你以“从前，有一只独角兽”作为 Prompt ，基础 LLM 可能会继续预测“她与独角兽朋友共同生活在一片神奇森林中”。但是，如果你以“法国的首都是什么”为 Prompt ，则基础 LLM 可能会根据互联网上的文章，将回答预测为“法国最大的城市是什么？法国的人口是多少？”，因为互联网上的文章很可能是有关法国国家的问答题目列表。\n", "--------------\n", "检索到的第7个内容: \n", "[1] Wikipedia contributors. Laplacian matrix, 2020.\n", "[2] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Learning\n", "with local and global consistency. Advances in neural information processing systems, 16, 2003.\n", "→_→\n", "配套视频教程：https://www.bilibili.com/video/BV1Mh411e7VU\n", "←_←\n", "--------------\n", "检索到的第8个内容: \n", "hugging-sd <PERSON><PERSON>ry\n", "\n", "这个仓库名是 hugging-sd. 这个仓库内容主要介绍了以stable-diffusion为代表的视觉生成大模型的原理、使用和应用，旨在让更多非专业人士了解和使用视觉生成大模型，以促进行业发展并提高人们的工作效率和生活质量。项目包括二维生成、三维生成、视频生成等内容，提供了算法原理、使用指南、示例代码等，适合学生和相关行业从业者。\n", "--------------\n", "检索到的第9个内容: \n", "so-large-lm Summary\n", "\n", "这个仓库名是 so-large-lm. 这仓库内容主要是关于大规模预训练语言模型的教程，涵盖数据准备、模型构建、训练策略、模型评估与改进，以及大模型在安全、隐私、环境和法律道德方面的综合知识分享。项目旨在为人工智能、自然语言处理和机器学习领域的学者、研究者和从业者提供全面深入的理论与实践方法。\n", "--------------\n"]}], "source": ["for i, sim_doc in enumerate(sim_docs):\n", "    print(f\"检索到的第{i}个内容: \\n{sim_doc.page_content}\", end=\"\\n--------------\\n\")"]}], "metadata": {"kernelspec": {"display_name": "llm_universe", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 2}