{"text": "B站的小伙伴们好我是蘑菇书一语二语二强化学习教程的作者之一王奇今天来有给大家带来一个强化学习的入门指南本次入门指南基于蘑菇书一语二语二强化学习教程本书的作者目前都是Dell会员成员也都是数学在读下面去介绍每个作者我是王奇目前就留于中国科研院大学引用方向是深度学习、静态视觉以及数据挖掘杨玉云目前就读于清华大学他的引用方向为时空数据挖掘、智能冲砍系统以及深度学习张记目前就读于北京大学他的引用方向为强化学习记忆人接下来开始正式的分享本次分享分为三部分第一部分为什么要学强化学习第二部分为什么要用本书来学第三部分这本书怎么学最高效首先讲一下为什么要学强化学习我们先聊一下强化学习的基本概念强化学习用来学习如何做出一系列好的决策而人工智能的基本挑战是学习在不确定的情况下做出好的决策这边我举个例子比如你想让一个小孩学会走路他就需要通过不断尝试来发现怎么走比较好怎么走比较快强化学习的交互过程可以通过这张图来表示强化学习由智能体和环境两部分组成在强化学习过程中智能体与环境一直在交互智能体在环境中获取某个状态后它会利用刚刚的状态输出一个动作这个动作也被称为决策然后这个动作会被环境中执行环境会根据智能体采取的动作来输出下一个状态以及当前这个动作带来的奖励整体它的目的呢就是尽可能的多的尽可能多的在环境中获取奖励强化学习的应用非常广泛比如说我们可以使用强化学习来玩游戏玩游戏的话可以玩这种电子游戏也可以玩这种围棋游戏围棋游戏中比较出名的一个强化学习的算法就是AlphaGo此外我们可以使用强化学习来控制机器人以及来实现助力交通另外还可以使用强化学习来更好地给我们做推进接下来就到第二部分也就是为什么要使用本书来学习强化学习这部分其实也是讲这个蘑菇书它出版的一些故事当时我在学习强化学习的时候搜集了一些资料然后我发现这些资料都有点灰色难懂并不是那么容易地上手于是我开始到网上搜索一些公开课来学习首先我搜索到的是李宏毅老师的一些公开课很多人就是入门深度学习和基学低门课其实就是李宏毅老师的课李宏毅老师的课李宏毅老师的基础学习和深度学习公开课在编上有很高的播放量于是我搜索了李宏毅老师的强化学习课这门课叫顺强化学习这门课跟以往的李宏毅老师的课人的风格都是一样的就是非常的生动有趣李宏毅老师经常会用玩亚达利游戏的例子来讲强化学习这样强化学你就变得通透易懂然后就很多人都会把这门课作为自己的强化学习入门课这边我们念出了一些就是观众的一些好评比如说这边观众说国内这个讲得最好了然后李宏毅老师讲得真厉害还有这个评论比较搞笑这个视频每天晚上都有几个到十几个人在看因此我们可以发现这门课还是非常受人欢迎的后来我发现李宏毅老师的课不是那么全面他的课主要集中于讲解迅速强化学算法而忽略了全球强化学算法以及一些比较前沿的强化学算法因此我开始到网上搜索其他的强化学公开课当时是搜索到了周博恩老师的强化学纲要周博恩老师是人文智能底层的一个顶尖学者他在人文智能底层会议亲爱发表了五十余篇学术论文然后论文的作用量超过一万次周博恩老师的这门强化学纲要课就是理论严谨然后内容丰富全面的教训介绍了强化学领域并有相关代码实践在学习完女红衣和周博恩老师两门强化学课以后我发现还是有一点不足就是代码的实践还偏少于是我在网上搜索其他的强化学课当时就搜索了李克强老师的一个叫试验冠军代理丛林实践强化学习这门课这门课有个特别突出的优点就是实战性强突兵课只能会使用单单的代码来讲解强化学习这边也有很多好评有了这三门课以后我发现这三门课通过这些公开课来进学的话有些优点但也有缺点优点的话就是这些课都是非常经典的一些公开课然后他们这些课的这些老师也都是人文领域的一些大流然后他们的播放量非常高就比较受欢迎但是这些课呢也会存在一些问题就用这些公开课来进学的课会存在一些问题比如说它不便于实际的查询然后对于一些重点的知识点它会缺乏一些讲解此外这些知识点比较分散就每个老师他讲的知识点各有侧重此外就是视频的扩弱化比较严重因此基于这三门公开课就是我和杨亦远和江静三个DDR成员对这些三门课的内容进行整合补充优化然后写出了这本教材叫Easy IR 强化域教程这本教程当时是在一开始是在Github上发布的然后Github上呢当时发完以后就是有很多好评比如这边比如说有人的同学就说昨天非常棒超越有帮助感谢博主然后等等等等然后目前这本书对应的那个Github仓库的start数已经达48k然后它的增长就是它的start数的一个增长曲线也是曾经上升的一个态势后来我们在DataWare举办了一次组队学习然后组队学习的那个教材呢就是以我们这本书的Github的那个在线版作为一个教材在组队学习结束以后然后也是有很多的来自世界各地的学习的小伙伴给出好评比如这边我就列出了一个小伙伴的好评然后因为在Github上有很多的读者会通过issue来给我们反馈比如说他们会觉得我们来的地方翻译来电会有些问题他们会直接列出来然后我们也会给他们及时的反馈然后对我们的教程进行优化后来我们初入以后有幸得到了七位强行学习领域大开的亲笔推荐比如说台湾大学的宁欧英老师周伯伦老师以及李克强老师还有比如清华的宁生梦老师汪峥老师张伟老师胡玉静老师等等然后这边我们列出了宁欧英老师的一个推荐语因为我觉得宁欧英老师的推荐语非常的有趣比如他说他当时看到我们这个伊丽艾尔这本书的时候他第一个想法是这成员把强化学习的知识整理得真好不仅有理论说明还加上了诚实实力同学们以后可以直接阅读这份教程这样我以后上课就不用再讲强化学习的部分了可以发现宁欧英对我们这本书还是挺认可的然后再来讲一个问题这本书为什么叫蘑菇书呢难道是因为作者都是吃货之前有一本西瓜书还有一本南瓜书然后作为一个吃货的话还有一个传承一下叫蘑菇书就吃完西瓜南瓜再来热蘑菇但其实并不是蘑菇书真的寓意是玛里奥大家如果玩过超级玛里奥游戏就知道玛里奥吃了蘑菇以后会变得更加强大然后我们也希望读者在吃下这个蘑菇书以后能够养有兴致的探索强化学习然后像玛里奥那样越强大然后进而在人工智能领域里面获得一些奇葩收获这边我们放出了一个小彩蛋就是我们的作者之一杨亦远跟宁欧英老师的一个意外接触当时杨亦远在参加一个顶位的时候发现宁欧英老师也在参会者名单里面他给宁欧英老师发了封邮件介绍了我们当时GitHub的开源教程然后非常惊喜的就是宁欧英老师很快地给出了回信然后也是对我们这个教程给出了好评这边列出了蘑菇书获得的一些荣誉比如说人民邮件出版社的季度好书然后日读期期间当当计算机期中网的第一名然后上市时间以后获得的就是京东人工智能榜的第一名全网的推文阅读量破十万然后目前清华大学李淑木教授小米NLP首位科学家王斌老师以及百度高级研发工程师李克强老师还有一些等20家的一个大咖公众号以及微博大V社区他们进行的一个转发推荐然后这个也被推荐到华为电影大学的一个保定图书馆蘑菇书全书一共13章可以分两部分第一部分他介绍了强学的基础知识以及传统的强化学习算法第二部分他介绍了适用强化学习算法以及常见问题的解决方法咱们这本书还有一些突出的特点比如第一点我们会利用一些简单生动的例子来解释强化学概念比如说我们可以用这个玩视频游戏以及下围棋的例子来对强化学的一些基本概念做出解释然后还有一些其他特点比如说我们会对专业的一些公式进行详细的推导和分析这边我们以这个比尔曼方程的这个推导过程的例子我们会一步一步的把这个推导过程念出来不会跳步走然后此外呢我们还会对一些可能大家比较难以理解的地方我们会加入一些注解通过这些注解我们会让大家更容易理解一些相关的概念此外本书配有对应的一个观念词习题和面试题当读者读完一章以后大家可以通过观念词来快速的掌握重点然后一个通过习题和面试题来巩固对知识的理解然后这个习题和面试题也方便大家的一个程度补缺然后最后呢我们会有对应的代码实战大家学完这个理论以后还要通过动手来把这个代码进行来把这个算法进行一个实践就是大家把这个算法进行实践以后大家才算对这个算法有一个比较深入了解接下来到最后一部分叫这本书怎么学比较高效第一个当然你可以把这本书作为三门公开课的一个必要教材就是当你在看这三门课的时候如果你发现有哪些概念不是很清楚的情况下你是可以直接翻到本书对应的知识点进行学习当然本书也是完全图形于三门教材的大家可以直接预读本书进行学习本书在GitHub上面配有对应的代码这个代码也会适宜的更新大家如果只是想很快的把这个算法给应用上大家可以直接去GitHub上跑对应的代码此外本书还有相关的一个刊物和修订这些刊物和修订也会根据大家的反馈的一些意见进行一些适宜的更新然后这边也要讲一下本书它在GitHub有个叫PK版然后它还有这种纸质书的版本叫纸质版它们有什么区别呢就GitHub的那种PK版本是本书的一个初稿然后在本书的作者已经憋进来不断的修改中我们最后有了纸质版大家看到我们对这个PK版本里面有了大量的修订所以说纸质书的一个质量相对于PK版本是要高不少的讲到这里大家可能会有疑问可能是如果我目前我涉及的工作不涉及到强化学习那我还有必要学强化学习吗对于这个问题我想用乔布斯的观点叫黏生命的点或者叫应用相连这边就举一下乔布斯他本人的例子乔布斯当时在大学学了一门书法课这门课在很多人眼里都是没有用处的而乔布斯他本人也是出于兴趣才学这门课他也没有对这门课抱有很大的期望但是后来发现他这门课在设计苹果电脑字体的时候取到了极大作用如果乔布斯当时没有学这门课的话大家可能就看不到苹果电脑中这些优美丰富赏意悦目的字体而对强化学习来说强化学习这种应用非常广泛的技术即使现在可能跟你的工作没有很大关联但说不定某一天可能你的工作就要涉及到相关的涉及到强化学习这时候如果你在之前对强化学习有一个基本了解或者说入门的强化学习这时候可能对你的工作会起到一个意想不到的助理最后我们念出了蘑菇书在京东以及档案的一个购买的链接以及对应的二维码然后大家可以在这两个可以在京东和档案上购买对应的直述进行学习然后我们念一下slogan就是我们这本书的一个标语标语叫EAR像柴蘑菇一样轻松入门强化学习然后这个标语也代表了我们作者的编写这本书的一个目的就想让大家学习强化学习的时候更加轻松不要像当时作者学习强化学习那样有很多的困难然后最后祝大家都能够轻松入门强化学习然后学习强化学习的过程都很顺利", "segments": [{"id": 0, "seek": 0, "start": 1.0, "end": 3.0, "text": "B站的小伙伴们好", "tokens": [50414, 33, 34155, 1546, 7322, 7384, 247, 7384, 112, 9497, 2131, 50514], "temperature": 0.0, "avg_logprob": -0.17060546385936248, "compression_ratio": 1.4710743801652892, "no_speech_prob": 0.050269726663827896}, {"id": 1, "seek": 0, "start": 3.0, "end": 7.0, "text": "我是蘑菇书一语二语二强化学习教程的作者之一王奇", "tokens": [50514, 15914, 31882, 239, 16616, 229, 2930, 99, 2257, 5233, 255, 11217, 5233, 255, 11217, 5702, 118, 23756, 29618, 2930, 254, 21936, 29649, 1546, 11914, 12444, 9574, 2257, 19940, 26907, 50714], "temperature": 0.0, "avg_logprob": -0.17060546385936248, "compression_ratio": 1.4710743801652892, "no_speech_prob": 0.050269726663827896}, {"id": 2, "seek": 0, "start": 7.0, "end": 11.0, "text": "今天来有给大家带来一个强化学习的入门指南", "tokens": [50714, 12074, 6912, 2412, 23197, 6868, 4845, 99, 6912, 20182, 5702, 118, 23756, 29618, 2930, 254, 1546, 14028, 8259, 101, 25922, 31021, 50914], "temperature": 0.0, "avg_logprob": -0.17060546385936248, "compression_ratio": 1.4710743801652892, "no_speech_prob": 0.050269726663827896}, {"id": 3, "seek": 0, "start": 13.0, "end": 18.0, "text": "本次入门指南基于蘑菇书一语二语二强化学习教程", "tokens": [51014, 8802, 9487, 14028, 8259, 101, 25922, 31021, 26008, 37732, 31882, 239, 16616, 229, 2930, 99, 2257, 5233, 255, 11217, 5233, 255, 11217, 5702, 118, 23756, 29618, 2930, 254, 21936, 29649, 51264], "temperature": 0.0, "avg_logprob": -0.17060546385936248, "compression_ratio": 1.4710743801652892, "no_speech_prob": 0.050269726663827896}, {"id": 4, "seek": 0, "start": 18.0, "end": 20.0, "text": "本书的作者目前都是Dell会员成员", "tokens": [51264, 8802, 2930, 99, 1546, 11914, 12444, 39004, 22796, 35, 898, 12949, 3606, 246, 11336, 3606, 246, 51364], "temperature": 0.0, "avg_logprob": -0.17060546385936248, "compression_ratio": 1.4710743801652892, "no_speech_prob": 0.050269726663827896}, {"id": 5, "seek": 0, "start": 20.0, "end": 22.0, "text": "也都是数学在读", "tokens": [51364, 6404, 22796, 33188, 29618, 3581, 5233, 119, 51464], "temperature": 0.0, "avg_logprob": -0.17060546385936248, "compression_ratio": 1.4710743801652892, "no_speech_prob": 0.050269726663827896}, {"id": 6, "seek": 0, "start": 22.0, "end": 24.0, "text": "下面去介绍每个作者", "tokens": [51464, 47150, 6734, 30312, 10115, 235, 23664, 7549, 11914, 12444, 51564], "temperature": 0.0, "avg_logprob": -0.17060546385936248, "compression_ratio": 1.4710743801652892, "no_speech_prob": 0.050269726663827896}, {"id": 7, "seek": 0, "start": 26.0, "end": 27.0, "text": "我是王奇", "tokens": [51664, 15914, 19940, 26907, 51714], "temperature": 0.0, "avg_logprob": -0.17060546385936248, "compression_ratio": 1.4710743801652892, "no_speech_prob": 0.050269726663827896}, {"id": 8, "seek": 0, "start": 27.0, "end": 29.0, "text": "目前就留于中国科研院大学", "tokens": [51714, 39004, 3111, 24456, 37732, 38165, 42091, 23230, 242, 38358, 3582, 29618, 51814], "temperature": 0.0, "avg_logprob": -0.17060546385936248, "compression_ratio": 1.4710743801652892, "no_speech_prob": 0.050269726663827896}, {"id": 9, "seek": 2900, "start": 29.0, "end": 33.0, "text": "引用方向是深度学习、静态视觉以及数据挖掘", "tokens": [50364, 41301, 9254, 9249, 24282, 1541, 24043, 13127, 29618, 2930, 254, 1231, 5363, 247, 3757, 223, 40656, 24447, 40282, 33188, 26075, 106, 8501, 244, 6900, 246, 50564], "temperature": 0.0, "avg_logprob": -0.1471697782215319, "compression_ratio": 1.5454545454545454, "no_speech_prob": 0.0019266814924776554}, {"id": 10, "seek": 2900, "start": 36.0, "end": 38.0, "text": "杨玉云目前就读于清华大学", "tokens": [50714, 4422, 101, 8051, 231, 1369, 239, 39004, 3111, 5233, 119, 37732, 21784, 5322, 236, 3582, 29618, 50814], "temperature": 0.0, "avg_logprob": -0.1471697782215319, "compression_ratio": 1.5454545454545454, "no_speech_prob": 0.0019266814924776554}, {"id": 11, "seek": 2900, "start": 38.0, "end": 39.0, "text": "他的引用方向为", "tokens": [50814, 31309, 41301, 9254, 9249, 24282, 13992, 50864], "temperature": 0.0, "avg_logprob": -0.1471697782215319, "compression_ratio": 1.5454545454545454, "no_speech_prob": 0.0019266814924776554}, {"id": 12, "seek": 2900, "start": 39.0, "end": 43.0, "text": "时空数据挖掘、智能冲砍系统以及深度学习", "tokens": [50864, 15729, 23322, 33188, 26075, 106, 8501, 244, 6900, 246, 1231, 5094, 118, 8225, 5676, 110, 23230, 235, 25368, 10115, 253, 40282, 24043, 13127, 29618, 2930, 254, 51064], "temperature": 0.0, "avg_logprob": -0.1471697782215319, "compression_ratio": 1.5454545454545454, "no_speech_prob": 0.0019266814924776554}, {"id": 13, "seek": 2900, "start": 44.0, "end": 46.0, "text": "张记目前就读于北京大学", "tokens": [51114, 44059, 34756, 39004, 3111, 5233, 119, 37732, 26668, 31375, 3582, 29618, 51214], "temperature": 0.0, "avg_logprob": -0.1471697782215319, "compression_ratio": 1.5454545454545454, "no_speech_prob": 0.0019266814924776554}, {"id": 14, "seek": 2900, "start": 46.0, "end": 49.0, "text": "他的引用方向为强化学习记忆人", "tokens": [51214, 31309, 41301, 9254, 9249, 24282, 13992, 5702, 118, 23756, 29618, 2930, 254, 34756, 4263, 228, 4035, 51364], "temperature": 0.0, "avg_logprob": -0.1471697782215319, "compression_ratio": 1.5454545454545454, "no_speech_prob": 0.0019266814924776554}, {"id": 15, "seek": 2900, "start": 52.0, "end": 54.0, "text": "接下来开始正式的分享", "tokens": [51514, 14468, 4438, 6912, 45213, 15789, 27584, 1546, 30855, 51614], "temperature": 0.0, "avg_logprob": -0.1471697782215319, "compression_ratio": 1.5454545454545454, "no_speech_prob": 0.0019266814924776554}, {"id": 16, "seek": 2900, "start": 54.0, "end": 55.0, "text": "本次分享分为三部分", "tokens": [51614, 8802, 9487, 30855, 6627, 13992, 10960, 32174, 51664], "temperature": 0.0, "avg_logprob": -0.1471697782215319, "compression_ratio": 1.5454545454545454, "no_speech_prob": 0.0019266814924776554}, {"id": 17, "seek": 2900, "start": 55.0, "end": 56.0, "text": "第一部分", "tokens": [51664, 18049, 32174, 51714], "temperature": 0.0, "avg_logprob": -0.1471697782215319, "compression_ratio": 1.5454545454545454, "no_speech_prob": 0.0019266814924776554}, {"id": 18, "seek": 2900, "start": 56.0, "end": 57.0, "text": "为什么要学强化学习", "tokens": [51714, 31006, 4275, 29618, 5702, 118, 23756, 29618, 2930, 254, 51764], "temperature": 0.0, "avg_logprob": -0.1471697782215319, "compression_ratio": 1.5454545454545454, "no_speech_prob": 0.0019266814924776554}, {"id": 19, "seek": 2900, "start": 57.0, "end": 58.0, "text": "第二部分", "tokens": [51764, 19693, 32174, 51814], "temperature": 0.0, "avg_logprob": -0.1471697782215319, "compression_ratio": 1.5454545454545454, "no_speech_prob": 0.0019266814924776554}, {"id": 20, "seek": 5800, "start": 58.0, "end": 60.0, "text": "为什么要用本书来学", "tokens": [50364, 31006, 4275, 9254, 8802, 2930, 99, 6912, 29618, 50464], "temperature": 0.0, "avg_logprob": -0.10798361271987726, "compression_ratio": 1.4729241877256318, "no_speech_prob": 0.0006771791959181428}, {"id": 21, "seek": 5800, "start": 60.0, "end": 61.0, "text": "第三部分", "tokens": [50464, 35878, 32174, 50514], "temperature": 0.0, "avg_logprob": -0.10798361271987726, "compression_ratio": 1.4729241877256318, "no_speech_prob": 0.0006771791959181428}, {"id": 22, "seek": 5800, "start": 61.0, "end": 62.0, "text": "这本书怎么学最高效", "tokens": [50514, 5562, 8802, 2930, 99, 15282, 29618, 8661, 12979, 43076, 50564], "temperature": 0.0, "avg_logprob": -0.10798361271987726, "compression_ratio": 1.4729241877256318, "no_speech_prob": 0.0006771791959181428}, {"id": 23, "seek": 5800, "start": 64.0, "end": 66.0, "text": "首先讲一下为什么要学强化学习", "tokens": [50664, 36490, 39255, 8861, 31006, 4275, 29618, 5702, 118, 23756, 29618, 2930, 254, 50764], "temperature": 0.0, "avg_logprob": -0.10798361271987726, "compression_ratio": 1.4729241877256318, "no_speech_prob": 0.0006771791959181428}, {"id": 24, "seek": 5800, "start": 69.0, "end": 71.0, "text": "我们先聊一下强化学习的基本概念", "tokens": [50914, 15003, 10108, 40096, 8861, 5702, 118, 23756, 29618, 2930, 254, 1546, 37946, 26181, 33679, 51014], "temperature": 0.0, "avg_logprob": -0.10798361271987726, "compression_ratio": 1.4729241877256318, "no_speech_prob": 0.0006771791959181428}, {"id": 25, "seek": 5800, "start": 71.0, "end": 75.0, "text": "强化学习用来学习如何做出一系列好的决策", "tokens": [51014, 5702, 118, 23756, 29618, 2930, 254, 9254, 6912, 29618, 2930, 254, 43526, 10907, 7781, 2257, 25368, 43338, 20715, 5676, 111, 7973, 244, 51214], "temperature": 0.0, "avg_logprob": -0.10798361271987726, "compression_ratio": 1.4729241877256318, "no_speech_prob": 0.0006771791959181428}, {"id": 26, "seek": 5800, "start": 75.0, "end": 77.0, "text": "而人工智能的基本挑战是", "tokens": [51214, 11070, 4035, 23323, 5094, 118, 8225, 1546, 37946, 49067, 1486, 246, 1541, 51314], "temperature": 0.0, "avg_logprob": -0.10798361271987726, "compression_ratio": 1.4729241877256318, "no_speech_prob": 0.0006771791959181428}, {"id": 27, "seek": 5800, "start": 77.0, "end": 80.0, "text": "学习在不确定的情况下做出好的决策", "tokens": [51314, 29618, 2930, 254, 3581, 1960, 38114, 106, 12088, 1546, 46514, 4438, 10907, 7781, 20715, 5676, 111, 7973, 244, 51464], "temperature": 0.0, "avg_logprob": -0.10798361271987726, "compression_ratio": 1.4729241877256318, "no_speech_prob": 0.0006771791959181428}, {"id": 28, "seek": 5800, "start": 80.0, "end": 81.0, "text": "这边我举个例子", "tokens": [51464, 5562, 40503, 1654, 940, 122, 7549, 17797, 7626, 51514], "temperature": 0.0, "avg_logprob": -0.10798361271987726, "compression_ratio": 1.4729241877256318, "no_speech_prob": 0.0006771791959181428}, {"id": 29, "seek": 5800, "start": 81.0, "end": 83.0, "text": "比如你想让一个小孩学会走路", "tokens": [51514, 36757, 38386, 33650, 20182, 7322, 36269, 29618, 12949, 9575, 24658, 51614], "temperature": 0.0, "avg_logprob": -0.10798361271987726, "compression_ratio": 1.4729241877256318, "no_speech_prob": 0.0006771791959181428}, {"id": 30, "seek": 5800, "start": 83.0, "end": 86.0, "text": "他就需要通过不断尝试来发现", "tokens": [51614, 5000, 3111, 35748, 19550, 16866, 1960, 4307, 255, 1530, 251, 5233, 243, 6912, 28926, 20204, 51764], "temperature": 0.0, "avg_logprob": -0.10798361271987726, "compression_ratio": 1.4729241877256318, "no_speech_prob": 0.0006771791959181428}, {"id": 31, "seek": 5800, "start": 86.0, "end": 87.0, "text": "怎么走比较好", "tokens": [51764, 15282, 9575, 11706, 9830, 225, 2131, 51814], "temperature": 0.0, "avg_logprob": -0.10798361271987726, "compression_ratio": 1.4729241877256318, "no_speech_prob": 0.0006771791959181428}, {"id": 32, "seek": 8700, "start": 87.0, "end": 88.0, "text": "怎么走比较快", "tokens": [50364, 15282, 9575, 11706, 9830, 225, 10251, 50414], "temperature": 0.0, "avg_logprob": -0.08408290840858637, "compression_ratio": 1.5432098765432098, "no_speech_prob": 0.0046816859394311905}, {"id": 33, "seek": 8700, "start": 91.0, "end": 94.0, "text": "强化学习的交互过程可以通过这张图来表示", "tokens": [50564, 5702, 118, 23756, 29618, 2930, 254, 1546, 28455, 1369, 240, 16866, 29649, 6723, 19550, 16866, 5562, 44059, 3919, 122, 6912, 40053, 50714], "temperature": 0.0, "avg_logprob": -0.08408290840858637, "compression_ratio": 1.5432098765432098, "no_speech_prob": 0.0046816859394311905}, {"id": 34, "seek": 8700, "start": 94.0, "end": 97.0, "text": "强化学习由智能体和环境两部分组成", "tokens": [50714, 5702, 118, 23756, 29618, 2930, 254, 23786, 5094, 118, 8225, 29485, 12565, 8051, 107, 47885, 36257, 32174, 10115, 226, 11336, 50864], "temperature": 0.0, "avg_logprob": -0.08408290840858637, "compression_ratio": 1.5432098765432098, "no_speech_prob": 0.0046816859394311905}, {"id": 35, "seek": 8700, "start": 97.0, "end": 98.0, "text": "在强化学习过程中", "tokens": [50864, 3581, 5702, 118, 23756, 29618, 2930, 254, 16866, 29649, 5975, 50914], "temperature": 0.0, "avg_logprob": -0.08408290840858637, "compression_ratio": 1.5432098765432098, "no_speech_prob": 0.0046816859394311905}, {"id": 36, "seek": 8700, "start": 98.0, "end": 100.0, "text": "智能体与环境一直在交互", "tokens": [50914, 5094, 118, 8225, 29485, 940, 236, 8051, 107, 47885, 34448, 3581, 28455, 1369, 240, 51014], "temperature": 0.0, "avg_logprob": -0.08408290840858637, "compression_ratio": 1.5432098765432098, "no_speech_prob": 0.0046816859394311905}, {"id": 37, "seek": 8700, "start": 101.0, "end": 104.0, "text": "智能体在环境中获取某个状态后", "tokens": [51064, 5094, 118, 8225, 29485, 3581, 8051, 107, 47885, 5975, 31127, 115, 29436, 17238, 238, 7549, 35276, 114, 3757, 223, 13547, 51214], "temperature": 0.0, "avg_logprob": -0.08408290840858637, "compression_ratio": 1.5432098765432098, "no_speech_prob": 0.0046816859394311905}, {"id": 38, "seek": 8700, "start": 104.0, "end": 106.0, "text": "它会利用刚刚的状态输出一个动作", "tokens": [51214, 11284, 12949, 23700, 9254, 49160, 49160, 1546, 35276, 114, 3757, 223, 9830, 241, 7781, 20182, 34961, 11914, 51314], "temperature": 0.0, "avg_logprob": -0.08408290840858637, "compression_ratio": 1.5432098765432098, "no_speech_prob": 0.0046816859394311905}, {"id": 39, "seek": 8700, "start": 106.0, "end": 108.0, "text": "这个动作也被称为决策", "tokens": [51314, 15368, 34961, 11914, 6404, 23238, 8204, 108, 13992, 5676, 111, 7973, 244, 51414], "temperature": 0.0, "avg_logprob": -0.08408290840858637, "compression_ratio": 1.5432098765432098, "no_speech_prob": 0.0046816859394311905}, {"id": 40, "seek": 8700, "start": 108.0, "end": 112.0, "text": "然后这个动作会被环境中执行", "tokens": [51414, 26636, 15368, 34961, 11914, 12949, 23238, 8051, 107, 47885, 5975, 3416, 100, 8082, 51614], "temperature": 0.0, "avg_logprob": -0.08408290840858637, "compression_ratio": 1.5432098765432098, "no_speech_prob": 0.0046816859394311905}, {"id": 41, "seek": 8700, "start": 112.0, "end": 115.0, "text": "环境会根据智能体采取的动作", "tokens": [51614, 8051, 107, 47885, 12949, 31337, 26075, 106, 5094, 118, 8225, 29485, 5873, 229, 29436, 1546, 34961, 11914, 51764], "temperature": 0.0, "avg_logprob": -0.08408290840858637, "compression_ratio": 1.5432098765432098, "no_speech_prob": 0.0046816859394311905}, {"id": 42, "seek": 11500, "start": 115.0, "end": 117.0, "text": "来输出下一个状态", "tokens": [50364, 6912, 9830, 241, 7781, 4438, 20182, 35276, 114, 3757, 223, 50464], "temperature": 0.0, "avg_logprob": -0.09532012512434775, "compression_ratio": 1.3963133640552996, "no_speech_prob": 0.0356743186712265}, {"id": 43, "seek": 11500, "start": 117.0, "end": 120.0, "text": "以及当前这个动作带来的奖励", "tokens": [50464, 40282, 16233, 8945, 15368, 34961, 11914, 4845, 99, 6912, 1546, 1881, 244, 5087, 109, 50614], "temperature": 0.0, "avg_logprob": -0.09532012512434775, "compression_ratio": 1.3963133640552996, "no_speech_prob": 0.0356743186712265}, {"id": 44, "seek": 11500, "start": 120.0, "end": 121.0, "text": "整体它的目的呢", "tokens": [50614, 27662, 29485, 45224, 11386, 1546, 6240, 50664], "temperature": 0.0, "avg_logprob": -0.09532012512434775, "compression_ratio": 1.3963133640552996, "no_speech_prob": 0.0356743186712265}, {"id": 45, "seek": 11500, "start": 121.0, "end": 124.0, "text": "就是尽可能的多的", "tokens": [50664, 5620, 1530, 121, 16657, 1546, 6392, 1546, 50814], "temperature": 0.0, "avg_logprob": -0.09532012512434775, "compression_ratio": 1.3963133640552996, "no_speech_prob": 0.0356743186712265}, {"id": 46, "seek": 11500, "start": 125.0, "end": 128.0, "text": "尽可能多的在环境中获取奖励", "tokens": [50864, 1530, 121, 16657, 6392, 1546, 3581, 8051, 107, 47885, 5975, 31127, 115, 29436, 1881, 244, 5087, 109, 51014], "temperature": 0.0, "avg_logprob": -0.09532012512434775, "compression_ratio": 1.3963133640552996, "no_speech_prob": 0.0356743186712265}, {"id": 47, "seek": 11500, "start": 135.0, "end": 136.0, "text": "强化学习的应用非常广泛", "tokens": [51364, 5702, 118, 23756, 29618, 2930, 254, 1546, 44297, 9254, 14392, 3509, 123, 6847, 249, 51414], "temperature": 0.0, "avg_logprob": -0.09532012512434775, "compression_ratio": 1.3963133640552996, "no_speech_prob": 0.0356743186712265}, {"id": 48, "seek": 11500, "start": 136.0, "end": 139.0, "text": "比如说我们可以使用强化学习来玩游戏", "tokens": [51414, 36757, 8090, 15003, 6723, 22982, 9254, 5702, 118, 23756, 29618, 2930, 254, 6912, 19912, 9592, 116, 1486, 237, 51564], "temperature": 0.0, "avg_logprob": -0.09532012512434775, "compression_ratio": 1.3963133640552996, "no_speech_prob": 0.0356743186712265}, {"id": 49, "seek": 11500, "start": 139.0, "end": 141.0, "text": "玩游戏的话可以玩这种电子游戏", "tokens": [51564, 19912, 9592, 116, 1486, 237, 44575, 6723, 19912, 5562, 39810, 42182, 7626, 9592, 116, 1486, 237, 51664], "temperature": 0.0, "avg_logprob": -0.09532012512434775, "compression_ratio": 1.3963133640552996, "no_speech_prob": 0.0356743186712265}, {"id": 50, "seek": 11500, "start": 141.0, "end": 143.0, "text": "也可以玩这种围棋游戏", "tokens": [51664, 47935, 19912, 5562, 39810, 3919, 112, 16407, 233, 9592, 116, 1486, 237, 51764], "temperature": 0.0, "avg_logprob": -0.09532012512434775, "compression_ratio": 1.3963133640552996, "no_speech_prob": 0.0356743186712265}, {"id": 51, "seek": 14300, "start": 144.0, "end": 146.0, "text": "围棋游戏中比较出名的一个", "tokens": [50414, 3919, 112, 16407, 233, 9592, 116, 1486, 237, 5975, 11706, 9830, 225, 7781, 15940, 1546, 20182, 50514], "temperature": 0.0, "avg_logprob": -0.14394597406987544, "compression_ratio": 1.376923076923077, "no_speech_prob": 0.0011878727236762643}, {"id": 52, "seek": 14300, "start": 146.0, "end": 149.0, "text": "强化学习的算法就是AlphaGo", "tokens": [50514, 5702, 118, 23756, 29618, 2930, 254, 1546, 19497, 11148, 5620, 9171, 7211, 12104, 50664], "temperature": 0.0, "avg_logprob": -0.14394597406987544, "compression_ratio": 1.376923076923077, "no_speech_prob": 0.0011878727236762643}, {"id": 53, "seek": 14300, "start": 151.0, "end": 152.0, "text": "此外我们可以使用强化学习", "tokens": [50764, 17947, 12022, 15003, 6723, 22982, 9254, 5702, 118, 23756, 29618, 2930, 254, 50814], "temperature": 0.0, "avg_logprob": -0.14394597406987544, "compression_ratio": 1.376923076923077, "no_speech_prob": 0.0011878727236762643}, {"id": 54, "seek": 14300, "start": 152.0, "end": 153.0, "text": "来控制机器人", "tokens": [50814, 6912, 48707, 25491, 37960, 34386, 4035, 50864], "temperature": 0.0, "avg_logprob": -0.14394597406987544, "compression_ratio": 1.376923076923077, "no_speech_prob": 0.0011878727236762643}, {"id": 55, "seek": 14300, "start": 153.0, "end": 155.0, "text": "以及来实现助力交通", "tokens": [50864, 40282, 6912, 24726, 20204, 37618, 13486, 28455, 19550, 50964], "temperature": 0.0, "avg_logprob": -0.14394597406987544, "compression_ratio": 1.376923076923077, "no_speech_prob": 0.0011878727236762643}, {"id": 56, "seek": 14300, "start": 155.0, "end": 157.0, "text": "另外还可以使用强化学习", "tokens": [50964, 26202, 14852, 6723, 22982, 9254, 5702, 118, 23756, 29618, 2930, 254, 51064], "temperature": 0.0, "avg_logprob": -0.14394597406987544, "compression_ratio": 1.376923076923077, "no_speech_prob": 0.0011878727236762643}, {"id": 57, "seek": 14300, "start": 157.0, "end": 159.0, "text": "来更好地给我们做推进", "tokens": [51064, 6912, 19002, 2131, 10928, 23197, 15003, 10907, 33597, 36700, 51164], "temperature": 0.0, "avg_logprob": -0.14394597406987544, "compression_ratio": 1.376923076923077, "no_speech_prob": 0.0011878727236762643}, {"id": 58, "seek": 14300, "start": 161.0, "end": 162.0, "text": "接下来就到第二部分", "tokens": [51264, 14468, 4438, 6912, 45918, 19693, 32174, 51314], "temperature": 0.0, "avg_logprob": -0.14394597406987544, "compression_ratio": 1.376923076923077, "no_speech_prob": 0.0011878727236762643}, {"id": 59, "seek": 14300, "start": 162.0, "end": 166.0, "text": "也就是为什么要使用本书来学习强化学习", "tokens": [51314, 6404, 5620, 31006, 4275, 22982, 9254, 8802, 2930, 99, 6912, 29618, 2930, 254, 5702, 118, 23756, 29618, 2930, 254, 51514], "temperature": 0.0, "avg_logprob": -0.14394597406987544, "compression_ratio": 1.376923076923077, "no_speech_prob": 0.0011878727236762643}, {"id": 60, "seek": 14300, "start": 166.0, "end": 167.0, "text": "这部分其实也是讲", "tokens": [51514, 5562, 32174, 41646, 22021, 39255, 51564], "temperature": 0.0, "avg_logprob": -0.14394597406987544, "compression_ratio": 1.376923076923077, "no_speech_prob": 0.0011878727236762643}, {"id": 61, "seek": 14300, "start": 167.0, "end": 170.0, "text": "这个蘑菇书它出版的一些故事", "tokens": [51564, 15368, 31882, 239, 16616, 229, 2930, 99, 11284, 7781, 42096, 1546, 38515, 43045, 6973, 51714], "temperature": 0.0, "avg_logprob": -0.14394597406987544, "compression_ratio": 1.376923076923077, "no_speech_prob": 0.0011878727236762643}, {"id": 62, "seek": 17000, "start": 171.0, "end": 173.0, "text": "当时我在学习强化学习的时候", "tokens": [50414, 16233, 15729, 40528, 29618, 2930, 254, 5702, 118, 23756, 29618, 2930, 254, 49873, 50514], "temperature": 0.0, "avg_logprob": -0.15009263845590445, "compression_ratio": 1.4230769230769231, "no_speech_prob": 0.025176001712679863}, {"id": 63, "seek": 17000, "start": 173.0, "end": 175.0, "text": "搜集了一些资料", "tokens": [50514, 162, 14987, 26020, 2289, 38515, 5266, 226, 33404, 50614], "temperature": 0.0, "avg_logprob": -0.15009263845590445, "compression_ratio": 1.4230769230769231, "no_speech_prob": 0.025176001712679863}, {"id": 64, "seek": 17000, "start": 175.0, "end": 178.0, "text": "然后我发现这些资料", "tokens": [50614, 26636, 1654, 28926, 20204, 5562, 13824, 5266, 226, 33404, 50764], "temperature": 0.0, "avg_logprob": -0.15009263845590445, "compression_ratio": 1.4230769230769231, "no_speech_prob": 0.025176001712679863}, {"id": 65, "seek": 17000, "start": 178.0, "end": 179.0, "text": "都有点灰色难懂", "tokens": [50764, 48121, 12579, 13290, 108, 17673, 46531, 29624, 50814], "temperature": 0.0, "avg_logprob": -0.15009263845590445, "compression_ratio": 1.4230769230769231, "no_speech_prob": 0.025176001712679863}, {"id": 66, "seek": 17000, "start": 179.0, "end": 182.0, "text": "并不是那么容易地上手", "tokens": [50814, 3509, 114, 7296, 35693, 49212, 10928, 5708, 11389, 50964], "temperature": 0.0, "avg_logprob": -0.15009263845590445, "compression_ratio": 1.4230769230769231, "no_speech_prob": 0.025176001712679863}, {"id": 67, "seek": 17000, "start": 183.0, "end": 185.0, "text": "于是我开始到网上", "tokens": [51014, 37732, 42614, 45213, 4511, 16469, 239, 5708, 51114], "temperature": 0.0, "avg_logprob": -0.15009263845590445, "compression_ratio": 1.4230769230769231, "no_speech_prob": 0.025176001712679863}, {"id": 68, "seek": 17000, "start": 186.0, "end": 188.0, "text": "搜索一些公开课来学习", "tokens": [51164, 162, 14987, 7732, 95, 38515, 13545, 18937, 5233, 122, 6912, 29618, 2930, 254, 51264], "temperature": 0.0, "avg_logprob": -0.15009263845590445, "compression_ratio": 1.4230769230769231, "no_speech_prob": 0.025176001712679863}, {"id": 69, "seek": 17000, "start": 188.0, "end": 190.0, "text": "首先我搜索到的是", "tokens": [51264, 36490, 1654, 162, 14987, 7732, 95, 4511, 24620, 51364], "temperature": 0.0, "avg_logprob": -0.15009263845590445, "compression_ratio": 1.4230769230769231, "no_speech_prob": 0.025176001712679863}, {"id": 70, "seek": 17000, "start": 190.0, "end": 192.0, "text": "李宏毅老师的一些公开课", "tokens": [51364, 31206, 2415, 237, 7256, 227, 10439, 29186, 1546, 38515, 13545, 18937, 5233, 122, 51464], "temperature": 0.0, "avg_logprob": -0.15009263845590445, "compression_ratio": 1.4230769230769231, "no_speech_prob": 0.025176001712679863}, {"id": 71, "seek": 17000, "start": 192.0, "end": 195.0, "text": "很多人就是入门深度学习", "tokens": [51464, 20778, 4035, 5620, 14028, 8259, 101, 24043, 13127, 29618, 2930, 254, 51614], "temperature": 0.0, "avg_logprob": -0.15009263845590445, "compression_ratio": 1.4230769230769231, "no_speech_prob": 0.025176001712679863}, {"id": 72, "seek": 17000, "start": 195.0, "end": 196.0, "text": "和基学低门课", "tokens": [51614, 12565, 26008, 29618, 41377, 8259, 101, 5233, 122, 51664], "temperature": 0.0, "avg_logprob": -0.15009263845590445, "compression_ratio": 1.4230769230769231, "no_speech_prob": 0.025176001712679863}, {"id": 73, "seek": 17000, "start": 196.0, "end": 198.0, "text": "其实就是李宏毅老师的课", "tokens": [51664, 41646, 5620, 31206, 2415, 237, 7256, 227, 10439, 29186, 1546, 5233, 122, 51764], "temperature": 0.0, "avg_logprob": -0.15009263845590445, "compression_ratio": 1.4230769230769231, "no_speech_prob": 0.025176001712679863}, {"id": 74, "seek": 19800, "start": 198.0, "end": 200.0, "text": "李宏毅老师的课", "tokens": [50364, 31206, 2415, 237, 7256, 227, 10439, 29186, 1546, 5233, 122, 50464], "temperature": 0.0, "avg_logprob": -0.1459581313594695, "compression_ratio": 1.598360655737705, "no_speech_prob": 0.0021826450247317553}, {"id": 75, "seek": 19800, "start": 202.0, "end": 205.0, "text": "李宏毅老师的基础学习和深度学习公开课", "tokens": [50564, 31206, 2415, 237, 7256, 227, 10439, 29186, 1546, 26008, 38114, 222, 29618, 2930, 254, 12565, 24043, 13127, 29618, 2930, 254, 13545, 18937, 5233, 122, 50714], "temperature": 0.0, "avg_logprob": -0.1459581313594695, "compression_ratio": 1.598360655737705, "no_speech_prob": 0.0021826450247317553}, {"id": 76, "seek": 19800, "start": 205.0, "end": 207.0, "text": "在编上有很高的播放量", "tokens": [50714, 3581, 38109, 244, 5708, 2412, 4563, 12979, 1546, 49993, 12744, 26748, 50814], "temperature": 0.0, "avg_logprob": -0.1459581313594695, "compression_ratio": 1.598360655737705, "no_speech_prob": 0.0021826450247317553}, {"id": 77, "seek": 19800, "start": 208.0, "end": 211.0, "text": "于是我搜索了李宏毅老师的强化学习课", "tokens": [50864, 37732, 42614, 162, 14987, 7732, 95, 2289, 31206, 2415, 237, 7256, 227, 10439, 29186, 1546, 5702, 118, 23756, 29618, 2930, 254, 5233, 122, 51014], "temperature": 0.0, "avg_logprob": -0.1459581313594695, "compression_ratio": 1.598360655737705, "no_speech_prob": 0.0021826450247317553}, {"id": 78, "seek": 19800, "start": 211.0, "end": 213.0, "text": "这门课叫顺强化学习", "tokens": [51014, 5562, 8259, 101, 5233, 122, 19855, 10178, 118, 5702, 118, 23756, 29618, 2930, 254, 51114], "temperature": 0.0, "avg_logprob": -0.1459581313594695, "compression_ratio": 1.598360655737705, "no_speech_prob": 0.0021826450247317553}, {"id": 79, "seek": 19800, "start": 213.0, "end": 215.0, "text": "这门课跟以往的李宏毅老师的", "tokens": [51114, 5562, 8259, 101, 5233, 122, 9678, 3588, 29510, 1546, 31206, 2415, 237, 7256, 227, 10439, 29186, 1546, 51214], "temperature": 0.0, "avg_logprob": -0.1459581313594695, "compression_ratio": 1.598360655737705, "no_speech_prob": 0.0021826450247317553}, {"id": 80, "seek": 19800, "start": 215.0, "end": 217.0, "text": "课人的风格都是一样的", "tokens": [51214, 5233, 122, 4035, 1546, 47209, 30921, 22796, 2257, 14496, 1546, 51314], "temperature": 0.0, "avg_logprob": -0.1459581313594695, "compression_ratio": 1.598360655737705, "no_speech_prob": 0.0021826450247317553}, {"id": 81, "seek": 19800, "start": 217.0, "end": 219.0, "text": "就是非常的生动有趣", "tokens": [51314, 5620, 48263, 8244, 34961, 2412, 39835, 51414], "temperature": 0.0, "avg_logprob": -0.1459581313594695, "compression_ratio": 1.598360655737705, "no_speech_prob": 0.0021826450247317553}, {"id": 82, "seek": 19800, "start": 219.0, "end": 221.0, "text": "李宏毅老师经常会用", "tokens": [51414, 31206, 2415, 237, 7256, 227, 10439, 29186, 30276, 11279, 12949, 9254, 51514], "temperature": 0.0, "avg_logprob": -0.1459581313594695, "compression_ratio": 1.598360655737705, "no_speech_prob": 0.0021826450247317553}, {"id": 83, "seek": 19800, "start": 221.0, "end": 224.0, "text": "玩亚达利游戏的例子来讲强化学习", "tokens": [51514, 19912, 1369, 248, 9830, 122, 23700, 9592, 116, 1486, 237, 1546, 17797, 7626, 6912, 39255, 5702, 118, 23756, 29618, 2930, 254, 51664], "temperature": 0.0, "avg_logprob": -0.1459581313594695, "compression_ratio": 1.598360655737705, "no_speech_prob": 0.0021826450247317553}, {"id": 84, "seek": 19800, "start": 224.0, "end": 226.0, "text": "这样强化学你就变得通透易懂", "tokens": [51664, 21209, 5702, 118, 23756, 29618, 2166, 3111, 2129, 246, 5916, 19550, 2215, 237, 31962, 29624, 51764], "temperature": 0.0, "avg_logprob": -0.1459581313594695, "compression_ratio": 1.598360655737705, "no_speech_prob": 0.0021826450247317553}, {"id": 85, "seek": 22600, "start": 226.0, "end": 228.0, "text": "然后就很多人都会", "tokens": [50364, 26636, 3111, 20778, 4035, 7182, 12949, 50464], "temperature": 0.0, "avg_logprob": -0.17728419704291656, "compression_ratio": 1.3333333333333333, "no_speech_prob": 0.01566268689930439}, {"id": 86, "seek": 22600, "start": 228.0, "end": 231.0, "text": "把这门课作为自己的强化学习入门课", "tokens": [50464, 16075, 5562, 8259, 101, 5233, 122, 11914, 13992, 17645, 1546, 5702, 118, 23756, 29618, 2930, 254, 14028, 8259, 101, 5233, 122, 50614], "temperature": 0.0, "avg_logprob": -0.17728419704291656, "compression_ratio": 1.3333333333333333, "no_speech_prob": 0.01566268689930439}, {"id": 87, "seek": 22600, "start": 231.0, "end": 234.0, "text": "这边我们念出了一些", "tokens": [50614, 5562, 40503, 15003, 33679, 7781, 2289, 38515, 50764], "temperature": 0.0, "avg_logprob": -0.17728419704291656, "compression_ratio": 1.3333333333333333, "no_speech_prob": 0.01566268689930439}, {"id": 88, "seek": 22600, "start": 234.0, "end": 237.0, "text": "就是观众的一些好评", "tokens": [50764, 5620, 42350, 7384, 245, 1546, 38515, 2131, 5233, 226, 50914], "temperature": 0.0, "avg_logprob": -0.17728419704291656, "compression_ratio": 1.3333333333333333, "no_speech_prob": 0.01566268689930439}, {"id": 89, "seek": 22600, "start": 237.0, "end": 240.0, "text": "比如说这边观众说", "tokens": [50914, 36757, 8090, 5562, 40503, 42350, 7384, 245, 8090, 51064], "temperature": 0.0, "avg_logprob": -0.17728419704291656, "compression_ratio": 1.3333333333333333, "no_speech_prob": 0.01566268689930439}, {"id": 90, "seek": 22600, "start": 240.0, "end": 242.0, "text": "国内这个讲得最好了", "tokens": [51064, 16086, 34742, 15368, 39255, 5916, 8661, 12621, 51164], "temperature": 0.0, "avg_logprob": -0.17728419704291656, "compression_ratio": 1.3333333333333333, "no_speech_prob": 0.01566268689930439}, {"id": 91, "seek": 22600, "start": 242.0, "end": 244.0, "text": "然后李宏毅老师讲得真厉害", "tokens": [51164, 26636, 31206, 2415, 237, 7256, 227, 10439, 29186, 39255, 5916, 6303, 5014, 231, 14694, 51264], "temperature": 0.0, "avg_logprob": -0.17728419704291656, "compression_ratio": 1.3333333333333333, "no_speech_prob": 0.01566268689930439}, {"id": 92, "seek": 22600, "start": 244.0, "end": 247.0, "text": "还有这个评论比较搞笑", "tokens": [51264, 35091, 15368, 5233, 226, 7422, 118, 11706, 9830, 225, 41816, 11600, 51414], "temperature": 0.0, "avg_logprob": -0.17728419704291656, "compression_ratio": 1.3333333333333333, "no_speech_prob": 0.01566268689930439}, {"id": 93, "seek": 22600, "start": 247.0, "end": 249.0, "text": "这个视频每天晚上都有", "tokens": [51414, 15368, 40656, 39752, 23664, 6135, 50157, 48121, 51514], "temperature": 0.0, "avg_logprob": -0.17728419704291656, "compression_ratio": 1.3333333333333333, "no_speech_prob": 0.01566268689930439}, {"id": 94, "seek": 22600, "start": 249.0, "end": 251.0, "text": "几个到十几个人在看", "tokens": [51514, 6336, 254, 7549, 4511, 20145, 6336, 254, 7549, 4035, 3581, 4200, 51614], "temperature": 0.0, "avg_logprob": -0.17728419704291656, "compression_ratio": 1.3333333333333333, "no_speech_prob": 0.01566268689930439}, {"id": 95, "seek": 22600, "start": 251.0, "end": 253.0, "text": "因此我们可以发现", "tokens": [51614, 8698, 17947, 15003, 6723, 28926, 20204, 51714], "temperature": 0.0, "avg_logprob": -0.17728419704291656, "compression_ratio": 1.3333333333333333, "no_speech_prob": 0.01566268689930439}, {"id": 96, "seek": 25300, "start": 253.0, "end": 259.0, "text": "这门课还是非常受人欢迎的", "tokens": [50364, 5562, 8259, 101, 5233, 122, 45726, 14392, 23151, 4035, 28566, 17699, 1546, 50664], "temperature": 0.0, "avg_logprob": -0.1147757432399652, "compression_ratio": 1.4556962025316456, "no_speech_prob": 0.03209590166807175}, {"id": 97, "seek": 25300, "start": 262.0, "end": 264.0, "text": "后来我发现李宏毅老师的课", "tokens": [50814, 13547, 6912, 1654, 28926, 20204, 31206, 2415, 237, 7256, 227, 10439, 29186, 1546, 5233, 122, 50914], "temperature": 0.0, "avg_logprob": -0.1147757432399652, "compression_ratio": 1.4556962025316456, "no_speech_prob": 0.03209590166807175}, {"id": 98, "seek": 25300, "start": 264.0, "end": 265.0, "text": "不是那么全面", "tokens": [50914, 7296, 35693, 11319, 8833, 50964], "temperature": 0.0, "avg_logprob": -0.1147757432399652, "compression_ratio": 1.4556962025316456, "no_speech_prob": 0.03209590166807175}, {"id": 99, "seek": 25300, "start": 265.0, "end": 267.0, "text": "他的课主要集中于讲解", "tokens": [50964, 31309, 5233, 122, 13557, 4275, 26020, 5975, 37732, 39255, 17278, 51064], "temperature": 0.0, "avg_logprob": -0.1147757432399652, "compression_ratio": 1.4556962025316456, "no_speech_prob": 0.03209590166807175}, {"id": 100, "seek": 25300, "start": 267.0, "end": 268.0, "text": "迅速强化学算法", "tokens": [51064, 3316, 227, 31217, 5702, 118, 23756, 29618, 19497, 11148, 51114], "temperature": 0.0, "avg_logprob": -0.1147757432399652, "compression_ratio": 1.4556962025316456, "no_speech_prob": 0.03209590166807175}, {"id": 101, "seek": 25300, "start": 268.0, "end": 270.0, "text": "而忽略了全球强化学算法", "tokens": [51114, 11070, 4263, 121, 6904, 98, 2289, 11319, 28533, 5702, 118, 23756, 29618, 19497, 11148, 51214], "temperature": 0.0, "avg_logprob": -0.1147757432399652, "compression_ratio": 1.4556962025316456, "no_speech_prob": 0.03209590166807175}, {"id": 102, "seek": 25300, "start": 270.0, "end": 273.0, "text": "以及一些比较前沿的强化学算法", "tokens": [51214, 40282, 38515, 11706, 9830, 225, 8945, 3308, 123, 1546, 5702, 118, 23756, 29618, 19497, 11148, 51364], "temperature": 0.0, "avg_logprob": -0.1147757432399652, "compression_ratio": 1.4556962025316456, "no_speech_prob": 0.03209590166807175}, {"id": 103, "seek": 25300, "start": 273.0, "end": 274.0, "text": "因此我开始", "tokens": [51364, 8698, 17947, 1654, 45213, 51414], "temperature": 0.0, "avg_logprob": -0.1147757432399652, "compression_ratio": 1.4556962025316456, "no_speech_prob": 0.03209590166807175}, {"id": 104, "seek": 25300, "start": 274.0, "end": 276.0, "text": "到网上搜索其他的强化学公开课", "tokens": [51414, 4511, 16469, 239, 5708, 162, 14987, 7732, 95, 9572, 31309, 5702, 118, 23756, 29618, 13545, 18937, 5233, 122, 51514], "temperature": 0.0, "avg_logprob": -0.1147757432399652, "compression_ratio": 1.4556962025316456, "no_speech_prob": 0.03209590166807175}, {"id": 105, "seek": 25300, "start": 276.0, "end": 278.0, "text": "当时是搜索到了", "tokens": [51514, 16233, 15729, 1541, 162, 14987, 7732, 95, 21381, 51614], "temperature": 0.0, "avg_logprob": -0.1147757432399652, "compression_ratio": 1.4556962025316456, "no_speech_prob": 0.03209590166807175}, {"id": 106, "seek": 25300, "start": 278.0, "end": 280.0, "text": "周博恩老师的强化学纲要", "tokens": [51614, 37390, 5322, 248, 47638, 10439, 29186, 1546, 5702, 118, 23756, 29618, 16853, 110, 4275, 51714], "temperature": 0.0, "avg_logprob": -0.1147757432399652, "compression_ratio": 1.4556962025316456, "no_speech_prob": 0.03209590166807175}, {"id": 107, "seek": 25300, "start": 280.0, "end": 282.0, "text": "周博恩老师是", "tokens": [51714, 37390, 5322, 248, 47638, 10439, 29186, 1541, 51814], "temperature": 0.0, "avg_logprob": -0.1147757432399652, "compression_ratio": 1.4556962025316456, "no_speech_prob": 0.03209590166807175}, {"id": 108, "seek": 28200, "start": 282.0, "end": 285.0, "text": "人文智能底层的一个顶尖学者", "tokens": [50364, 4035, 17174, 5094, 118, 8225, 22816, 9636, 224, 1546, 20182, 10178, 114, 1530, 244, 29618, 12444, 50514], "temperature": 0.0, "avg_logprob": -0.1777700089119576, "compression_ratio": 1.2875536480686696, "no_speech_prob": 0.013221688568592072}, {"id": 109, "seek": 28200, "start": 285.0, "end": 288.0, "text": "他在人文智能底层会议", "tokens": [50514, 5000, 3581, 4035, 17174, 5094, 118, 8225, 22816, 9636, 224, 12949, 7422, 106, 50664], "temperature": 0.0, "avg_logprob": -0.1777700089119576, "compression_ratio": 1.2875536480686696, "no_speech_prob": 0.013221688568592072}, {"id": 110, "seek": 28200, "start": 288.0, "end": 290.0, "text": "亲爱发表了五十余篇学术论文", "tokens": [50664, 49296, 27324, 28926, 17571, 2289, 21001, 20145, 1593, 247, 20878, 229, 29618, 1474, 107, 7422, 118, 17174, 50764], "temperature": 0.0, "avg_logprob": -0.1777700089119576, "compression_ratio": 1.2875536480686696, "no_speech_prob": 0.013221688568592072}, {"id": 111, "seek": 28200, "start": 290.0, "end": 293.0, "text": "然后论文的作用量超过一万次", "tokens": [50764, 26636, 7422, 118, 17174, 1546, 11914, 9254, 26748, 19869, 16866, 2257, 23570, 9487, 50914], "temperature": 0.0, "avg_logprob": -0.1777700089119576, "compression_ratio": 1.2875536480686696, "no_speech_prob": 0.013221688568592072}, {"id": 112, "seek": 28200, "start": 293.0, "end": 297.0, "text": "周博恩老师的这门强化学纲要课", "tokens": [50914, 37390, 5322, 248, 47638, 10439, 29186, 1546, 5562, 8259, 101, 5702, 118, 23756, 29618, 16853, 110, 4275, 5233, 122, 51114], "temperature": 0.0, "avg_logprob": -0.1777700089119576, "compression_ratio": 1.2875536480686696, "no_speech_prob": 0.013221688568592072}, {"id": 113, "seek": 28200, "start": 297.0, "end": 298.0, "text": "就是理论严谨", "tokens": [51114, 5620, 13876, 7422, 118, 940, 98, 8897, 101, 51164], "temperature": 0.0, "avg_logprob": -0.1777700089119576, "compression_ratio": 1.2875536480686696, "no_speech_prob": 0.013221688568592072}, {"id": 114, "seek": 28200, "start": 298.0, "end": 299.0, "text": "然后内容丰富", "tokens": [51164, 26636, 34742, 25750, 940, 108, 47564, 51214], "temperature": 0.0, "avg_logprob": -0.1777700089119576, "compression_ratio": 1.2875536480686696, "no_speech_prob": 0.013221688568592072}, {"id": 115, "seek": 28200, "start": 299.0, "end": 301.0, "text": "全面的教训介绍了强化学领域", "tokens": [51214, 11319, 8833, 1546, 21936, 7422, 255, 30312, 10115, 235, 2289, 5702, 118, 23756, 29618, 12501, 228, 16262, 253, 51314], "temperature": 0.0, "avg_logprob": -0.1777700089119576, "compression_ratio": 1.2875536480686696, "no_speech_prob": 0.013221688568592072}, {"id": 116, "seek": 28200, "start": 301.0, "end": 303.0, "text": "并有相关代码实践", "tokens": [51314, 3509, 114, 2412, 15106, 28053, 19105, 23230, 223, 24726, 6563, 113, 51414], "temperature": 0.0, "avg_logprob": -0.1777700089119576, "compression_ratio": 1.2875536480686696, "no_speech_prob": 0.013221688568592072}, {"id": 117, "seek": 28200, "start": 308.0, "end": 309.0, "text": "在学习完", "tokens": [51664, 3581, 29618, 2930, 254, 14128, 51714], "temperature": 0.0, "avg_logprob": -0.1777700089119576, "compression_ratio": 1.2875536480686696, "no_speech_prob": 0.013221688568592072}, {"id": 118, "seek": 30900, "start": 309.0, "end": 314.0, "text": "女红衣和周博恩老师两门强化学课以后", "tokens": [50364, 17015, 16853, 95, 9890, 96, 12565, 37390, 5322, 248, 47638, 10439, 29186, 36257, 8259, 101, 5702, 118, 23756, 29618, 5233, 122, 3588, 13547, 50614], "temperature": 0.0, "avg_logprob": -0.1435796832511438, "compression_ratio": 1.3978494623655915, "no_speech_prob": 0.026352880522608757}, {"id": 119, "seek": 30900, "start": 314.0, "end": 316.0, "text": "我发现还是有一点不足", "tokens": [50614, 1654, 28926, 20204, 45726, 32241, 12579, 1960, 37236, 50714], "temperature": 0.0, "avg_logprob": -0.1435796832511438, "compression_ratio": 1.3978494623655915, "no_speech_prob": 0.026352880522608757}, {"id": 120, "seek": 30900, "start": 316.0, "end": 318.0, "text": "就是代码的实践还偏少", "tokens": [50714, 5620, 19105, 23230, 223, 1546, 24726, 6563, 113, 14852, 7437, 237, 15686, 50814], "temperature": 0.0, "avg_logprob": -0.1435796832511438, "compression_ratio": 1.3978494623655915, "no_speech_prob": 0.026352880522608757}, {"id": 121, "seek": 30900, "start": 318.0, "end": 322.0, "text": "于是我在网上搜索其他的强化学课", "tokens": [50814, 37732, 1541, 40528, 16469, 239, 5708, 162, 14987, 7732, 95, 9572, 31309, 5702, 118, 23756, 29618, 5233, 122, 51014], "temperature": 0.0, "avg_logprob": -0.1435796832511438, "compression_ratio": 1.3978494623655915, "no_speech_prob": 0.026352880522608757}, {"id": 122, "seek": 30900, "start": 322.0, "end": 324.0, "text": "当时就搜索了李克强老师的一个", "tokens": [51014, 16233, 15729, 3111, 162, 14987, 7732, 95, 2289, 31206, 24881, 5702, 118, 10439, 29186, 1546, 20182, 51114], "temperature": 0.0, "avg_logprob": -0.1435796832511438, "compression_ratio": 1.3978494623655915, "no_speech_prob": 0.026352880522608757}, {"id": 123, "seek": 30900, "start": 324.0, "end": 328.0, "text": "叫试验冠军代理丛林实践强化学习这门课", "tokens": [51114, 19855, 5233, 243, 49657, 234, 5676, 254, 5676, 249, 19105, 13876, 940, 249, 32063, 24726, 6563, 113, 5702, 118, 23756, 29618, 2930, 254, 5562, 8259, 101, 5233, 122, 51314], "temperature": 0.0, "avg_logprob": -0.1435796832511438, "compression_ratio": 1.3978494623655915, "no_speech_prob": 0.026352880522608757}, {"id": 124, "seek": 30900, "start": 328.0, "end": 330.0, "text": "这门课有个特别突出的优点", "tokens": [51314, 5562, 8259, 101, 5233, 122, 2412, 7549, 17682, 18453, 40859, 7781, 1546, 7384, 246, 12579, 51414], "temperature": 0.0, "avg_logprob": -0.1435796832511438, "compression_ratio": 1.3978494623655915, "no_speech_prob": 0.026352880522608757}, {"id": 125, "seek": 30900, "start": 330.0, "end": 332.0, "text": "就是实战性强", "tokens": [51414, 5620, 24726, 1486, 246, 21686, 5702, 118, 51514], "temperature": 0.0, "avg_logprob": -0.1435796832511438, "compression_ratio": 1.3978494623655915, "no_speech_prob": 0.026352880522608757}, {"id": 126, "seek": 30900, "start": 332.0, "end": 335.0, "text": "突兵课只能会使用单单的代码来讲解强化学习", "tokens": [51514, 40859, 2347, 113, 5233, 122, 14003, 8225, 12949, 22982, 9254, 47446, 47446, 1546, 19105, 23230, 223, 6912, 39255, 17278, 5702, 118, 23756, 29618, 2930, 254, 51664], "temperature": 0.0, "avg_logprob": -0.1435796832511438, "compression_ratio": 1.3978494623655915, "no_speech_prob": 0.026352880522608757}, {"id": 127, "seek": 30900, "start": 335.0, "end": 337.0, "text": "这边也有很多好评", "tokens": [51664, 5562, 40503, 6404, 2412, 20778, 2131, 5233, 226, 51764], "temperature": 0.0, "avg_logprob": -0.1435796832511438, "compression_ratio": 1.3978494623655915, "no_speech_prob": 0.026352880522608757}, {"id": 128, "seek": 33900, "start": 340.0, "end": 344.0, "text": "有了这三门课以后", "tokens": [50414, 2412, 2289, 5562, 10960, 8259, 101, 5233, 122, 3588, 13547, 50614], "temperature": 0.0, "avg_logprob": -0.1369733952764255, "compression_ratio": 1.506726457399103, "no_speech_prob": 0.009859289973974228}, {"id": 129, "seek": 33900, "start": 344.0, "end": 347.0, "text": "我发现这三门课", "tokens": [50614, 1654, 28926, 20204, 5562, 10960, 8259, 101, 5233, 122, 50764], "temperature": 0.0, "avg_logprob": -0.1369733952764255, "compression_ratio": 1.506726457399103, "no_speech_prob": 0.009859289973974228}, {"id": 130, "seek": 33900, "start": 347.0, "end": 350.0, "text": "通过这些公开课来进学的话", "tokens": [50764, 19550, 16866, 5562, 13824, 13545, 18937, 5233, 122, 6912, 36700, 29618, 44575, 50914], "temperature": 0.0, "avg_logprob": -0.1369733952764255, "compression_ratio": 1.506726457399103, "no_speech_prob": 0.009859289973974228}, {"id": 131, "seek": 33900, "start": 350.0, "end": 352.0, "text": "有些优点但也有缺点", "tokens": [50914, 2412, 13824, 7384, 246, 12579, 8395, 6404, 2412, 38109, 118, 12579, 51014], "temperature": 0.0, "avg_logprob": -0.1369733952764255, "compression_ratio": 1.506726457399103, "no_speech_prob": 0.009859289973974228}, {"id": 132, "seek": 33900, "start": 352.0, "end": 355.0, "text": "优点的话就是这些课都是非常经典的一些公开课", "tokens": [51014, 7384, 246, 12579, 44575, 5620, 5562, 13824, 5233, 122, 22796, 14392, 30276, 2347, 116, 1546, 38515, 13545, 18937, 5233, 122, 51164], "temperature": 0.0, "avg_logprob": -0.1369733952764255, "compression_ratio": 1.506726457399103, "no_speech_prob": 0.009859289973974228}, {"id": 133, "seek": 33900, "start": 355.0, "end": 358.0, "text": "然后他们这些课的这些老师", "tokens": [51164, 26636, 47911, 5562, 13824, 5233, 122, 1546, 5562, 13824, 10439, 29186, 51314], "temperature": 0.0, "avg_logprob": -0.1369733952764255, "compression_ratio": 1.506726457399103, "no_speech_prob": 0.009859289973974228}, {"id": 134, "seek": 33900, "start": 358.0, "end": 360.0, "text": "也都是人文领域的一些大流", "tokens": [51314, 6404, 22796, 4035, 17174, 12501, 228, 16262, 253, 1546, 38515, 3582, 27854, 51414], "temperature": 0.0, "avg_logprob": -0.1369733952764255, "compression_ratio": 1.506726457399103, "no_speech_prob": 0.009859289973974228}, {"id": 135, "seek": 33900, "start": 360.0, "end": 363.0, "text": "然后他们的播放量非常高", "tokens": [51414, 26636, 47911, 1546, 49993, 12744, 26748, 14392, 12979, 51564], "temperature": 0.0, "avg_logprob": -0.1369733952764255, "compression_ratio": 1.506726457399103, "no_speech_prob": 0.009859289973974228}, {"id": 136, "seek": 33900, "start": 363.0, "end": 365.0, "text": "就比较受欢迎", "tokens": [51564, 3111, 11706, 9830, 225, 23151, 28566, 17699, 51664], "temperature": 0.0, "avg_logprob": -0.1369733952764255, "compression_ratio": 1.506726457399103, "no_speech_prob": 0.009859289973974228}, {"id": 137, "seek": 33900, "start": 365.0, "end": 367.0, "text": "但是这些课呢也会存在一些问题", "tokens": [51664, 11189, 5562, 13824, 5233, 122, 6240, 6404, 12949, 39781, 3581, 38515, 34069, 51764], "temperature": 0.0, "avg_logprob": -0.1369733952764255, "compression_ratio": 1.506726457399103, "no_speech_prob": 0.009859289973974228}, {"id": 138, "seek": 36700, "start": 367.0, "end": 369.0, "text": "就用这些公开课来进学的课会存在一些问题", "tokens": [50364, 3111, 9254, 5562, 13824, 13545, 18937, 5233, 122, 6912, 36700, 29618, 1546, 5233, 122, 12949, 39781, 3581, 38515, 34069, 50464], "temperature": 0.0, "avg_logprob": -0.18313076279380106, "compression_ratio": 1.4098939929328622, "no_speech_prob": 0.014955746941268444}, {"id": 139, "seek": 36700, "start": 369.0, "end": 371.0, "text": "比如说它不便于实际的查询", "tokens": [50464, 36757, 8090, 11284, 1960, 27364, 37732, 24726, 8842, 227, 1546, 42623, 5233, 95, 50564], "temperature": 0.0, "avg_logprob": -0.18313076279380106, "compression_ratio": 1.4098939929328622, "no_speech_prob": 0.014955746941268444}, {"id": 140, "seek": 36700, "start": 371.0, "end": 374.0, "text": "然后对于一些重点的知识点它会缺乏一些讲解", "tokens": [50564, 26636, 8713, 37732, 38515, 12624, 12579, 1546, 6498, 5233, 228, 12579, 11284, 12949, 38109, 118, 2930, 237, 38515, 39255, 17278, 50714], "temperature": 0.0, "avg_logprob": -0.18313076279380106, "compression_ratio": 1.4098939929328622, "no_speech_prob": 0.014955746941268444}, {"id": 141, "seek": 36700, "start": 374.0, "end": 377.0, "text": "此外这些知识点比较分散", "tokens": [50714, 17947, 12022, 5562, 13824, 6498, 5233, 228, 12579, 11706, 9830, 225, 6627, 7017, 96, 50864], "temperature": 0.0, "avg_logprob": -0.18313076279380106, "compression_ratio": 1.4098939929328622, "no_speech_prob": 0.014955746941268444}, {"id": 142, "seek": 36700, "start": 377.0, "end": 379.0, "text": "就每个老师他讲的知识点各有侧重", "tokens": [50864, 3111, 23664, 7549, 10439, 29186, 5000, 39255, 1546, 6498, 5233, 228, 12579, 17516, 2412, 3254, 100, 12624, 50964], "temperature": 0.0, "avg_logprob": -0.18313076279380106, "compression_ratio": 1.4098939929328622, "no_speech_prob": 0.014955746941268444}, {"id": 143, "seek": 36700, "start": 379.0, "end": 383.0, "text": "此外就是视频的扩弱化比较严重", "tokens": [50964, 17947, 12022, 5620, 40656, 39752, 1546, 3416, 102, 5702, 109, 23756, 11706, 9830, 225, 940, 98, 12624, 51164], "temperature": 0.0, "avg_logprob": -0.18313076279380106, "compression_ratio": 1.4098939929328622, "no_speech_prob": 0.014955746941268444}, {"id": 144, "seek": 36700, "start": 383.0, "end": 387.0, "text": "因此基于这三门公开课", "tokens": [51164, 8698, 17947, 26008, 37732, 5562, 10960, 8259, 101, 13545, 18937, 5233, 122, 51364], "temperature": 0.0, "avg_logprob": -0.18313076279380106, "compression_ratio": 1.4098939929328622, "no_speech_prob": 0.014955746941268444}, {"id": 145, "seek": 36700, "start": 387.0, "end": 391.0, "text": "就是我和杨亦远和江静", "tokens": [51364, 5620, 1654, 12565, 4422, 101, 1369, 99, 3316, 250, 12565, 41796, 5363, 247, 51564], "temperature": 0.0, "avg_logprob": -0.18313076279380106, "compression_ratio": 1.4098939929328622, "no_speech_prob": 0.014955746941268444}, {"id": 146, "seek": 36700, "start": 391.0, "end": 393.0, "text": "三个DDR成员", "tokens": [51564, 10960, 7549, 35, 9301, 11336, 3606, 246, 51664], "temperature": 0.0, "avg_logprob": -0.18313076279380106, "compression_ratio": 1.4098939929328622, "no_speech_prob": 0.014955746941268444}, {"id": 147, "seek": 36700, "start": 393.0, "end": 396.0, "text": "对这些三门课的内容进行整合补充优化", "tokens": [51664, 8713, 5562, 13824, 10960, 8259, 101, 5233, 122, 1546, 34742, 25750, 36700, 8082, 27662, 14245, 9890, 98, 2347, 227, 7384, 246, 23756, 51814], "temperature": 0.0, "avg_logprob": -0.18313076279380106, "compression_ratio": 1.4098939929328622, "no_speech_prob": 0.014955746941268444}, {"id": 148, "seek": 39600, "start": 396.0, "end": 399.0, "text": "然后写出了这本教材叫", "tokens": [50364, 26636, 5676, 247, 7781, 2289, 5562, 8802, 21936, 4422, 238, 19855, 50514], "temperature": 0.0, "avg_logprob": -0.1932887597517534, "compression_ratio": 1.28, "no_speech_prob": 0.013427086174488068}, {"id": 149, "seek": 39600, "start": 399.0, "end": 401.0, "text": "Easy IR 强化域教程", "tokens": [50514, 36, 5871, 16486, 220, 5702, 118, 23756, 16262, 253, 21936, 29649, 50614], "temperature": 0.0, "avg_logprob": -0.1932887597517534, "compression_ratio": 1.28, "no_speech_prob": 0.013427086174488068}, {"id": 150, "seek": 39600, "start": 405.0, "end": 407.0, "text": "这本教程当时是在", "tokens": [50814, 5562, 8802, 21936, 29649, 16233, 15729, 1541, 3581, 50914], "temperature": 0.0, "avg_logprob": -0.1932887597517534, "compression_ratio": 1.28, "no_speech_prob": 0.013427086174488068}, {"id": 151, "seek": 39600, "start": 407.0, "end": 409.0, "text": "一开始是在Github上发布的", "tokens": [50914, 2257, 45213, 1541, 3581, 38, 355, 836, 5708, 28926, 34688, 1546, 51014], "temperature": 0.0, "avg_logprob": -0.1932887597517534, "compression_ratio": 1.28, "no_speech_prob": 0.013427086174488068}, {"id": 152, "seek": 39600, "start": 409.0, "end": 411.0, "text": "然后Github上呢当时发完以后", "tokens": [51014, 26636, 38, 355, 836, 5708, 6240, 16233, 15729, 28926, 14128, 3588, 13547, 51114], "temperature": 0.0, "avg_logprob": -0.1932887597517534, "compression_ratio": 1.28, "no_speech_prob": 0.013427086174488068}, {"id": 153, "seek": 39600, "start": 411.0, "end": 412.0, "text": "就是有很多好评", "tokens": [51114, 5620, 2412, 20778, 2131, 5233, 226, 51164], "temperature": 0.0, "avg_logprob": -0.1932887597517534, "compression_ratio": 1.28, "no_speech_prob": 0.013427086174488068}, {"id": 154, "seek": 39600, "start": 412.0, "end": 413.0, "text": "比如这边", "tokens": [51164, 36757, 5562, 40503, 51214], "temperature": 0.0, "avg_logprob": -0.1932887597517534, "compression_ratio": 1.28, "no_speech_prob": 0.013427086174488068}, {"id": 155, "seek": 39600, "start": 413.0, "end": 415.0, "text": "比如说有人的同学就说", "tokens": [51214, 36757, 8090, 45820, 1546, 13089, 29618, 3111, 8090, 51314], "temperature": 0.0, "avg_logprob": -0.1932887597517534, "compression_ratio": 1.28, "no_speech_prob": 0.013427086174488068}, {"id": 156, "seek": 39600, "start": 415.0, "end": 416.0, "text": "昨天非常棒", "tokens": [51314, 47404, 6135, 14392, 23514, 51364], "temperature": 0.0, "avg_logprob": -0.1932887597517534, "compression_ratio": 1.28, "no_speech_prob": 0.013427086174488068}, {"id": 157, "seek": 39600, "start": 416.0, "end": 417.0, "text": "超越有帮助感谢博主", "tokens": [51364, 19869, 25761, 2412, 4845, 106, 37618, 9709, 11340, 5322, 248, 13557, 51414], "temperature": 0.0, "avg_logprob": -0.1932887597517534, "compression_ratio": 1.28, "no_speech_prob": 0.013427086174488068}, {"id": 158, "seek": 39600, "start": 417.0, "end": 419.0, "text": "然后等等等等", "tokens": [51414, 26636, 36000, 36000, 51514], "temperature": 0.0, "avg_logprob": -0.1932887597517534, "compression_ratio": 1.28, "no_speech_prob": 0.013427086174488068}, {"id": 159, "seek": 39600, "start": 419.0, "end": 423.0, "text": "然后目前这本书对应的那个Github仓库的", "tokens": [51514, 26636, 39004, 5562, 8802, 2930, 99, 8713, 44297, 1546, 43083, 38, 355, 836, 1550, 241, 6346, 241, 1546, 51714], "temperature": 0.0, "avg_logprob": -0.1932887597517534, "compression_ratio": 1.28, "no_speech_prob": 0.013427086174488068}, {"id": 160, "seek": 42300, "start": 423.0, "end": 426.0, "text": "start数已经达48k", "tokens": [50364, 24419, 33188, 49161, 9830, 122, 13318, 74, 50514], "temperature": 0.0, "avg_logprob": -0.20578058063983917, "compression_ratio": 1.3183856502242153, "no_speech_prob": 0.004538204986602068}, {"id": 161, "seek": 42300, "start": 426.0, "end": 428.0, "text": "然后", "tokens": [50514, 26636, 50614], "temperature": 0.0, "avg_logprob": -0.20578058063983917, "compression_ratio": 1.3183856502242153, "no_speech_prob": 0.004538204986602068}, {"id": 162, "seek": 42300, "start": 428.0, "end": 430.0, "text": "它的增长就是它的", "tokens": [50614, 45224, 24228, 252, 32271, 5620, 45224, 50714], "temperature": 0.0, "avg_logprob": -0.20578058063983917, "compression_ratio": 1.3183856502242153, "no_speech_prob": 0.004538204986602068}, {"id": 163, "seek": 42300, "start": 430.0, "end": 432.0, "text": "start数的一个增长曲线", "tokens": [50714, 24419, 33188, 1546, 20182, 24228, 252, 32271, 30753, 16853, 123, 50814], "temperature": 0.0, "avg_logprob": -0.20578058063983917, "compression_ratio": 1.3183856502242153, "no_speech_prob": 0.004538204986602068}, {"id": 164, "seek": 42300, "start": 432.0, "end": 434.0, "text": "也是曾经上升的一个态势", "tokens": [50814, 22021, 43801, 30276, 5708, 41670, 1546, 20182, 3757, 223, 5087, 123, 50914], "temperature": 0.0, "avg_logprob": -0.20578058063983917, "compression_ratio": 1.3183856502242153, "no_speech_prob": 0.004538204986602068}, {"id": 165, "seek": 42300, "start": 435.0, "end": 438.0, "text": "后来我们在DataWare举办了一次组队学习", "tokens": [50964, 13547, 6912, 15003, 3581, 35, 3274, 54, 543, 940, 122, 39453, 2289, 27505, 10115, 226, 10034, 253, 29618, 2930, 254, 51114], "temperature": 0.0, "avg_logprob": -0.20578058063983917, "compression_ratio": 1.3183856502242153, "no_speech_prob": 0.004538204986602068}, {"id": 166, "seek": 42300, "start": 438.0, "end": 440.0, "text": "然后组队学习的那个教材呢", "tokens": [51114, 26636, 10115, 226, 10034, 253, 29618, 2930, 254, 1546, 43083, 21936, 4422, 238, 6240, 51214], "temperature": 0.0, "avg_logprob": -0.20578058063983917, "compression_ratio": 1.3183856502242153, "no_speech_prob": 0.004538204986602068}, {"id": 167, "seek": 42300, "start": 440.0, "end": 442.0, "text": "就是以我们这本书的", "tokens": [51214, 5620, 3588, 15003, 5562, 8802, 2930, 99, 1546, 51314], "temperature": 0.0, "avg_logprob": -0.20578058063983917, "compression_ratio": 1.3183856502242153, "no_speech_prob": 0.004538204986602068}, {"id": 168, "seek": 42300, "start": 442.0, "end": 445.0, "text": "Github的那个在线版", "tokens": [51314, 38, 355, 836, 1546, 43083, 3581, 16853, 123, 42096, 51464], "temperature": 0.0, "avg_logprob": -0.20578058063983917, "compression_ratio": 1.3183856502242153, "no_speech_prob": 0.004538204986602068}, {"id": 169, "seek": 42300, "start": 445.0, "end": 447.0, "text": "作为一个教材", "tokens": [51464, 11914, 13992, 20182, 21936, 4422, 238, 51564], "temperature": 0.0, "avg_logprob": -0.20578058063983917, "compression_ratio": 1.3183856502242153, "no_speech_prob": 0.004538204986602068}, {"id": 170, "seek": 42300, "start": 449.0, "end": 451.0, "text": "在组队学习结束以后", "tokens": [51664, 3581, 10115, 226, 10034, 253, 29618, 2930, 254, 45641, 44320, 3588, 13547, 51764], "temperature": 0.0, "avg_logprob": -0.20578058063983917, "compression_ratio": 1.3183856502242153, "no_speech_prob": 0.004538204986602068}, {"id": 171, "seek": 45100, "start": 451.0, "end": 453.0, "text": "然后也是有很多的", "tokens": [50364, 26636, 22021, 2412, 20778, 1546, 50464], "temperature": 0.0, "avg_logprob": -0.14993891508682913, "compression_ratio": 1.3895131086142323, "no_speech_prob": 0.011506998911499977}, {"id": 172, "seek": 45100, "start": 453.0, "end": 456.0, "text": "来自世界各地的学习的小伙伴", "tokens": [50464, 6912, 9722, 24486, 17516, 10928, 1546, 29618, 2930, 254, 1546, 7322, 7384, 247, 7384, 112, 50614], "temperature": 0.0, "avg_logprob": -0.14993891508682913, "compression_ratio": 1.3895131086142323, "no_speech_prob": 0.011506998911499977}, {"id": 173, "seek": 45100, "start": 456.0, "end": 457.0, "text": "给出好评", "tokens": [50614, 23197, 7781, 2131, 5233, 226, 50664], "temperature": 0.0, "avg_logprob": -0.14993891508682913, "compression_ratio": 1.3895131086142323, "no_speech_prob": 0.011506998911499977}, {"id": 174, "seek": 45100, "start": 457.0, "end": 458.0, "text": "比如这边我就", "tokens": [50664, 36757, 5562, 40503, 22020, 50714], "temperature": 0.0, "avg_logprob": -0.14993891508682913, "compression_ratio": 1.3895131086142323, "no_speech_prob": 0.011506998911499977}, {"id": 175, "seek": 45100, "start": 458.0, "end": 460.0, "text": "列出了一个小伙伴的好评", "tokens": [50714, 43338, 7781, 2289, 20182, 7322, 7384, 247, 7384, 112, 1546, 2131, 5233, 226, 50814], "temperature": 0.0, "avg_logprob": -0.14993891508682913, "compression_ratio": 1.3895131086142323, "no_speech_prob": 0.011506998911499977}, {"id": 176, "seek": 45100, "start": 462.0, "end": 464.0, "text": "然后因为在Github上有很多的读者", "tokens": [50914, 26636, 34627, 3581, 38, 355, 836, 5708, 2412, 20778, 1546, 5233, 119, 12444, 51014], "temperature": 0.0, "avg_logprob": -0.14993891508682913, "compression_ratio": 1.3895131086142323, "no_speech_prob": 0.011506998911499977}, {"id": 177, "seek": 45100, "start": 464.0, "end": 466.0, "text": "会通过issue来给我们反馈", "tokens": [51014, 12949, 19550, 16866, 891, 622, 6912, 23197, 15003, 22138, 11748, 230, 51114], "temperature": 0.0, "avg_logprob": -0.14993891508682913, "compression_ratio": 1.3895131086142323, "no_speech_prob": 0.011506998911499977}, {"id": 178, "seek": 45100, "start": 466.0, "end": 469.0, "text": "比如说他们会觉得我们来的地方", "tokens": [51114, 36757, 8090, 47911, 12949, 29992, 15003, 6912, 1546, 30146, 51264], "temperature": 0.0, "avg_logprob": -0.14993891508682913, "compression_ratio": 1.3895131086142323, "no_speech_prob": 0.011506998911499977}, {"id": 179, "seek": 45100, "start": 469.0, "end": 471.0, "text": "翻译来电会有些问题", "tokens": [51264, 42716, 5233, 239, 6912, 42182, 12949, 2412, 13824, 34069, 51364], "temperature": 0.0, "avg_logprob": -0.14993891508682913, "compression_ratio": 1.3895131086142323, "no_speech_prob": 0.011506998911499977}, {"id": 180, "seek": 45100, "start": 471.0, "end": 472.0, "text": "他们会直接列出来", "tokens": [51364, 47911, 12949, 43297, 43338, 44561, 51414], "temperature": 0.0, "avg_logprob": -0.14993891508682913, "compression_ratio": 1.3895131086142323, "no_speech_prob": 0.011506998911499977}, {"id": 181, "seek": 45100, "start": 472.0, "end": 475.0, "text": "然后我们也会给他们及时的反馈", "tokens": [51414, 26636, 15003, 6404, 12949, 23197, 47911, 25703, 15729, 1546, 22138, 11748, 230, 51564], "temperature": 0.0, "avg_logprob": -0.14993891508682913, "compression_ratio": 1.3895131086142323, "no_speech_prob": 0.011506998911499977}, {"id": 182, "seek": 45100, "start": 475.0, "end": 478.0, "text": "然后对我们的教程进行优化", "tokens": [51564, 26636, 8713, 15003, 1546, 21936, 29649, 36700, 8082, 7384, 246, 23756, 51714], "temperature": 0.0, "avg_logprob": -0.14993891508682913, "compression_ratio": 1.3895131086142323, "no_speech_prob": 0.011506998911499977}, {"id": 183, "seek": 47800, "start": 479.0, "end": 482.0, "text": "后来我们初入以后", "tokens": [50414, 13547, 6912, 15003, 28727, 14028, 3588, 13547, 50564], "temperature": 0.0, "avg_logprob": -0.22204615618731524, "compression_ratio": 1.4291845493562232, "no_speech_prob": 0.0034293029457330704}, {"id": 184, "seek": 47800, "start": 482.0, "end": 487.0, "text": "有幸得到了七位强行学习领域", "tokens": [50564, 2412, 36482, 5916, 21381, 29920, 11160, 5702, 118, 8082, 29618, 2930, 254, 12501, 228, 16262, 253, 50814], "temperature": 0.0, "avg_logprob": -0.22204615618731524, "compression_ratio": 1.4291845493562232, "no_speech_prob": 0.0034293029457330704}, {"id": 185, "seek": 47800, "start": 487.0, "end": 488.0, "text": "大开的亲笔推荐", "tokens": [50814, 3582, 18937, 1546, 49296, 5437, 242, 33597, 31409, 238, 50864], "temperature": 0.0, "avg_logprob": -0.22204615618731524, "compression_ratio": 1.4291845493562232, "no_speech_prob": 0.0034293029457330704}, {"id": 186, "seek": 47800, "start": 488.0, "end": 490.0, "text": "比如说台湾大学的宁欧英老师", "tokens": [50864, 36757, 8090, 15433, 33744, 122, 3582, 29618, 1546, 2415, 223, 5988, 100, 27869, 10439, 29186, 50964], "temperature": 0.0, "avg_logprob": -0.22204615618731524, "compression_ratio": 1.4291845493562232, "no_speech_prob": 0.0034293029457330704}, {"id": 187, "seek": 47800, "start": 490.0, "end": 493.0, "text": "周伯伦老师以及李克强老师", "tokens": [50964, 37390, 7384, 107, 7384, 99, 10439, 29186, 40282, 31206, 24881, 5702, 118, 10439, 29186, 51114], "temperature": 0.0, "avg_logprob": -0.22204615618731524, "compression_ratio": 1.4291845493562232, "no_speech_prob": 0.0034293029457330704}, {"id": 188, "seek": 47800, "start": 493.0, "end": 497.0, "text": "还有比如清华的宁生梦老师", "tokens": [51114, 35091, 36757, 21784, 5322, 236, 1546, 2415, 223, 8244, 19017, 99, 10439, 29186, 51314], "temperature": 0.0, "avg_logprob": -0.22204615618731524, "compression_ratio": 1.4291845493562232, "no_speech_prob": 0.0034293029457330704}, {"id": 189, "seek": 47800, "start": 497.0, "end": 499.0, "text": "汪峥老师张伟老师", "tokens": [51314, 12800, 103, 26555, 98, 10439, 29186, 44059, 7384, 253, 10439, 29186, 51414], "temperature": 0.0, "avg_logprob": -0.22204615618731524, "compression_ratio": 1.4291845493562232, "no_speech_prob": 0.0034293029457330704}, {"id": 190, "seek": 47800, "start": 499.0, "end": 501.0, "text": "胡玉静老师等等", "tokens": [51414, 43861, 8051, 231, 5363, 247, 10439, 29186, 36000, 51514], "temperature": 0.0, "avg_logprob": -0.22204615618731524, "compression_ratio": 1.4291845493562232, "no_speech_prob": 0.0034293029457330704}, {"id": 191, "seek": 47800, "start": 502.0, "end": 503.0, "text": "然后这边我们列出了", "tokens": [51564, 26636, 5562, 40503, 15003, 43338, 7781, 2289, 51614], "temperature": 0.0, "avg_logprob": -0.22204615618731524, "compression_ratio": 1.4291845493562232, "no_speech_prob": 0.0034293029457330704}, {"id": 192, "seek": 47800, "start": 503.0, "end": 505.0, "text": "宁欧英老师的一个推荐语", "tokens": [51614, 2415, 223, 5988, 100, 27869, 10439, 29186, 1546, 20182, 33597, 31409, 238, 5233, 255, 51714], "temperature": 0.0, "avg_logprob": -0.22204615618731524, "compression_ratio": 1.4291845493562232, "no_speech_prob": 0.0034293029457330704}, {"id": 193, "seek": 47800, "start": 505.0, "end": 507.0, "text": "因为我觉得宁欧英老师的", "tokens": [51714, 34627, 45681, 2415, 223, 5988, 100, 27869, 10439, 29186, 1546, 51814], "temperature": 0.0, "avg_logprob": -0.22204615618731524, "compression_ratio": 1.4291845493562232, "no_speech_prob": 0.0034293029457330704}, {"id": 194, "seek": 50700, "start": 507.0, "end": 510.0, "text": "推荐语非常的有趣", "tokens": [50364, 33597, 31409, 238, 5233, 255, 48263, 2412, 39835, 50514], "temperature": 0.0, "avg_logprob": -0.13201366326747796, "compression_ratio": 1.3273381294964028, "no_speech_prob": 0.003593497211113572}, {"id": 195, "seek": 50700, "start": 510.0, "end": 511.0, "text": "比如他说", "tokens": [50514, 36757, 5000, 8090, 50564], "temperature": 0.0, "avg_logprob": -0.13201366326747796, "compression_ratio": 1.3273381294964028, "no_speech_prob": 0.003593497211113572}, {"id": 196, "seek": 50700, "start": 511.0, "end": 512.0, "text": "他当时看到我们这个", "tokens": [50564, 5000, 16233, 15729, 18032, 15003, 15368, 50614], "temperature": 0.0, "avg_logprob": -0.13201366326747796, "compression_ratio": 1.3273381294964028, "no_speech_prob": 0.003593497211113572}, {"id": 197, "seek": 50700, "start": 512.0, "end": 514.0, "text": "伊丽艾尔这本书的时候", "tokens": [50614, 48141, 940, 121, 12531, 122, 1530, 242, 5562, 8802, 2930, 99, 49873, 50714], "temperature": 0.0, "avg_logprob": -0.13201366326747796, "compression_ratio": 1.3273381294964028, "no_speech_prob": 0.003593497211113572}, {"id": 198, "seek": 50700, "start": 514.0, "end": 515.0, "text": "他第一个想法是", "tokens": [50714, 5000, 18049, 7549, 7093, 11148, 1541, 50764], "temperature": 0.0, "avg_logprob": -0.13201366326747796, "compression_ratio": 1.3273381294964028, "no_speech_prob": 0.003593497211113572}, {"id": 199, "seek": 50700, "start": 515.0, "end": 517.0, "text": "这成员把强化学习的知识整理得真好", "tokens": [50764, 5562, 11336, 3606, 246, 16075, 5702, 118, 23756, 29618, 2930, 254, 1546, 6498, 5233, 228, 27662, 13876, 5916, 6303, 2131, 50864], "temperature": 0.0, "avg_logprob": -0.13201366326747796, "compression_ratio": 1.3273381294964028, "no_speech_prob": 0.003593497211113572}, {"id": 200, "seek": 50700, "start": 517.0, "end": 519.0, "text": "不仅有理论说明", "tokens": [50864, 1960, 1550, 227, 2412, 13876, 7422, 118, 8090, 11100, 50964], "temperature": 0.0, "avg_logprob": -0.13201366326747796, "compression_ratio": 1.3273381294964028, "no_speech_prob": 0.003593497211113572}, {"id": 201, "seek": 50700, "start": 519.0, "end": 520.0, "text": "还加上了诚实实力", "tokens": [50964, 14852, 9990, 5708, 2289, 5233, 248, 24726, 24726, 13486, 51014], "temperature": 0.0, "avg_logprob": -0.13201366326747796, "compression_ratio": 1.3273381294964028, "no_speech_prob": 0.003593497211113572}, {"id": 202, "seek": 50700, "start": 520.0, "end": 523.0, "text": "同学们以后可以直接阅读这份教程", "tokens": [51014, 13089, 29618, 9497, 3588, 13547, 6723, 43297, 10034, 227, 5233, 119, 5562, 36266, 21936, 29649, 51164], "temperature": 0.0, "avg_logprob": -0.13201366326747796, "compression_ratio": 1.3273381294964028, "no_speech_prob": 0.003593497211113572}, {"id": 203, "seek": 50700, "start": 523.0, "end": 525.0, "text": "这样我以后上课", "tokens": [51164, 21209, 1654, 3588, 13547, 5708, 5233, 122, 51264], "temperature": 0.0, "avg_logprob": -0.13201366326747796, "compression_ratio": 1.3273381294964028, "no_speech_prob": 0.003593497211113572}, {"id": 204, "seek": 50700, "start": 525.0, "end": 528.0, "text": "就不用再讲强化学习的部分了", "tokens": [51264, 3111, 24384, 8623, 39255, 5702, 118, 23756, 29618, 2930, 254, 1546, 32174, 2289, 51414], "temperature": 0.0, "avg_logprob": -0.13201366326747796, "compression_ratio": 1.3273381294964028, "no_speech_prob": 0.003593497211113572}, {"id": 205, "seek": 50700, "start": 533.0, "end": 534.0, "text": "可以发现宁欧英对我们", "tokens": [51664, 6723, 28926, 20204, 2415, 223, 5988, 100, 27869, 8713, 15003, 51714], "temperature": 0.0, "avg_logprob": -0.13201366326747796, "compression_ratio": 1.3273381294964028, "no_speech_prob": 0.003593497211113572}, {"id": 206, "seek": 50700, "start": 534.0, "end": 536.0, "text": "这本书还是挺认可的", "tokens": [51714, 5562, 8802, 2930, 99, 45726, 41046, 7422, 97, 4429, 1546, 51814], "temperature": 0.0, "avg_logprob": -0.13201366326747796, "compression_ratio": 1.3273381294964028, "no_speech_prob": 0.003593497211113572}, {"id": 207, "seek": 53600, "start": 536.0, "end": 537.0, "text": "然后再来讲一个问题", "tokens": [50364, 26636, 8623, 6912, 39255, 20182, 34069, 50414], "temperature": 0.0, "avg_logprob": -0.11337475294477484, "compression_ratio": 1.5, "no_speech_prob": 0.0072323232889175415}, {"id": 208, "seek": 53600, "start": 537.0, "end": 539.0, "text": "这本书为什么叫蘑菇书呢", "tokens": [50414, 5562, 8802, 2930, 99, 31006, 19855, 31882, 239, 16616, 229, 2930, 99, 6240, 50514], "temperature": 0.0, "avg_logprob": -0.11337475294477484, "compression_ratio": 1.5, "no_speech_prob": 0.0072323232889175415}, {"id": 209, "seek": 53600, "start": 539.0, "end": 541.0, "text": "难道是因为作者都是吃货", "tokens": [50514, 46531, 6025, 1541, 34627, 11914, 12444, 22796, 10123, 18464, 100, 50614], "temperature": 0.0, "avg_logprob": -0.11337475294477484, "compression_ratio": 1.5, "no_speech_prob": 0.0072323232889175415}, {"id": 210, "seek": 53600, "start": 541.0, "end": 544.0, "text": "之前有一本西瓜书", "tokens": [50614, 32442, 32241, 8802, 16220, 36727, 2930, 99, 50764], "temperature": 0.0, "avg_logprob": -0.11337475294477484, "compression_ratio": 1.5, "no_speech_prob": 0.0072323232889175415}, {"id": 211, "seek": 53600, "start": 544.0, "end": 545.0, "text": "还有一本南瓜书", "tokens": [50764, 14852, 32241, 8802, 31021, 36727, 2930, 99, 50814], "temperature": 0.0, "avg_logprob": -0.11337475294477484, "compression_ratio": 1.5, "no_speech_prob": 0.0072323232889175415}, {"id": 212, "seek": 53600, "start": 545.0, "end": 547.0, "text": "然后作为一个吃货的话", "tokens": [50814, 26636, 11914, 13992, 20182, 10123, 18464, 100, 44575, 50914], "temperature": 0.0, "avg_logprob": -0.11337475294477484, "compression_ratio": 1.5, "no_speech_prob": 0.0072323232889175415}, {"id": 213, "seek": 53600, "start": 547.0, "end": 549.0, "text": "还有一个传承一下叫蘑菇书", "tokens": [50914, 35091, 20182, 7384, 254, 3416, 123, 8861, 19855, 31882, 239, 16616, 229, 2930, 99, 51014], "temperature": 0.0, "avg_logprob": -0.11337475294477484, "compression_ratio": 1.5, "no_speech_prob": 0.0072323232889175415}, {"id": 214, "seek": 53600, "start": 549.0, "end": 552.0, "text": "就吃完西瓜南瓜再来热蘑菇", "tokens": [51014, 3111, 10123, 14128, 16220, 36727, 31021, 36727, 8623, 6912, 23661, 255, 31882, 239, 16616, 229, 51164], "temperature": 0.0, "avg_logprob": -0.11337475294477484, "compression_ratio": 1.5, "no_speech_prob": 0.0072323232889175415}, {"id": 215, "seek": 53600, "start": 552.0, "end": 554.0, "text": "但其实并不是", "tokens": [51164, 8395, 41646, 3509, 114, 7296, 51264], "temperature": 0.0, "avg_logprob": -0.11337475294477484, "compression_ratio": 1.5, "no_speech_prob": 0.0072323232889175415}, {"id": 216, "seek": 53600, "start": 554.0, "end": 557.0, "text": "蘑菇书真的寓意是玛里奥", "tokens": [51264, 31882, 239, 16616, 229, 2930, 99, 8034, 4510, 241, 9042, 1541, 8051, 249, 15759, 1881, 98, 51414], "temperature": 0.0, "avg_logprob": -0.11337475294477484, "compression_ratio": 1.5, "no_speech_prob": 0.0072323232889175415}, {"id": 217, "seek": 53600, "start": 557.0, "end": 559.0, "text": "大家如果玩过超级玛里奥游戏", "tokens": [51414, 6868, 13119, 19912, 16866, 19869, 16853, 100, 8051, 249, 15759, 1881, 98, 9592, 116, 1486, 237, 51514], "temperature": 0.0, "avg_logprob": -0.11337475294477484, "compression_ratio": 1.5, "no_speech_prob": 0.0072323232889175415}, {"id": 218, "seek": 53600, "start": 559.0, "end": 561.0, "text": "就知道玛里奥吃了蘑菇以后", "tokens": [51514, 3111, 7758, 8051, 249, 15759, 1881, 98, 10123, 2289, 31882, 239, 16616, 229, 3588, 13547, 51614], "temperature": 0.0, "avg_logprob": -0.11337475294477484, "compression_ratio": 1.5, "no_speech_prob": 0.0072323232889175415}, {"id": 219, "seek": 53600, "start": 561.0, "end": 562.0, "text": "会变得更加强大", "tokens": [51614, 12949, 2129, 246, 5916, 19002, 9990, 5702, 118, 3582, 51664], "temperature": 0.0, "avg_logprob": -0.11337475294477484, "compression_ratio": 1.5, "no_speech_prob": 0.0072323232889175415}, {"id": 220, "seek": 53600, "start": 562.0, "end": 565.0, "text": "然后我们也希望读者", "tokens": [51664, 26636, 15003, 6404, 29955, 5233, 119, 12444, 51814], "temperature": 0.0, "avg_logprob": -0.11337475294477484, "compression_ratio": 1.5, "no_speech_prob": 0.0072323232889175415}, {"id": 221, "seek": 56500, "start": 566.0, "end": 569.0, "text": "在吃下这个蘑菇书以后", "tokens": [50414, 3581, 10123, 4438, 15368, 31882, 239, 16616, 229, 2930, 99, 3588, 13547, 50564], "temperature": 0.0, "avg_logprob": -0.16682559941090694, "compression_ratio": 1.2738095238095237, "no_speech_prob": 0.003123542992398143}, {"id": 222, "seek": 56500, "start": 569.0, "end": 572.0, "text": "能够养有兴致的探索强化学习", "tokens": [50564, 8225, 1787, 253, 2347, 119, 2412, 2347, 112, 6784, 112, 1546, 6900, 95, 7732, 95, 5702, 118, 23756, 29618, 2930, 254, 50714], "temperature": 0.0, "avg_logprob": -0.16682559941090694, "compression_ratio": 1.2738095238095237, "no_speech_prob": 0.003123542992398143}, {"id": 223, "seek": 56500, "start": 572.0, "end": 574.0, "text": "然后像玛里奥那样越强大", "tokens": [50714, 26636, 12760, 8051, 249, 15759, 1881, 98, 4184, 14496, 25761, 5702, 118, 3582, 50814], "temperature": 0.0, "avg_logprob": -0.16682559941090694, "compression_ratio": 1.2738095238095237, "no_speech_prob": 0.003123542992398143}, {"id": 224, "seek": 56500, "start": 574.0, "end": 578.0, "text": "然后进而在人工智能领域里面", "tokens": [50814, 26636, 36700, 11070, 3581, 4035, 23323, 5094, 118, 8225, 12501, 228, 16262, 253, 15759, 8833, 51014], "temperature": 0.0, "avg_logprob": -0.16682559941090694, "compression_ratio": 1.2738095238095237, "no_speech_prob": 0.003123542992398143}, {"id": 225, "seek": 56500, "start": 578.0, "end": 581.0, "text": "获得一些奇葩收获", "tokens": [51014, 31127, 115, 5916, 38515, 26907, 14059, 102, 18681, 31127, 115, 51164], "temperature": 0.0, "avg_logprob": -0.16682559941090694, "compression_ratio": 1.2738095238095237, "no_speech_prob": 0.003123542992398143}, {"id": 226, "seek": 56500, "start": 583.0, "end": 585.0, "text": "这边我们放出了一个小彩蛋", "tokens": [51264, 5562, 40503, 15003, 12744, 7781, 2289, 20182, 7322, 7391, 102, 35081, 51364], "temperature": 0.0, "avg_logprob": -0.16682559941090694, "compression_ratio": 1.2738095238095237, "no_speech_prob": 0.003123542992398143}, {"id": 227, "seek": 56500, "start": 585.0, "end": 588.0, "text": "就是我们的作者之一杨亦远", "tokens": [51364, 5620, 15003, 1546, 11914, 12444, 9574, 2257, 4422, 101, 1369, 99, 3316, 250, 51514], "temperature": 0.0, "avg_logprob": -0.16682559941090694, "compression_ratio": 1.2738095238095237, "no_speech_prob": 0.003123542992398143}, {"id": 228, "seek": 56500, "start": 588.0, "end": 590.0, "text": "跟宁欧英老师的一个意外接触", "tokens": [51514, 9678, 2415, 223, 5988, 100, 27869, 10439, 29186, 1546, 20182, 9042, 12022, 14468, 6758, 99, 51614], "temperature": 0.0, "avg_logprob": -0.16682559941090694, "compression_ratio": 1.2738095238095237, "no_speech_prob": 0.003123542992398143}, {"id": 229, "seek": 56500, "start": 591.0, "end": 594.0, "text": "当时杨亦远在参加一个顶位的时候", "tokens": [51664, 16233, 15729, 4422, 101, 1369, 99, 3316, 250, 3581, 2129, 224, 9990, 20182, 10178, 114, 11160, 49873, 51814], "temperature": 0.0, "avg_logprob": -0.16682559941090694, "compression_ratio": 1.2738095238095237, "no_speech_prob": 0.003123542992398143}, {"id": 230, "seek": 59400, "start": 594.0, "end": 599.0, "text": "发现宁欧英老师也在参会者名单里面", "tokens": [50364, 28926, 20204, 2415, 223, 5988, 100, 27869, 10439, 29186, 6404, 3581, 2129, 224, 12949, 12444, 15940, 47446, 15759, 8833, 50614], "temperature": 0.0, "avg_logprob": -0.13698350096778047, "compression_ratio": 1.3253012048192772, "no_speech_prob": 0.001810150220990181}, {"id": 231, "seek": 59400, "start": 599.0, "end": 601.0, "text": "他给宁欧英老师发了封邮件", "tokens": [50614, 5000, 23197, 2415, 223, 5988, 100, 27869, 10439, 29186, 28926, 2289, 1530, 223, 3023, 106, 20485, 50714], "temperature": 0.0, "avg_logprob": -0.13698350096778047, "compression_ratio": 1.3253012048192772, "no_speech_prob": 0.001810150220990181}, {"id": 232, "seek": 59400, "start": 601.0, "end": 604.0, "text": "介绍了我们当时GitHub的开源教程", "tokens": [50714, 30312, 10115, 235, 2289, 15003, 16233, 15729, 38, 270, 21150, 1546, 18937, 47402, 21936, 29649, 50864], "temperature": 0.0, "avg_logprob": -0.13698350096778047, "compression_ratio": 1.3253012048192772, "no_speech_prob": 0.001810150220990181}, {"id": 233, "seek": 59400, "start": 605.0, "end": 608.0, "text": "然后非常惊喜的就是", "tokens": [50914, 26636, 14392, 4799, 232, 13620, 1546, 5620, 51064], "temperature": 0.0, "avg_logprob": -0.13698350096778047, "compression_ratio": 1.3253012048192772, "no_speech_prob": 0.001810150220990181}, {"id": 234, "seek": 59400, "start": 608.0, "end": 611.0, "text": "宁欧英老师很快地给出了回信", "tokens": [51064, 2415, 223, 5988, 100, 27869, 10439, 29186, 4563, 10251, 10928, 23197, 7781, 2289, 8350, 17665, 51214], "temperature": 0.0, "avg_logprob": -0.13698350096778047, "compression_ratio": 1.3253012048192772, "no_speech_prob": 0.001810150220990181}, {"id": 235, "seek": 59400, "start": 611.0, "end": 615.0, "text": "然后也是对我们这个教程给出了好评", "tokens": [51214, 26636, 22021, 8713, 15003, 15368, 21936, 29649, 23197, 7781, 2289, 2131, 5233, 226, 51414], "temperature": 0.0, "avg_logprob": -0.13698350096778047, "compression_ratio": 1.3253012048192772, "no_speech_prob": 0.001810150220990181}, {"id": 236, "seek": 59400, "start": 618.0, "end": 621.0, "text": "这边列出了蘑菇书获得的一些荣誉", "tokens": [51564, 5562, 40503, 43338, 7781, 2289, 31882, 239, 16616, 229, 2930, 99, 31127, 115, 5916, 1546, 38515, 31409, 96, 3549, 231, 51714], "temperature": 0.0, "avg_logprob": -0.13698350096778047, "compression_ratio": 1.3253012048192772, "no_speech_prob": 0.001810150220990181}, {"id": 237, "seek": 59400, "start": 621.0, "end": 623.0, "text": "比如说人民邮件出版社的季度好书", "tokens": [51714, 36757, 8090, 44894, 3023, 106, 20485, 7781, 42096, 27658, 1546, 8052, 96, 13127, 2131, 2930, 99, 51814], "temperature": 0.0, "avg_logprob": -0.13698350096778047, "compression_ratio": 1.3253012048192772, "no_speech_prob": 0.001810150220990181}, {"id": 238, "seek": 62300, "start": 624.0, "end": 626.0, "text": "然后日读期期间", "tokens": [50414, 26636, 6890, 5233, 119, 16786, 16786, 31685, 50514], "temperature": 0.0, "avg_logprob": -0.19613869984944662, "compression_ratio": 1.2653846153846153, "no_speech_prob": 0.0053015341982245445}, {"id": 239, "seek": 62300, "start": 626.0, "end": 629.0, "text": "当当计算机期中网的第一名", "tokens": [50514, 16233, 16233, 7422, 94, 19497, 37960, 16786, 5975, 16469, 239, 1546, 18049, 15940, 50664], "temperature": 0.0, "avg_logprob": -0.19613869984944662, "compression_ratio": 1.2653846153846153, "no_speech_prob": 0.0053015341982245445}, {"id": 240, "seek": 62300, "start": 629.0, "end": 631.0, "text": "然后上市时间以后", "tokens": [50664, 26636, 5708, 27261, 44848, 3588, 13547, 50764], "temperature": 0.0, "avg_logprob": -0.19613869984944662, "compression_ratio": 1.2653846153846153, "no_speech_prob": 0.0053015341982245445}, {"id": 241, "seek": 62300, "start": 631.0, "end": 634.0, "text": "获得的就是京东人工智能榜的第一名", "tokens": [50764, 31127, 115, 5916, 1546, 5620, 31375, 38409, 4035, 23323, 5094, 118, 8225, 22989, 250, 1546, 18049, 15940, 50914], "temperature": 0.0, "avg_logprob": -0.19613869984944662, "compression_ratio": 1.2653846153846153, "no_speech_prob": 0.0053015341982245445}, {"id": 242, "seek": 62300, "start": 634.0, "end": 637.0, "text": "全网的推文阅读量破十万", "tokens": [50914, 11319, 16469, 239, 1546, 33597, 17174, 10034, 227, 5233, 119, 26748, 39560, 20145, 23570, 51064], "temperature": 0.0, "avg_logprob": -0.19613869984944662, "compression_ratio": 1.2653846153846153, "no_speech_prob": 0.0053015341982245445}, {"id": 243, "seek": 62300, "start": 637.0, "end": 639.0, "text": "然后目前清华大学李淑木教授", "tokens": [51064, 26636, 39004, 21784, 5322, 236, 3582, 29618, 31206, 14176, 239, 41437, 21936, 6900, 230, 51164], "temperature": 0.0, "avg_logprob": -0.19613869984944662, "compression_ratio": 1.2653846153846153, "no_speech_prob": 0.0053015341982245445}, {"id": 244, "seek": 62300, "start": 639.0, "end": 642.0, "text": "小米NLP首位科学家王斌老师", "tokens": [51164, 7322, 27742, 45, 45196, 25444, 11160, 42091, 29618, 5155, 19940, 4307, 234, 10439, 29186, 51314], "temperature": 0.0, "avg_logprob": -0.19613869984944662, "compression_ratio": 1.2653846153846153, "no_speech_prob": 0.0053015341982245445}, {"id": 245, "seek": 62300, "start": 642.0, "end": 645.0, "text": "以及百度高级研发工程师李克强老师", "tokens": [51314, 40282, 31906, 13127, 12979, 16853, 100, 23230, 242, 28926, 23323, 29649, 29186, 31206, 24881, 5702, 118, 10439, 29186, 51464], "temperature": 0.0, "avg_logprob": -0.19613869984944662, "compression_ratio": 1.2653846153846153, "no_speech_prob": 0.0053015341982245445}, {"id": 246, "seek": 62300, "start": 645.0, "end": 651.0, "text": "还有一些等20家的一个大咖公众号", "tokens": [51464, 14852, 32241, 13824, 10187, 2009, 5155, 1546, 20182, 3582, 8975, 244, 13545, 7384, 245, 26987, 51764], "temperature": 0.0, "avg_logprob": -0.19613869984944662, "compression_ratio": 1.2653846153846153, "no_speech_prob": 0.0053015341982245445}, {"id": 247, "seek": 65100, "start": 651.0, "end": 654.0, "text": "以及微博大V社区他们进行的一个转发推荐", "tokens": [50364, 40282, 39152, 5322, 248, 3582, 53, 27658, 9937, 118, 47911, 36700, 8082, 1546, 20182, 17819, 105, 28926, 33597, 31409, 238, 50514], "temperature": 0.0, "avg_logprob": -0.10672847480530952, "compression_ratio": 1.387218045112782, "no_speech_prob": 0.05418863520026207}, {"id": 248, "seek": 65100, "start": 654.0, "end": 658.0, "text": "然后这个也被推荐到华为电影大学的一个保定图书馆", "tokens": [50514, 26636, 15368, 6404, 23238, 33597, 31409, 238, 4511, 5322, 236, 13992, 42182, 16820, 3582, 29618, 1546, 20182, 24302, 12088, 3919, 122, 2930, 99, 11748, 228, 50714], "temperature": 0.0, "avg_logprob": -0.10672847480530952, "compression_ratio": 1.387218045112782, "no_speech_prob": 0.05418863520026207}, {"id": 249, "seek": 65100, "start": 659.0, "end": 662.0, "text": "蘑菇书全书一共13章可以分两部分", "tokens": [50764, 31882, 239, 16616, 229, 2930, 99, 11319, 2930, 99, 2257, 15408, 7668, 11957, 254, 6723, 6627, 36257, 32174, 50914], "temperature": 0.0, "avg_logprob": -0.10672847480530952, "compression_ratio": 1.387218045112782, "no_speech_prob": 0.05418863520026207}, {"id": 250, "seek": 65100, "start": 662.0, "end": 665.0, "text": "第一部分他介绍了强学的基础知识", "tokens": [50914, 18049, 32174, 5000, 30312, 10115, 235, 2289, 5702, 118, 29618, 1546, 26008, 38114, 222, 6498, 5233, 228, 51064], "temperature": 0.0, "avg_logprob": -0.10672847480530952, "compression_ratio": 1.387218045112782, "no_speech_prob": 0.05418863520026207}, {"id": 251, "seek": 65100, "start": 665.0, "end": 667.0, "text": "以及传统的强化学习算法", "tokens": [51064, 40282, 7384, 254, 10115, 253, 1546, 5702, 118, 23756, 29618, 2930, 254, 19497, 11148, 51164], "temperature": 0.0, "avg_logprob": -0.10672847480530952, "compression_ratio": 1.387218045112782, "no_speech_prob": 0.05418863520026207}, {"id": 252, "seek": 65100, "start": 667.0, "end": 669.0, "text": "第二部分他介绍了", "tokens": [51164, 19693, 32174, 5000, 30312, 10115, 235, 2289, 51264], "temperature": 0.0, "avg_logprob": -0.10672847480530952, "compression_ratio": 1.387218045112782, "no_speech_prob": 0.05418863520026207}, {"id": 253, "seek": 65100, "start": 669.0, "end": 671.0, "text": "适用强化学习算法", "tokens": [51264, 2215, 224, 9254, 5702, 118, 23756, 29618, 2930, 254, 19497, 11148, 51364], "temperature": 0.0, "avg_logprob": -0.10672847480530952, "compression_ratio": 1.387218045112782, "no_speech_prob": 0.05418863520026207}, {"id": 254, "seek": 65100, "start": 671.0, "end": 674.0, "text": "以及常见问题的解决方法", "tokens": [51364, 40282, 11279, 23813, 34069, 1546, 17278, 5676, 111, 9249, 11148, 51514], "temperature": 0.0, "avg_logprob": -0.10672847480530952, "compression_ratio": 1.387218045112782, "no_speech_prob": 0.05418863520026207}, {"id": 255, "seek": 65100, "start": 675.0, "end": 678.0, "text": "咱们这本书还有一些突出的特点", "tokens": [51564, 8975, 109, 9497, 5562, 8802, 2930, 99, 14852, 32241, 13824, 40859, 7781, 1546, 17682, 12579, 51714], "temperature": 0.0, "avg_logprob": -0.10672847480530952, "compression_ratio": 1.387218045112782, "no_speech_prob": 0.05418863520026207}, {"id": 256, "seek": 67800, "start": 678.0, "end": 680.0, "text": "比如第一点", "tokens": [50364, 36757, 18049, 12579, 50464], "temperature": 0.0, "avg_logprob": -0.11869018049125211, "compression_ratio": 1.5591397849462365, "no_speech_prob": 0.009125161916017532}, {"id": 257, "seek": 67800, "start": 680.0, "end": 683.0, "text": "我们会利用一些简单生动的例子来解释强化学概念", "tokens": [50464, 15003, 12949, 23700, 9254, 38515, 11249, 222, 47446, 8244, 34961, 1546, 17797, 7626, 6912, 17278, 5873, 232, 5702, 118, 23756, 29618, 26181, 33679, 50614], "temperature": 0.0, "avg_logprob": -0.11869018049125211, "compression_ratio": 1.5591397849462365, "no_speech_prob": 0.009125161916017532}, {"id": 258, "seek": 67800, "start": 683.0, "end": 686.0, "text": "比如说我们可以用这个玩视频游戏", "tokens": [50614, 36757, 8090, 15003, 6723, 9254, 15368, 19912, 40656, 39752, 9592, 116, 1486, 237, 50764], "temperature": 0.0, "avg_logprob": -0.11869018049125211, "compression_ratio": 1.5591397849462365, "no_speech_prob": 0.009125161916017532}, {"id": 259, "seek": 67800, "start": 686.0, "end": 688.0, "text": "以及下围棋的例子", "tokens": [50764, 40282, 4438, 3919, 112, 16407, 233, 1546, 17797, 7626, 50864], "temperature": 0.0, "avg_logprob": -0.11869018049125211, "compression_ratio": 1.5591397849462365, "no_speech_prob": 0.009125161916017532}, {"id": 260, "seek": 67800, "start": 688.0, "end": 691.0, "text": "来对强化学的一些基本概念做出解释", "tokens": [50864, 6912, 8713, 5702, 118, 23756, 29618, 1546, 38515, 37946, 26181, 33679, 10907, 7781, 17278, 5873, 232, 51014], "temperature": 0.0, "avg_logprob": -0.11869018049125211, "compression_ratio": 1.5591397849462365, "no_speech_prob": 0.009125161916017532}, {"id": 261, "seek": 67800, "start": 693.0, "end": 695.0, "text": "然后还有一些其他特点", "tokens": [51114, 26636, 35091, 38515, 9572, 5000, 17682, 12579, 51214], "temperature": 0.0, "avg_logprob": -0.11869018049125211, "compression_ratio": 1.5591397849462365, "no_speech_prob": 0.009125161916017532}, {"id": 262, "seek": 67800, "start": 695.0, "end": 698.0, "text": "比如说我们会对专业的一些公式", "tokens": [51214, 36757, 8090, 15003, 12949, 8713, 940, 241, 940, 248, 1546, 38515, 13545, 27584, 51364], "temperature": 0.0, "avg_logprob": -0.11869018049125211, "compression_ratio": 1.5591397849462365, "no_speech_prob": 0.009125161916017532}, {"id": 263, "seek": 67800, "start": 698.0, "end": 700.0, "text": "进行详细的推导和分析", "tokens": [51364, 36700, 8082, 5233, 99, 10115, 228, 1546, 33597, 4510, 120, 12565, 6627, 7360, 238, 51464], "temperature": 0.0, "avg_logprob": -0.11869018049125211, "compression_ratio": 1.5591397849462365, "no_speech_prob": 0.009125161916017532}, {"id": 264, "seek": 67800, "start": 700.0, "end": 702.0, "text": "这边我们以这个比尔曼方程的", "tokens": [51464, 5562, 40503, 15003, 3588, 15368, 11706, 1530, 242, 9531, 120, 9249, 29649, 1546, 51564], "temperature": 0.0, "avg_logprob": -0.11869018049125211, "compression_ratio": 1.5591397849462365, "no_speech_prob": 0.009125161916017532}, {"id": 265, "seek": 67800, "start": 702.0, "end": 704.0, "text": "这个推导过程的例子", "tokens": [51564, 15368, 33597, 4510, 120, 16866, 29649, 1546, 17797, 7626, 51664], "temperature": 0.0, "avg_logprob": -0.11869018049125211, "compression_ratio": 1.5591397849462365, "no_speech_prob": 0.009125161916017532}, {"id": 266, "seek": 67800, "start": 704.0, "end": 706.0, "text": "我们会一步一步的把这个推导过程念出来", "tokens": [51664, 15003, 12949, 2257, 31429, 2257, 31429, 1546, 16075, 15368, 33597, 4510, 120, 16866, 29649, 33679, 44561, 51764], "temperature": 0.0, "avg_logprob": -0.11869018049125211, "compression_ratio": 1.5591397849462365, "no_speech_prob": 0.009125161916017532}, {"id": 267, "seek": 67800, "start": 706.0, "end": 707.0, "text": "不会跳步走", "tokens": [51764, 47928, 29366, 31429, 9575, 51814], "temperature": 0.0, "avg_logprob": -0.11869018049125211, "compression_ratio": 1.5591397849462365, "no_speech_prob": 0.009125161916017532}, {"id": 268, "seek": 70700, "start": 707.0, "end": 709.0, "text": "然后此外呢", "tokens": [50364, 26636, 17947, 12022, 6240, 50464], "temperature": 0.0, "avg_logprob": -0.0965947115862811, "compression_ratio": 1.4560669456066946, "no_speech_prob": 0.0007436745800077915}, {"id": 269, "seek": 70700, "start": 711.0, "end": 712.0, "text": "我们还会对一些", "tokens": [50564, 15003, 14852, 12949, 8713, 38515, 50614], "temperature": 0.0, "avg_logprob": -0.0965947115862811, "compression_ratio": 1.4560669456066946, "no_speech_prob": 0.0007436745800077915}, {"id": 270, "seek": 70700, "start": 712.0, "end": 714.0, "text": "可能大家比较难以理解的地方", "tokens": [50614, 16657, 6868, 11706, 9830, 225, 46531, 3588, 13876, 17278, 1546, 30146, 50714], "temperature": 0.0, "avg_logprob": -0.0965947115862811, "compression_ratio": 1.4560669456066946, "no_speech_prob": 0.0007436745800077915}, {"id": 271, "seek": 70700, "start": 714.0, "end": 716.0, "text": "我们会加入一些注解", "tokens": [50714, 15003, 12949, 43878, 38515, 26432, 17278, 50814], "temperature": 0.0, "avg_logprob": -0.0965947115862811, "compression_ratio": 1.4560669456066946, "no_speech_prob": 0.0007436745800077915}, {"id": 272, "seek": 70700, "start": 716.0, "end": 717.0, "text": "通过这些注解", "tokens": [50814, 19550, 16866, 5562, 13824, 26432, 17278, 50864], "temperature": 0.0, "avg_logprob": -0.0965947115862811, "compression_ratio": 1.4560669456066946, "no_speech_prob": 0.0007436745800077915}, {"id": 273, "seek": 70700, "start": 717.0, "end": 720.0, "text": "我们会让大家更容易理解一些相关的概念", "tokens": [50864, 15003, 12949, 33650, 6868, 19002, 49212, 13876, 17278, 38515, 15106, 28053, 1546, 26181, 33679, 51014], "temperature": 0.0, "avg_logprob": -0.0965947115862811, "compression_ratio": 1.4560669456066946, "no_speech_prob": 0.0007436745800077915}, {"id": 274, "seek": 70700, "start": 723.0, "end": 726.0, "text": "此外本书配有对应的一个观念词", "tokens": [51164, 17947, 12022, 8802, 2930, 99, 38846, 2412, 8713, 44297, 1546, 20182, 42350, 33679, 5233, 235, 51314], "temperature": 0.0, "avg_logprob": -0.0965947115862811, "compression_ratio": 1.4560669456066946, "no_speech_prob": 0.0007436745800077915}, {"id": 275, "seek": 70700, "start": 726.0, "end": 728.0, "text": "习题和面试题", "tokens": [51314, 2930, 254, 30716, 12565, 8833, 5233, 243, 30716, 51414], "temperature": 0.0, "avg_logprob": -0.0965947115862811, "compression_ratio": 1.4560669456066946, "no_speech_prob": 0.0007436745800077915}, {"id": 276, "seek": 70700, "start": 728.0, "end": 730.0, "text": "当读者读完一章以后", "tokens": [51414, 16233, 5233, 119, 12444, 5233, 119, 14128, 2257, 11957, 254, 3588, 13547, 51514], "temperature": 0.0, "avg_logprob": -0.0965947115862811, "compression_ratio": 1.4560669456066946, "no_speech_prob": 0.0007436745800077915}, {"id": 277, "seek": 70700, "start": 730.0, "end": 733.0, "text": "大家可以通过观念词来快速的掌握重点", "tokens": [51514, 6868, 6723, 19550, 16866, 42350, 33679, 5233, 235, 6912, 10251, 31217, 1546, 6900, 234, 11673, 94, 12624, 12579, 51664], "temperature": 0.0, "avg_logprob": -0.0965947115862811, "compression_ratio": 1.4560669456066946, "no_speech_prob": 0.0007436745800077915}, {"id": 278, "seek": 70700, "start": 733.0, "end": 736.0, "text": "然后一个通过习题和面试题", "tokens": [51664, 26636, 20182, 19550, 16866, 2930, 254, 30716, 12565, 8833, 5233, 243, 30716, 51814], "temperature": 0.0, "avg_logprob": -0.0965947115862811, "compression_ratio": 1.4560669456066946, "no_speech_prob": 0.0007436745800077915}, {"id": 279, "seek": 73600, "start": 736.0, "end": 738.0, "text": "来巩固对知识的理解", "tokens": [50364, 6912, 5238, 102, 3919, 118, 8713, 6498, 5233, 228, 1546, 13876, 17278, 50464], "temperature": 0.0, "avg_logprob": -0.09457344149950869, "compression_ratio": 1.511111111111111, "no_speech_prob": 0.0009110302198678255}, {"id": 280, "seek": 73600, "start": 738.0, "end": 740.0, "text": "然后这个习题和面试题", "tokens": [50464, 26636, 15368, 2930, 254, 30716, 12565, 8833, 5233, 243, 30716, 50564], "temperature": 0.0, "avg_logprob": -0.09457344149950869, "compression_ratio": 1.511111111111111, "no_speech_prob": 0.0009110302198678255}, {"id": 281, "seek": 73600, "start": 740.0, "end": 742.0, "text": "也方便大家的一个程度补缺", "tokens": [50564, 6404, 9249, 27364, 6868, 1546, 20182, 29649, 13127, 9890, 98, 38109, 118, 50664], "temperature": 0.0, "avg_logprob": -0.09457344149950869, "compression_ratio": 1.511111111111111, "no_speech_prob": 0.0009110302198678255}, {"id": 282, "seek": 73600, "start": 742.0, "end": 743.0, "text": "然后最后呢", "tokens": [50664, 26636, 8661, 13547, 6240, 50714], "temperature": 0.0, "avg_logprob": -0.09457344149950869, "compression_ratio": 1.511111111111111, "no_speech_prob": 0.0009110302198678255}, {"id": 283, "seek": 73600, "start": 743.0, "end": 745.0, "text": "我们会有对应的代码实战", "tokens": [50714, 15003, 12949, 2412, 8713, 44297, 1546, 19105, 23230, 223, 24726, 1486, 246, 50814], "temperature": 0.0, "avg_logprob": -0.09457344149950869, "compression_ratio": 1.511111111111111, "no_speech_prob": 0.0009110302198678255}, {"id": 284, "seek": 73600, "start": 745.0, "end": 747.0, "text": "大家学完这个理论以后", "tokens": [50814, 6868, 29618, 14128, 15368, 13876, 7422, 118, 3588, 13547, 50914], "temperature": 0.0, "avg_logprob": -0.09457344149950869, "compression_ratio": 1.511111111111111, "no_speech_prob": 0.0009110302198678255}, {"id": 285, "seek": 73600, "start": 747.0, "end": 750.0, "text": "还要通过动手来把这个代码进行", "tokens": [50914, 14852, 4275, 19550, 16866, 34961, 11389, 6912, 16075, 15368, 19105, 23230, 223, 36700, 8082, 51064], "temperature": 0.0, "avg_logprob": -0.09457344149950869, "compression_ratio": 1.511111111111111, "no_speech_prob": 0.0009110302198678255}, {"id": 286, "seek": 73600, "start": 750.0, "end": 752.0, "text": "来把这个算法进行一个实践", "tokens": [51064, 6912, 16075, 15368, 19497, 11148, 36700, 8082, 20182, 24726, 6563, 113, 51164], "temperature": 0.0, "avg_logprob": -0.09457344149950869, "compression_ratio": 1.511111111111111, "no_speech_prob": 0.0009110302198678255}, {"id": 287, "seek": 73600, "start": 752.0, "end": 755.0, "text": "就是大家把这个算法进行实践以后", "tokens": [51164, 5620, 6868, 16075, 15368, 19497, 11148, 36700, 8082, 24726, 6563, 113, 3588, 13547, 51314], "temperature": 0.0, "avg_logprob": -0.09457344149950869, "compression_ratio": 1.511111111111111, "no_speech_prob": 0.0009110302198678255}, {"id": 288, "seek": 73600, "start": 755.0, "end": 757.0, "text": "大家才算对这个算法", "tokens": [51314, 6868, 18888, 19497, 8713, 15368, 19497, 11148, 51414], "temperature": 0.0, "avg_logprob": -0.09457344149950869, "compression_ratio": 1.511111111111111, "no_speech_prob": 0.0009110302198678255}, {"id": 289, "seek": 73600, "start": 757.0, "end": 759.0, "text": "有一个比较深入了解", "tokens": [51414, 2412, 20182, 11706, 9830, 225, 24043, 14028, 2289, 17278, 51514], "temperature": 0.0, "avg_logprob": -0.09457344149950869, "compression_ratio": 1.511111111111111, "no_speech_prob": 0.0009110302198678255}, {"id": 290, "seek": 73600, "start": 761.0, "end": 763.0, "text": "接下来到最后一部分", "tokens": [51614, 14468, 4438, 6912, 4511, 8661, 13547, 2257, 32174, 51714], "temperature": 0.0, "avg_logprob": -0.09457344149950869, "compression_ratio": 1.511111111111111, "no_speech_prob": 0.0009110302198678255}, {"id": 291, "seek": 73600, "start": 763.0, "end": 765.0, "text": "叫这本书怎么学比较高效", "tokens": [51714, 19855, 5562, 8802, 2930, 99, 15282, 29618, 11706, 9830, 225, 12979, 43076, 51814], "temperature": 0.0, "avg_logprob": -0.09457344149950869, "compression_ratio": 1.511111111111111, "no_speech_prob": 0.0009110302198678255}, {"id": 292, "seek": 76500, "start": 766.0, "end": 768.0, "text": "第一个", "tokens": [50414, 18049, 7549, 50514], "temperature": 0.0, "avg_logprob": -0.11147176715689645, "compression_ratio": 1.3969465648854962, "no_speech_prob": 0.0010649428004398942}, {"id": 293, "seek": 76500, "start": 768.0, "end": 769.0, "text": "当然你可以把这本书", "tokens": [50514, 40486, 42766, 16075, 5562, 8802, 2930, 99, 50564], "temperature": 0.0, "avg_logprob": -0.11147176715689645, "compression_ratio": 1.3969465648854962, "no_speech_prob": 0.0010649428004398942}, {"id": 294, "seek": 76500, "start": 769.0, "end": 771.0, "text": "作为三门公开课的一个必要教材", "tokens": [50564, 11914, 13992, 10960, 8259, 101, 13545, 18937, 5233, 122, 1546, 20182, 28531, 4275, 21936, 4422, 238, 50664], "temperature": 0.0, "avg_logprob": -0.11147176715689645, "compression_ratio": 1.3969465648854962, "no_speech_prob": 0.0010649428004398942}, {"id": 295, "seek": 76500, "start": 771.0, "end": 774.0, "text": "就是当你在看这三门课的时候", "tokens": [50664, 5620, 16233, 41410, 4200, 5562, 10960, 8259, 101, 5233, 122, 49873, 50814], "temperature": 0.0, "avg_logprob": -0.11147176715689645, "compression_ratio": 1.3969465648854962, "no_speech_prob": 0.0010649428004398942}, {"id": 296, "seek": 76500, "start": 774.0, "end": 778.0, "text": "如果你发现有哪些概念不是很清楚的情况下", "tokens": [50814, 45669, 28926, 20204, 2412, 17028, 13824, 26181, 33679, 7296, 4563, 38433, 1546, 46514, 4438, 51014], "temperature": 0.0, "avg_logprob": -0.11147176715689645, "compression_ratio": 1.3969465648854962, "no_speech_prob": 0.0010649428004398942}, {"id": 297, "seek": 76500, "start": 778.0, "end": 780.0, "text": "你是可以直接翻到", "tokens": [51014, 32526, 6723, 43297, 42716, 4511, 51114], "temperature": 0.0, "avg_logprob": -0.11147176715689645, "compression_ratio": 1.3969465648854962, "no_speech_prob": 0.0010649428004398942}, {"id": 298, "seek": 76500, "start": 780.0, "end": 782.0, "text": "本书对应的知识点进行学习", "tokens": [51114, 8802, 2930, 99, 8713, 44297, 1546, 6498, 5233, 228, 12579, 36700, 8082, 29618, 2930, 254, 51214], "temperature": 0.0, "avg_logprob": -0.11147176715689645, "compression_ratio": 1.3969465648854962, "no_speech_prob": 0.0010649428004398942}, {"id": 299, "seek": 76500, "start": 782.0, "end": 785.0, "text": "当然本书也是完全图形于三门教材的", "tokens": [51214, 40486, 8802, 2930, 99, 22021, 37100, 3919, 122, 30900, 37732, 10960, 8259, 101, 21936, 4422, 238, 1546, 51364], "temperature": 0.0, "avg_logprob": -0.11147176715689645, "compression_ratio": 1.3969465648854962, "no_speech_prob": 0.0010649428004398942}, {"id": 300, "seek": 76500, "start": 785.0, "end": 788.0, "text": "大家可以直接预读本书进行学习", "tokens": [51364, 6868, 6723, 43297, 12501, 226, 5233, 119, 8802, 2930, 99, 36700, 8082, 29618, 2930, 254, 51514], "temperature": 0.0, "avg_logprob": -0.11147176715689645, "compression_ratio": 1.3969465648854962, "no_speech_prob": 0.0010649428004398942}, {"id": 301, "seek": 76500, "start": 790.0, "end": 792.0, "text": "本书在GitHub上面", "tokens": [51614, 8802, 2930, 99, 3581, 38, 270, 21150, 49750, 51714], "temperature": 0.0, "avg_logprob": -0.11147176715689645, "compression_ratio": 1.3969465648854962, "no_speech_prob": 0.0010649428004398942}, {"id": 302, "seek": 76500, "start": 792.0, "end": 793.0, "text": "配有对应的代码", "tokens": [51714, 38846, 2412, 8713, 44297, 1546, 19105, 23230, 223, 51764], "temperature": 0.0, "avg_logprob": -0.11147176715689645, "compression_ratio": 1.3969465648854962, "no_speech_prob": 0.0010649428004398942}, {"id": 303, "seek": 79300, "start": 793.0, "end": 795.0, "text": "这个代码也会适宜的更新", "tokens": [50364, 15368, 19105, 23230, 223, 6404, 12949, 2215, 224, 2415, 250, 1546, 19002, 12560, 50464], "temperature": 0.0, "avg_logprob": -0.13516669119558028, "compression_ratio": 1.3231441048034935, "no_speech_prob": 0.0034295176155865192}, {"id": 304, "seek": 79300, "start": 795.0, "end": 799.0, "text": "大家如果只是想很快的", "tokens": [50464, 6868, 13119, 36859, 7093, 4563, 10251, 1546, 50664], "temperature": 0.0, "avg_logprob": -0.13516669119558028, "compression_ratio": 1.3231441048034935, "no_speech_prob": 0.0034295176155865192}, {"id": 305, "seek": 79300, "start": 799.0, "end": 801.0, "text": "把这个算法给应用上", "tokens": [50664, 16075, 15368, 19497, 11148, 23197, 44297, 9254, 5708, 50764], "temperature": 0.0, "avg_logprob": -0.13516669119558028, "compression_ratio": 1.3231441048034935, "no_speech_prob": 0.0034295176155865192}, {"id": 306, "seek": 79300, "start": 801.0, "end": 802.0, "text": "大家可以直接去GitHub上", "tokens": [50764, 6868, 6723, 43297, 6734, 38, 270, 21150, 5708, 50814], "temperature": 0.0, "avg_logprob": -0.13516669119558028, "compression_ratio": 1.3231441048034935, "no_speech_prob": 0.0034295176155865192}, {"id": 307, "seek": 79300, "start": 802.0, "end": 804.0, "text": "跑对应的代码", "tokens": [50814, 32585, 8713, 44297, 1546, 19105, 23230, 223, 50914], "temperature": 0.0, "avg_logprob": -0.13516669119558028, "compression_ratio": 1.3231441048034935, "no_speech_prob": 0.0034295176155865192}, {"id": 308, "seek": 79300, "start": 806.0, "end": 811.0, "text": "此外本书还有相关的一个刊物和修订", "tokens": [51014, 17947, 12022, 8802, 2930, 99, 35091, 15106, 28053, 1546, 20182, 2437, 232, 23516, 12565, 7792, 106, 7422, 95, 51264], "temperature": 0.0, "avg_logprob": -0.13516669119558028, "compression_ratio": 1.3231441048034935, "no_speech_prob": 0.0034295176155865192}, {"id": 309, "seek": 79300, "start": 811.0, "end": 812.0, "text": "这些刊物和修订", "tokens": [51264, 5562, 13824, 2437, 232, 23516, 12565, 7792, 106, 7422, 95, 51314], "temperature": 0.0, "avg_logprob": -0.13516669119558028, "compression_ratio": 1.3231441048034935, "no_speech_prob": 0.0034295176155865192}, {"id": 310, "seek": 79300, "start": 812.0, "end": 814.0, "text": "也会根据大家的反馈的一些意见", "tokens": [51314, 6404, 12949, 31337, 26075, 106, 6868, 1546, 22138, 11748, 230, 1546, 38515, 9042, 23813, 51414], "temperature": 0.0, "avg_logprob": -0.13516669119558028, "compression_ratio": 1.3231441048034935, "no_speech_prob": 0.0034295176155865192}, {"id": 311, "seek": 79300, "start": 814.0, "end": 816.0, "text": "进行一些适宜的更新", "tokens": [51414, 36700, 8082, 38515, 2215, 224, 2415, 250, 1546, 19002, 12560, 51514], "temperature": 0.0, "avg_logprob": -0.13516669119558028, "compression_ratio": 1.3231441048034935, "no_speech_prob": 0.0034295176155865192}, {"id": 312, "seek": 79300, "start": 818.0, "end": 820.0, "text": "然后这边也要讲一下", "tokens": [51614, 26636, 5562, 40503, 6404, 4275, 39255, 8861, 51714], "temperature": 0.0, "avg_logprob": -0.13516669119558028, "compression_ratio": 1.3231441048034935, "no_speech_prob": 0.0034295176155865192}, {"id": 313, "seek": 82000, "start": 820.0, "end": 823.0, "text": "本书它在GitHub有个叫PK版", "tokens": [50364, 8802, 2930, 99, 11284, 3581, 38, 270, 21150, 2412, 7549, 19855, 47, 42, 42096, 50514], "temperature": 0.0, "avg_logprob": -0.17058783401677638, "compression_ratio": 1.437984496124031, "no_speech_prob": 0.01406256016343832}, {"id": 314, "seek": 82000, "start": 823.0, "end": 826.0, "text": "然后它还有这种纸质书的版本叫纸质版", "tokens": [50514, 26636, 11284, 35091, 5562, 39810, 16853, 116, 18464, 101, 2930, 99, 1546, 42096, 8802, 19855, 16853, 116, 18464, 101, 42096, 50664], "temperature": 0.0, "avg_logprob": -0.17058783401677638, "compression_ratio": 1.437984496124031, "no_speech_prob": 0.01406256016343832}, {"id": 315, "seek": 82000, "start": 826.0, "end": 827.0, "text": "它们有什么区别呢", "tokens": [50664, 11284, 9497, 2412, 10440, 9937, 118, 18453, 6240, 50714], "temperature": 0.0, "avg_logprob": -0.17058783401677638, "compression_ratio": 1.437984496124031, "no_speech_prob": 0.01406256016343832}, {"id": 316, "seek": 82000, "start": 827.0, "end": 830.0, "text": "就GitHub的那种PK版本是本书的一个初稿", "tokens": [50714, 3111, 38, 270, 21150, 1546, 4184, 39810, 47, 42, 42096, 8802, 1541, 8802, 2930, 99, 1546, 20182, 28727, 10415, 123, 50864], "temperature": 0.0, "avg_logprob": -0.17058783401677638, "compression_ratio": 1.437984496124031, "no_speech_prob": 0.01406256016343832}, {"id": 317, "seek": 82000, "start": 830.0, "end": 833.0, "text": "然后在本书的作者已经憋进来", "tokens": [50864, 26636, 3581, 8802, 2930, 99, 1546, 11914, 12444, 49161, 29713, 233, 36700, 6912, 51014], "temperature": 0.0, "avg_logprob": -0.17058783401677638, "compression_ratio": 1.437984496124031, "no_speech_prob": 0.01406256016343832}, {"id": 318, "seek": 82000, "start": 833.0, "end": 834.0, "text": "不断的修改中", "tokens": [51014, 1960, 4307, 255, 1546, 7792, 106, 34490, 5975, 51064], "temperature": 0.0, "avg_logprob": -0.17058783401677638, "compression_ratio": 1.437984496124031, "no_speech_prob": 0.01406256016343832}, {"id": 319, "seek": 82000, "start": 835.0, "end": 838.0, "text": "我们最后有了纸质版", "tokens": [51114, 15003, 8661, 13547, 2412, 2289, 16853, 116, 18464, 101, 42096, 51264], "temperature": 0.0, "avg_logprob": -0.17058783401677638, "compression_ratio": 1.437984496124031, "no_speech_prob": 0.01406256016343832}, {"id": 320, "seek": 82000, "start": 839.0, "end": 841.0, "text": "大家看到我们", "tokens": [51314, 6868, 18032, 15003, 51414], "temperature": 0.0, "avg_logprob": -0.17058783401677638, "compression_ratio": 1.437984496124031, "no_speech_prob": 0.01406256016343832}, {"id": 321, "seek": 82000, "start": 841.0, "end": 844.0, "text": "对这个PK版本里面有了大量的修订", "tokens": [51414, 8713, 15368, 47, 42, 42096, 8802, 15759, 8833, 2412, 2289, 3582, 26748, 1546, 7792, 106, 7422, 95, 51564], "temperature": 0.0, "avg_logprob": -0.17058783401677638, "compression_ratio": 1.437984496124031, "no_speech_prob": 0.01406256016343832}, {"id": 322, "seek": 82000, "start": 844.0, "end": 846.0, "text": "所以说纸质书的一个质量", "tokens": [51564, 7239, 8090, 16853, 116, 18464, 101, 2930, 99, 1546, 20182, 18464, 101, 26748, 51664], "temperature": 0.0, "avg_logprob": -0.17058783401677638, "compression_ratio": 1.437984496124031, "no_speech_prob": 0.01406256016343832}, {"id": 323, "seek": 82000, "start": 846.0, "end": 849.0, "text": "相对于PK版本是要高不少的", "tokens": [51664, 15106, 8713, 37732, 47, 42, 42096, 8802, 1541, 4275, 12979, 1960, 15686, 1546, 51814], "temperature": 0.0, "avg_logprob": -0.17058783401677638, "compression_ratio": 1.437984496124031, "no_speech_prob": 0.01406256016343832}, {"id": 324, "seek": 85000, "start": 851.0, "end": 853.0, "text": "讲到这里大家可能会有疑问", "tokens": [50414, 39255, 4511, 35102, 6868, 16657, 12949, 2412, 18140, 239, 22064, 50514], "temperature": 0.0, "avg_logprob": -0.12069139151737608, "compression_ratio": 1.3584905660377358, "no_speech_prob": 0.0009547214140184224}, {"id": 325, "seek": 85000, "start": 855.0, "end": 858.0, "text": "可能是如果我目前我涉及的工作", "tokens": [50614, 16657, 1541, 13119, 1654, 39004, 1654, 35681, 231, 25703, 1546, 41315, 50764], "temperature": 0.0, "avg_logprob": -0.12069139151737608, "compression_ratio": 1.3584905660377358, "no_speech_prob": 0.0009547214140184224}, {"id": 326, "seek": 85000, "start": 858.0, "end": 859.0, "text": "不涉及到强化学习", "tokens": [50764, 1960, 35681, 231, 25703, 4511, 5702, 118, 23756, 29618, 2930, 254, 50814], "temperature": 0.0, "avg_logprob": -0.12069139151737608, "compression_ratio": 1.3584905660377358, "no_speech_prob": 0.0009547214140184224}, {"id": 327, "seek": 85000, "start": 859.0, "end": 861.0, "text": "那我还有必要学强化学习吗", "tokens": [50814, 4184, 1654, 35091, 28531, 4275, 29618, 5702, 118, 23756, 29618, 2930, 254, 14769, 50914], "temperature": 0.0, "avg_logprob": -0.12069139151737608, "compression_ratio": 1.3584905660377358, "no_speech_prob": 0.0009547214140184224}, {"id": 328, "seek": 85000, "start": 863.0, "end": 865.0, "text": "对于这个问题我想用乔布斯的观点", "tokens": [51014, 8713, 37732, 15368, 34069, 25246, 9254, 2930, 242, 34688, 30758, 1546, 42350, 12579, 51114], "temperature": 0.0, "avg_logprob": -0.12069139151737608, "compression_ratio": 1.3584905660377358, "no_speech_prob": 0.0009547214140184224}, {"id": 329, "seek": 85000, "start": 865.0, "end": 866.0, "text": "叫黏生命的点", "tokens": [51114, 19855, 6173, 237, 8244, 25236, 1546, 12579, 51164], "temperature": 0.0, "avg_logprob": -0.12069139151737608, "compression_ratio": 1.3584905660377358, "no_speech_prob": 0.0009547214140184224}, {"id": 330, "seek": 85000, "start": 866.0, "end": 867.0, "text": "或者叫应用相连", "tokens": [51164, 31148, 19855, 44297, 9254, 15106, 3316, 252, 51214], "temperature": 0.0, "avg_logprob": -0.12069139151737608, "compression_ratio": 1.3584905660377358, "no_speech_prob": 0.0009547214140184224}, {"id": 331, "seek": 85000, "start": 869.0, "end": 871.0, "text": "这边就举一下乔布斯他本人的例子", "tokens": [51314, 5562, 40503, 3111, 940, 122, 8861, 2930, 242, 34688, 30758, 5000, 8802, 4035, 1546, 17797, 7626, 51414], "temperature": 0.0, "avg_logprob": -0.12069139151737608, "compression_ratio": 1.3584905660377358, "no_speech_prob": 0.0009547214140184224}, {"id": 332, "seek": 85000, "start": 872.0, "end": 875.0, "text": "乔布斯当时在大学学了一门书法课", "tokens": [51464, 2930, 242, 34688, 30758, 16233, 15729, 3581, 3582, 29618, 29618, 2289, 2257, 8259, 101, 2930, 99, 11148, 5233, 122, 51614], "temperature": 0.0, "avg_logprob": -0.12069139151737608, "compression_ratio": 1.3584905660377358, "no_speech_prob": 0.0009547214140184224}, {"id": 333, "seek": 85000, "start": 875.0, "end": 878.0, "text": "这门课在很多人眼里都是没有用处的", "tokens": [51614, 5562, 8259, 101, 5233, 122, 3581, 20778, 4035, 25281, 15759, 22796, 17944, 9254, 1787, 226, 1546, 51764], "temperature": 0.0, "avg_logprob": -0.12069139151737608, "compression_ratio": 1.3584905660377358, "no_speech_prob": 0.0009547214140184224}, {"id": 334, "seek": 87800, "start": 878.0, "end": 880.0, "text": "而乔布斯他本人也是出于兴趣", "tokens": [50364, 11070, 2930, 242, 34688, 30758, 5000, 8802, 4035, 22021, 7781, 37732, 2347, 112, 39835, 50464], "temperature": 0.0, "avg_logprob": -0.10959936633254543, "compression_ratio": 1.3794642857142858, "no_speech_prob": 0.001674364204518497}, {"id": 335, "seek": 87800, "start": 880.0, "end": 881.0, "text": "才学这门课", "tokens": [50464, 18888, 29618, 5562, 8259, 101, 5233, 122, 50514], "temperature": 0.0, "avg_logprob": -0.10959936633254543, "compression_ratio": 1.3794642857142858, "no_speech_prob": 0.001674364204518497}, {"id": 336, "seek": 87800, "start": 884.0, "end": 888.0, "text": "他也没有对这门课抱有很大的期望", "tokens": [50664, 5000, 6404, 17944, 8713, 5562, 8259, 101, 5233, 122, 38382, 2412, 4563, 39156, 16786, 22694, 50864], "temperature": 0.0, "avg_logprob": -0.10959936633254543, "compression_ratio": 1.3794642857142858, "no_speech_prob": 0.001674364204518497}, {"id": 337, "seek": 87800, "start": 888.0, "end": 890.0, "text": "但是后来发现他这门课", "tokens": [50864, 11189, 13547, 6912, 28926, 20204, 5000, 5562, 8259, 101, 5233, 122, 50964], "temperature": 0.0, "avg_logprob": -0.10959936633254543, "compression_ratio": 1.3794642857142858, "no_speech_prob": 0.001674364204518497}, {"id": 338, "seek": 87800, "start": 890.0, "end": 893.0, "text": "在设计苹果电脑字体的时候", "tokens": [50964, 3581, 7422, 122, 7422, 94, 13736, 117, 9319, 42182, 27067, 239, 22381, 29485, 49873, 51114], "temperature": 0.0, "avg_logprob": -0.10959936633254543, "compression_ratio": 1.3794642857142858, "no_speech_prob": 0.001674364204518497}, {"id": 339, "seek": 87800, "start": 893.0, "end": 894.0, "text": "取到了极大作用", "tokens": [51114, 29436, 21381, 7360, 223, 3582, 11914, 9254, 51164], "temperature": 0.0, "avg_logprob": -0.10959936633254543, "compression_ratio": 1.3794642857142858, "no_speech_prob": 0.001674364204518497}, {"id": 340, "seek": 87800, "start": 895.0, "end": 898.0, "text": "如果乔布斯当时没有学这门课的话", "tokens": [51214, 13119, 2930, 242, 34688, 30758, 16233, 15729, 17944, 29618, 5562, 8259, 101, 5233, 122, 44575, 51364], "temperature": 0.0, "avg_logprob": -0.10959936633254543, "compression_ratio": 1.3794642857142858, "no_speech_prob": 0.001674364204518497}, {"id": 341, "seek": 87800, "start": 898.0, "end": 899.0, "text": "大家可能就看不到", "tokens": [51364, 6868, 16657, 3111, 4200, 35728, 51414], "temperature": 0.0, "avg_logprob": -0.10959936633254543, "compression_ratio": 1.3794642857142858, "no_speech_prob": 0.001674364204518497}, {"id": 342, "seek": 87800, "start": 899.0, "end": 905.0, "text": "苹果电脑中这些优美丰富赏意悦目的字体", "tokens": [51414, 13736, 117, 9319, 42182, 27067, 239, 5975, 5562, 13824, 7384, 246, 9175, 940, 108, 47564, 5266, 237, 9042, 14696, 99, 11386, 1546, 22381, 29485, 51714], "temperature": 0.0, "avg_logprob": -0.10959936633254543, "compression_ratio": 1.3794642857142858, "no_speech_prob": 0.001674364204518497}, {"id": 343, "seek": 90500, "start": 906.0, "end": 910.0, "text": "而对强化学习来说", "tokens": [50414, 11070, 8713, 5702, 118, 23756, 29618, 2930, 254, 6912, 8090, 50614], "temperature": 0.0, "avg_logprob": -0.10653678391330926, "compression_ratio": 1.5138888888888888, "no_speech_prob": 0.002934876596555114}, {"id": 344, "seek": 90500, "start": 910.0, "end": 913.0, "text": "强化学习这种应用非常广泛的技术", "tokens": [50614, 5702, 118, 23756, 29618, 2930, 254, 5562, 39810, 44297, 9254, 14392, 3509, 123, 6847, 249, 1546, 32502, 1474, 107, 50764], "temperature": 0.0, "avg_logprob": -0.10653678391330926, "compression_ratio": 1.5138888888888888, "no_speech_prob": 0.002934876596555114}, {"id": 345, "seek": 90500, "start": 913.0, "end": 917.0, "text": "即使现在可能跟你的工作没有很大关联", "tokens": [50764, 39127, 22982, 25040, 16657, 9678, 18961, 41315, 17944, 4563, 3582, 28053, 8171, 242, 50964], "temperature": 0.0, "avg_logprob": -0.10653678391330926, "compression_ratio": 1.5138888888888888, "no_speech_prob": 0.002934876596555114}, {"id": 346, "seek": 90500, "start": 917.0, "end": 918.0, "text": "但说不定某一天", "tokens": [50964, 8395, 8090, 1960, 12088, 17238, 238, 2257, 6135, 51014], "temperature": 0.0, "avg_logprob": -0.10653678391330926, "compression_ratio": 1.5138888888888888, "no_speech_prob": 0.002934876596555114}, {"id": 347, "seek": 90500, "start": 918.0, "end": 921.0, "text": "可能你的工作就要涉及到相关的", "tokens": [51014, 16657, 18961, 41315, 3111, 4275, 35681, 231, 25703, 4511, 15106, 28053, 1546, 51164], "temperature": 0.0, "avg_logprob": -0.10653678391330926, "compression_ratio": 1.5138888888888888, "no_speech_prob": 0.002934876596555114}, {"id": 348, "seek": 90500, "start": 921.0, "end": 922.0, "text": "涉及到强化学习", "tokens": [51164, 35681, 231, 25703, 4511, 5702, 118, 23756, 29618, 2930, 254, 51214], "temperature": 0.0, "avg_logprob": -0.10653678391330926, "compression_ratio": 1.5138888888888888, "no_speech_prob": 0.002934876596555114}, {"id": 349, "seek": 90500, "start": 922.0, "end": 924.0, "text": "这时候如果你在之前", "tokens": [51214, 5562, 29111, 13119, 41410, 32442, 51314], "temperature": 0.0, "avg_logprob": -0.10653678391330926, "compression_ratio": 1.5138888888888888, "no_speech_prob": 0.002934876596555114}, {"id": 350, "seek": 90500, "start": 924.0, "end": 927.0, "text": "对强化学习有一个基本了解", "tokens": [51314, 8713, 5702, 118, 23756, 29618, 2930, 254, 2412, 20182, 37946, 2289, 17278, 51464], "temperature": 0.0, "avg_logprob": -0.10653678391330926, "compression_ratio": 1.5138888888888888, "no_speech_prob": 0.002934876596555114}, {"id": 351, "seek": 90500, "start": 927.0, "end": 928.0, "text": "或者说入门的强化学习", "tokens": [51464, 31148, 8090, 14028, 8259, 101, 1546, 5702, 118, 23756, 29618, 2930, 254, 51514], "temperature": 0.0, "avg_logprob": -0.10653678391330926, "compression_ratio": 1.5138888888888888, "no_speech_prob": 0.002934876596555114}, {"id": 352, "seek": 90500, "start": 928.0, "end": 931.0, "text": "这时候可能对你的工作", "tokens": [51514, 5562, 29111, 16657, 8713, 18961, 41315, 51664], "temperature": 0.0, "avg_logprob": -0.10653678391330926, "compression_ratio": 1.5138888888888888, "no_speech_prob": 0.002934876596555114}, {"id": 353, "seek": 93100, "start": 932.0, "end": 935.0, "text": "会起到一个意想不到的助理", "tokens": [50414, 12949, 9147, 4511, 20182, 9042, 7093, 35728, 1546, 37618, 13876, 50564], "temperature": 0.0, "avg_logprob": -0.15739509838969767, "compression_ratio": 1.3333333333333333, "no_speech_prob": 0.17098036408424377}, {"id": 354, "seek": 93100, "start": 939.0, "end": 942.0, "text": "最后我们念出了蘑菇书", "tokens": [50764, 8661, 13547, 15003, 33679, 7781, 2289, 31882, 239, 16616, 229, 2930, 99, 50914], "temperature": 0.0, "avg_logprob": -0.15739509838969767, "compression_ratio": 1.3333333333333333, "no_speech_prob": 0.17098036408424377}, {"id": 355, "seek": 93100, "start": 942.0, "end": 945.0, "text": "在京东以及档案的一个购买的链接", "tokens": [50914, 3581, 31375, 38409, 40282, 21416, 96, 28899, 1546, 20182, 18464, 255, 2930, 108, 1546, 165, 241, 122, 14468, 51064], "temperature": 0.0, "avg_logprob": -0.15739509838969767, "compression_ratio": 1.3333333333333333, "no_speech_prob": 0.17098036408424377}, {"id": 356, "seek": 93100, "start": 945.0, "end": 946.0, "text": "以及对应的二维码", "tokens": [51064, 40282, 8713, 44297, 1546, 11217, 10115, 112, 23230, 223, 51114], "temperature": 0.0, "avg_logprob": -0.15739509838969767, "compression_ratio": 1.3333333333333333, "no_speech_prob": 0.17098036408424377}, {"id": 357, "seek": 93100, "start": 947.0, "end": 949.0, "text": "然后大家可以在这两个", "tokens": [51164, 26636, 6868, 6723, 3581, 5562, 36257, 7549, 51264], "temperature": 0.0, "avg_logprob": -0.15739509838969767, "compression_ratio": 1.3333333333333333, "no_speech_prob": 0.17098036408424377}, {"id": 358, "seek": 93100, "start": 951.0, "end": 953.0, "text": "可以在京东和档案上", "tokens": [51364, 6723, 3581, 31375, 38409, 12565, 21416, 96, 28899, 5708, 51464], "temperature": 0.0, "avg_logprob": -0.15739509838969767, "compression_ratio": 1.3333333333333333, "no_speech_prob": 0.17098036408424377}, {"id": 359, "seek": 93100, "start": 953.0, "end": 955.0, "text": "购买对应的直述进行学习", "tokens": [51464, 18464, 255, 2930, 108, 8713, 44297, 1546, 16186, 3316, 108, 36700, 8082, 29618, 2930, 254, 51564], "temperature": 0.0, "avg_logprob": -0.15739509838969767, "compression_ratio": 1.3333333333333333, "no_speech_prob": 0.17098036408424377}, {"id": 360, "seek": 93100, "start": 956.0, "end": 958.0, "text": "然后我们念一下slogan", "tokens": [51614, 26636, 15003, 33679, 8861, 82, 4987, 282, 51714], "temperature": 0.0, "avg_logprob": -0.15739509838969767, "compression_ratio": 1.3333333333333333, "no_speech_prob": 0.17098036408424377}, {"id": 361, "seek": 93100, "start": 958.0, "end": 960.0, "text": "就是我们这本书的一个标语", "tokens": [51714, 5620, 15003, 5562, 8802, 2930, 99, 1546, 20182, 162, 3921, 5233, 255, 51814], "temperature": 0.0, "avg_logprob": -0.15739509838969767, "compression_ratio": 1.3333333333333333, "no_speech_prob": 0.17098036408424377}, {"id": 362, "seek": 96000, "start": 960.0, "end": 961.0, "text": "标语叫EAR", "tokens": [50364, 162, 3921, 5233, 255, 19855, 36, 1899, 50414], "temperature": 0.0, "avg_logprob": -0.12077609086648011, "compression_ratio": 1.48068669527897, "no_speech_prob": 0.00036829221062362194}, {"id": 363, "seek": 96000, "start": 961.0, "end": 964.0, "text": "像柴蘑菇一样轻松入门强化学习", "tokens": [50414, 12760, 17238, 112, 31882, 239, 16616, 229, 2257, 14496, 17819, 119, 36897, 14028, 8259, 101, 5702, 118, 23756, 29618, 2930, 254, 50564], "temperature": 0.0, "avg_logprob": -0.12077609086648011, "compression_ratio": 1.48068669527897, "no_speech_prob": 0.00036829221062362194}, {"id": 364, "seek": 96000, "start": 964.0, "end": 965.0, "text": "然后这个标语", "tokens": [50564, 26636, 15368, 162, 3921, 5233, 255, 50614], "temperature": 0.0, "avg_logprob": -0.12077609086648011, "compression_ratio": 1.48068669527897, "no_speech_prob": 0.00036829221062362194}, {"id": 365, "seek": 96000, "start": 965.0, "end": 968.0, "text": "也代表了我们作者的编写这本书的一个目的", "tokens": [50614, 6404, 19105, 17571, 2289, 15003, 11914, 12444, 1546, 38109, 244, 5676, 247, 5562, 8802, 2930, 99, 1546, 20182, 11386, 1546, 50764], "temperature": 0.0, "avg_logprob": -0.12077609086648011, "compression_ratio": 1.48068669527897, "no_speech_prob": 0.00036829221062362194}, {"id": 366, "seek": 96000, "start": 968.0, "end": 970.0, "text": "就想让大家学习强化学习的时候", "tokens": [50764, 3111, 7093, 33650, 6868, 29618, 2930, 254, 5702, 118, 23756, 29618, 2930, 254, 49873, 50864], "temperature": 0.0, "avg_logprob": -0.12077609086648011, "compression_ratio": 1.48068669527897, "no_speech_prob": 0.00036829221062362194}, {"id": 367, "seek": 96000, "start": 970.0, "end": 971.0, "text": "更加轻松", "tokens": [50864, 19002, 9990, 17819, 119, 36897, 50914], "temperature": 0.0, "avg_logprob": -0.12077609086648011, "compression_ratio": 1.48068669527897, "no_speech_prob": 0.00036829221062362194}, {"id": 368, "seek": 96000, "start": 971.0, "end": 974.0, "text": "不要像当时作者学习强化学习那样", "tokens": [50914, 11962, 12760, 16233, 15729, 11914, 12444, 29618, 2930, 254, 5702, 118, 23756, 29618, 2930, 254, 4184, 14496, 51064], "temperature": 0.0, "avg_logprob": -0.12077609086648011, "compression_ratio": 1.48068669527897, "no_speech_prob": 0.00036829221062362194}, {"id": 369, "seek": 96000, "start": 974.0, "end": 975.0, "text": "有很多的困难", "tokens": [51064, 2412, 20778, 1546, 3919, 108, 46531, 51114], "temperature": 0.0, "avg_logprob": -0.12077609086648011, "compression_ratio": 1.48068669527897, "no_speech_prob": 0.00036829221062362194}, {"id": 370, "seek": 96000, "start": 975.0, "end": 979.0, "text": "然后最后祝大家都能够轻松入门强化学习", "tokens": [51114, 26636, 8661, 13547, 12695, 251, 46525, 8225, 1787, 253, 17819, 119, 36897, 14028, 8259, 101, 5702, 118, 23756, 29618, 2930, 254, 51314], "temperature": 0.0, "avg_logprob": -0.12077609086648011, "compression_ratio": 1.48068669527897, "no_speech_prob": 0.00036829221062362194}, {"id": 371, "seek": 96000, "start": 981.0, "end": 983.0, "text": "然后学习强化学习的过程都很顺利", "tokens": [51414, 26636, 29618, 2930, 254, 5702, 118, 23756, 29618, 2930, 254, 1546, 16866, 29649, 7182, 4563, 10178, 118, 23700, 51514], "temperature": 0.0, "avg_logprob": -0.12077609086648011, "compression_ratio": 1.48068669527897, "no_speech_prob": 0.00036829221062362194}], "language": "zh"}