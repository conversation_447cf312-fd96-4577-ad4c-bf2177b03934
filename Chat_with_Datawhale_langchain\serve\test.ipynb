{"cells": [{"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [], "source": ["import requests\n", "\n", "url = \"http://127.0.0.1:8000\"\n", "\n", "data = {\n", "    \"prompt\":\"什么是蘑菇书(easyrl)？\",\n", "    \"api_key\":\"\"\n", "}\n", "\n", "r = requests.post(url, json=data)"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"data": {"text/plain": ["'\"蘑菇书(easyrl)是一个仓库，内容主要包括李宏毅老师强化学习视频内容、经典资料整理、章节习题、算法实战等，旨在帮助读者学习强化学习并探索人工智能领域。谢谢你的提问！\"'"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["r.text"]}], "metadata": {"kernelspec": {"display_name": "gpt", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 2}