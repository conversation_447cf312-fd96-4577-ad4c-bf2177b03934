[![GitHub issues](https://img.shields.io/github/issues/datawhalechina/leedl-tutorial)](https://github.com/datawhalechina/leedl-tutorial/issues) [![GitHub stars](https://img.shields.io/github/stars/datawhalechina/leedl-tutorial)](https://github.com/datawhalechina/leedl-tutorial/stargazers) [![GitHub forks](https://img.shields.io/github/forks/datawhalechina/leedl-tutorial)](https://github.com/datawhalechina/leedl-tutorial/network) [![Hits](https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fgithub.com%2Fdatawhalechina%2Fleedl-tutorial%2F&count_bg=%2379C83D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=hits&edge_flat=false)](https://hits.seeyoufarm.com) <a rel="license" href="http://creativecommons.org/licenses/by-nc-sa/4.0/"><img alt="知识共享许可协议" style="border-width:0" src="https://img.shields.io/badge/license-CC%20BY--NC--SA%204.0-lightgrey" /></a>

# 李宏毅深度学习教程LeeDL-Tutorial

李宏毅老师是台湾大学的教授，其[《机器学习》（2021年春）](https://speech.ee.ntu.edu.tw/~hylee/ml/2021-spring.html)是深度学习领域经典的中文视频之一。李老师幽默风趣的授课风格深受大家喜爱，让晦涩难懂的深度学习理论变得轻松易懂，他会通过很多动漫相关的有趣例子来讲解深度学习理论。李老师的课程内容很全面，覆盖了到深度学习必须掌握的常见理论，能让学生对于深度学习的绝大多数领域都有一定了解，从而可以进一步选择想要深入的方向进行学习，对于想入门深度学习又想看中文讲解的同学是非常推荐的。

本教程主要内容源于[《机器学习》（2021年春）](https://speech.ee.ntu.edu.tw/~hylee/ml/2021-spring.html)，并在其基础上进行了一定的原创。比如，为了尽可能地降低阅读门槛，笔者对这门公开课的精华内容进行选取并优化，对所涉及的公式都给出详细的推导过程，对较难理解的知识点进行了重点讲解和强化，以方便读者较为轻松地入门。此外，为了丰富内容，笔者在教程中选取了[《机器学习》（2017年春）](https://speech.ee.ntu.edu.tw/~hylee/ml/2017-spring.php) 的部分内容，并补充了不少除这门公开课之外的深度学习相关知识。

注：

- 基于《机器学习》（2017年春）的李宏毅机器学习笔记在线阅读地址：https://datawhalechina.github.io/leedl-tutorial/#/

- 基于《机器学习》（2017年春）的李宏毅机器学习笔记源文件：https://github.com/datawhalechina/leedl-tutorial/tree/pre_master

## 最新版PDF下载

地址：https://github.com/datawhalechina/leedl-tutorial/releases

国内地址(推荐国内读者使用)：链接: https://pan.baidu.com/s/1qekZVyCigIbt_Iv5rwcnqQ 提取码: se5s

>ℹ️ **[李宏毅老师推荐](https://twitter.com/HungyiLee2/status/1754042391211004235)：**

<div align=center><img src="https://github.com/datawhalechina/leedl-tutorial/blob/master/assets/prof._lee_twitter.jpg?raw=true" height = "450" alt="李宏毅老师推荐。"></div>

## 内容介绍

* [x] **引言** @王琦
* **深度学习** @王琦
    *  [x] 局部最小值与鞍点
    *  [x] 训练技巧
    *  [x] 自适应学习率
    *  [x] 分类问题损失函数
    *  [x] 归一化
* **卷积神经网络和自注意力机制** @王琦
  *  [x] 卷积神经网络
  *  [x] 自注意力机制
* [x] **循环神经网络** @王琦
* **Transformer** @王琦
  *  [x] Transformer
* **生成模型** @杨毅远
	*  [x] 生成对抗网络基础
	*  [x] 生成对抗网络理论与 Wasserstein 生成对抗网络
	*  [x] 生成对抗网络的评估与有条件的生成对抗网络
	*  [x] 循环生成对抗网络
* **自监督学习** @王琦
  *  [x] 芝麻街的模型
  *  [x] BERT
  *  [x] GPT-3
* [x] **自动编码器概念及其应用** @江季
* [x] 扩散模型@王琦
* **对抗攻击** @杨毅远
    * [x] 对抗攻击基本概念
    * [x] 白盒攻击vs黑盒攻击
    * [x] 被动防守vs主动防守 
* **可解释人工智能** @杨毅远
    * [x] 可解释人工智能概念与案例
    * [x] 可解释人工智能中的局部可解释性
    * [x] 可解释人工智能中的全局可解释性
* **迁移学习** @王琦
  *  [x] 领域自适应
  *  [x] 领域对抗训练
* [x] **深度强化学习** @王琦
* **终身学习** @杨毅远
  *  [x] 灾难性遗忘
  *  [x] 缓解灾难性遗忘 
* **网络压缩** @王琦
  *  [x] 剪枝与彩票假设
  *  [x] 知识蒸馏
* **元学习** @杨毅远
  *  [x] 元学习的概念
  *  [x] 元学习的实例算法
  *  [x] 元学习的应用
* **ChatGPT** @杨毅远
  *  [x] 对于ChatGPT的误解
  *  [x] ChatGPT背后的关键技术——预训练
  *  [x] ChatGPT带来的研究问题

## 配套代码

[点击](https://github.com/datawhalechina/leedl-tutorial/tree/master/Homework)或者网页点击```Homework```文件夹进入配套代码

## 扩展资源
对**强化学习**感兴趣的读者，可阅读[蘑菇书EasyRL](https://github.com/datawhalechina/easy-rl)

## 贡献者

<table border="0">
  <tbody>
    <tr align="center" >
      <td>
         <a href="https://github.com/qiwang067"><img width="70" height="70" src="https://github.com/qiwang067.png?s=40" alt="pic"></a><br>
         <a href="https://github.com/qiwang067">Qi Wang</a> 
        <p> 上海交通大学博士生<br>中国科学院大学硕士</p>
      </td>
      <td>
         <a href="https://yyysjz1997.github.io/"><img width="70" height="70" src="https://github.com/yyysjz1997.png?s=40" alt="pic"></a><br>
         <a href="https://yyysjz1997.github.io/">Yiyuan Yang</a> 
        <p> 牛津大学博士生<br>清华大学硕士</p>
      </td>
      <td>
         <a href="https://github.com/JohnJim0816"><img width="70" height="70" src="https://github.com/JohnJim0816.png?s=40" alt="pic"></a><br>
         <a href="https://github.com/JohnJim0816">John Jim</a>
         <p>北京大学硕士</p>
      </td>
    </tr>
  </tbody>
</table>



## 引用信息

```bibtex
@misc{wang2023leedltutorial,
title = {李宏毅深度学习教程},
year = {2023},
author = {王琦，杨毅远，江季},
url = {https://github.com/datawhalechina/leedl-tutorial}
}
```
```bibtex
@misc{wang2023leedltutorialen,
title = {Deep Learning Tutorial by Hung-yi Lee},
year = {2023},
author = {Qi Wang，Yiyuan Yang，Ji Jiang},
url = {https://github.com/datawhalechina/leedl-tutorial}
}
```

如果您需要转载该教程的内容，请注明出处：[https://github.com/datawhalechina/leedl-tutorial](https://github.com/datawhalechina/leedl-tutorial)。

## 致谢

特别感谢 [@Sm1les](https://github.com/Sm1les)、[@LSGOMYP](https://github.com/LSGOMYP)、[FuWeiru](https://github.com/FuWeiru) 对本项目的帮助与支持。

另外，十分感谢大家对于LeeDL-tutorial的关注。
[![Stargazers repo roster for @datawhalechina/leedl-tutorial](https://reporoster.com/stars/datawhalechina/leedl-tutorial)](https://github.com/datawhalechina/leedl-tutorial/stargazers)
[![Forkers repo roster for @datawhalechina/leedl-tutorial](https://reporoster.com/forks/datawhalechina/leedl-tutorial)](https://github.com/datawhalechina/leedl-tutorial/network/members)

## 关注我们
扫描下方二维码关注公众号：Datawhale，回复关键词“李宏毅深度学习”，即可加入“LeeDL-Tutorial读者交流群”
<div align=center><img src="https://raw.githubusercontent.com/datawhalechina/easy-rl/master/docs/res/qrcode.jpeg" width = "250" height = "270" alt="Datawhale是一个专注AI领域的开源组织，以“for the learner，和学习者一起成长”为愿景，构建对学习者最有价值的开源学习社区。关注我们，一起学习成长。"></div>


## LICENSE
<a rel="license" href="http://creativecommons.org/licenses/by-nc-sa/4.0/"><img alt="知识共享许可协议" style="border-width:0" src="https://img.shields.io/badge/license-CC%20BY--NC--SA%204.0-lightgrey" /></a><br />本作品采用<a rel="license" href="http://creativecommons.org/licenses/by-nc-sa/4.0/">知识共享署名-非商业性使用-相同方式共享 4.0 国际许可协议</a>进行许可。

## Star History
[![Star History](https://api.star-history.com/svg?repos=datawhalechina/leedl-tutorial)](https://star-history.com/#datawhalechina/leedl-tutorial&Date)
