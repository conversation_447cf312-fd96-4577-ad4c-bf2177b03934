# 水很深的深度学习
Unusual Deep learning

在线阅读：https://datawhalechina.github.io/unusual-deep-learning

#### 项目初衷

近些年，以机器学习为代表的人工智能技术逐渐被大家认识并在很多方面得到普及，深度学习技术在学术界和工业界取得了广泛的成功，受到高度重视，并掀起新一轮的人工智能热潮。我们希望通过组队学习的方式，让大家更清楚的了解深度学习的理论。

本课程讲授和讨论深度学习的主要理论和关键技术，主要内容有深度学习基础、卷积神经网络、循环神经网络、深度生成模型、深度学习正则化等以及上述深度学习理论在图像、语音、自然语言处理等领域的主要应用，同时也介绍了一些新兴的深度学习模型及其应用。

通过本课程的学习，希望能够掌握深度学习的基本理论和关键技术，提高基于深度学习技术进行科学研究与应用开发的能力。

学习的先修要求：了解一些基本的机器学习算法和高数知识，不要求编程，只讲述理论。

#### 内容设置

课程内容计划分成两个阶段：

深度学习理论入门：主要介绍深度学习的基本背景和数学基础，主要介绍CNN和RNN的相关知识。

深度学习理论进阶：在第一部分的基础上延伸对神经网络的理解，并介绍一些常见的优化算法和正则化方法，以及现在很流行的注意力机制，以及深度生成模型。

案例：主要介绍深度学习的主要平台，和深度学习在CV、Speech和NLP方面的应用。

------



1. 绪论与深度学习概述（视频地址：https://www.bilibili.com/video/BV1iq4y197L4?p=1）
2. 数学基础（视频地址：https://www.bilibili.com/video/BV1iq4y197L4?p=1）
3. 机器学习基础
4. 前馈神经网络 （视频地址：https://www.bilibili.com/video/BV1iq4y197L4?p=2）
5. 卷积神经网络CNN （视频地址：https://www.bilibili.com/video/BV1iq4y197L4?p=3）
6. 循环神经网络RNN（视频地址：https://www.bilibili.com/video/BV1iq4y197L4?p=4）
7. 网络优化（视频地址：https://www.bilibili.com/video/BV1iq4y197L4?p=5）
8. 正则化方法（视频地址：https://www.bilibili.com/video/BV1iq4y197L4?p=6）
9. 注意力机制与外部记忆
10. GAN
11. 深度生成模型
12. 深度生成模型概述
13. 深度学习常见的平台




1. CV图像
   1. 图像/视频处理
   2. 图像/视频压缩
   3. 图像分类
   4. 目标检测
   5. 图像分割
   6. 图像回归
2. Speech语音
   1. 语音数据集
   2. 语音识别
   3. 声纹识别
   4. 语音合成
3. NLP自然语言处理
   1. 语言模型
   2. 机器翻译
   3. 机器阅读理解
   4. 自动摘要
   5. 图像描述
4. 中英文对照手册



专题：

- 神经网络的逼近理论
- 算法正则化
- 算法正则化导致的归纳偏差
- 非凸优化的前沿
- 神经正切核
- 多层卷积稀疏编码
- 信息瓶颈及其他
- 深度神经网络的脆弱性
- 神经（常）微分方程
- 神经网络学习Wasserstein空间的测地线
- 生成模型



#### 人员安排

| 成员 | 个人简介                                      | 个人主页                                                     |
| ---- | --------------------------------------------- | ------------------------------------------------------------ |
| 刘洋 | DataWhale成员，中国科学院数学与系统科学研究院 | [知乎主页](https://www.zhihu.com/people/ming-ren-19-34)<br />公众号：鸣也的小屋<br />[个人主页](https://liu-yang-maker.github.io/Liu.Y/) |

#### 其他

## 关注我们
<div align=center>
<p>扫描下方二维码关注公众号：Datawhale</p>
<img src="https://raw.githubusercontent.com/datawhalechina/pumpkin-book/master/res/qrcode.jpeg" width = "180" height = "180">
</div>

## LICENSE
<a rel="license" href="http://creativecommons.org/licenses/by-nc-sa/4.0/"><img alt="知识共享许可协议" style="border-width:0" src="https://img.shields.io/badge/license-CC%20BY--NC--SA%204.0-lightgrey" /></a><br />本作品采用<a rel="license" href="http://creativecommons.org/licenses/by-nc-sa/4.0/">知识共享署名-非商业性使用-相同方式共享 4.0 国际许可协议</a>进行许可。









