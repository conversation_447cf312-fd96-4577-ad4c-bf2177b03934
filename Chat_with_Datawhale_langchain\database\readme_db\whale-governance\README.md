# whale-governance

组织治理小组负责推动组织持续发展，包括组织架构、文化、制度等工作

## Datawhale 组织架构

![pic](./pics/datawhale.png)

- 组织治理
  - 概要：负责推动组织持续发展，包括组织架构、文化、制度等工作
  - 负责人：@苏鹏
  - 工作内容：
    - 优化社区组织架构
    - 整理社区黑板报
    - 收集问题并持续推动各小组进行流程优化
- 产品技术
  - 概要：负责通过产品和技术满足学习者/社区发展的需求
  - Datawhale 网站负责人：@谢文昕
  - Datawhale 小程序负责人：@光城
- 社区建设
  - 概要：负责推动社区持续发展，帮助 Datawhale 成员以及学习者成长，提升社区影响力
  - Datawhale 开源学习联盟负责人：@范晶晶
  - Datawhale 社群负责人：@李碧涵
- 开源项目
  - 概要：负责推动开源教程与开源软件等
  - 负责人：@谢文睿
  - 工作内容：
    - 推动建设开源教程
    - 推动打造开源软件
- 开源学习
  - 概要：负责推动组队学习
  - 负责人：@马燕鹏
  - 工作内容：
    - 推动协调 Datawhale 组队学习
    - 完善组队学习内容，发掘社区人才
- 交流分享
  - 内容：负责 Datawhale 社区经验分享、读书分享、论文分享、干货分享等
  - 读书分享负责人：@蓝昔
  - 论文分享负责人：@芙蕖
  - 技术分享负责人：@司玉鑫
- 活动志愿
  - 内容：负责推动有助于组织或社区成员发展的活动
  - 负责人：暂无
  - 工作内容
    - 组织各城市线下聚会，可以是运动，沙龙，聚餐等等形式，帮助 Datawhale 社区成员构建更为紧密的连接
    - Datawhale 年会
