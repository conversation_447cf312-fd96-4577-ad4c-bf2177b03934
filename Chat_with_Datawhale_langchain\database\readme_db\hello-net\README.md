# hello-net

<img src="https://github.com/datawhalechina/hello-net/blob/main/pics/%E8%AE%A1%E7%AE%97%E6%9C%BA%E7%BD%91%E7%BB%9C%E5%B0%81%E9%9D%A2.jpeg" width=200px>

### 项目简介

《计算机网络(第六版)》是清华大学出版社于2022年出版的书籍。该书系统地介绍了计算机网络的基本原理，与时俱进地介绍了当下计算机网络的先进技术。同时作者生动而幽默的语言令本书富有趣味性和故事性，让读者在轻松流畅的阅读体验中学到知识。
此次开源项目旨在辅助学习者在阅读此书时能够更高效地进行**回顾总结**与**习题巩固**。拟开设两个板块，内容分别如下：
1. 以章内小节为单位进行知识要点整合与写作逻辑提炼
2. 课后习题解答

### 立项理由

1. 作者在每一章末进行了本章总结，鼓励读者总结梳理该章节的知识。但是章节篇幅过长、读者碎片时间阅读等因素，可能在客观上会给读者带来总结归纳上的困难，不利于读者进行复盘。因此本项目的第一版块拟定以章内小节为单位，进行逐小节的知识要点整合和写作逻辑提炼（即文章的结构，例如案例叙述的顺序、小节主要分为哪几部分），以**知识要点填空**和**小节结构填空**的形式，引导读者在进行本小节知识回顾的同时，把握作者写作的逻辑，辅助读者及时地归纳与复盘。
2.  本书每章末附有丰富的课后习题，为了提高习题利用率，本项目的第二板块拟对课后习题进行解析与思路指引，使读者在做习题时有所参考，帮助他们更好地巩固知识。

### 项目受众

1. 计算机相关专业的学生
2. 喜欢阅读国外经典教材的读者
3. 对计算机网络的过去、当下与未来感兴趣的读者

### 项目亮点

1. 本项目帮助读者进行及时的归纳和复盘，采用**填空**的形式，有针对性地让读者把握文章的重点知识与逻辑结构。希望通过这次尝试，探索一个高效阅读“计算机原理”书籍的新方式。
2. 本项目帮助读者进一步巩固知识，提供习题解答。同时市面上尚没有该书的习题解析。

### 项目规划

目录基于《计算机网络(第六版)》原书。
项目的初步规划是在10月24日前，完成本书1、3章的回顾总结和习题解答板块。
目前项目成员有四位，分别负责一、三章。两章内容同步进行，最后统一进行汇总润色。
可预见的困难主要是习题解答环节，须确保习题的解析或者思路是正确的。

### 项目负责人

项目负责人：梁恩瑞

Github主页链接：https://github.com/BlueLumen

微信：liangen18663307291

|项目贡献者|贡献内容|联系方式|
|----|----|----|
|梁恩瑞|第3章内容梳理，第3章习题1-20题解答|<EMAIL>|
|张子杰|第1章1.1-1.4节内容梳理，第1章习题1-10题解答|<EMAIL>|
|卢水琼|第1章1.5-1.11节内容梳理，第1章习题11-20题解答|<EMAIL>|
|邱雯|第1章21-50题解答，第3章21-52题解答|<EMAIL>|
