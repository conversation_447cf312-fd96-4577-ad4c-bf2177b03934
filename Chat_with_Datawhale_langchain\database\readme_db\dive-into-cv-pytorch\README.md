
<div align=center>
<img src="https://raw.githubusercontent.com/datawhalechina/dive-into-cv-pytorch/master/markdown_imgs/whale_pytorch.jpg" width="250">
</div>

# Dive-into-CV-PyTorch

本项目《动手学CV-Pytorch版》是Datawhale:whale:CV小组的一个重点项目，我们旨在构建一个以Pytorch为框架，强调动手实战的计算机视觉教程项目，帮助大家更好的学习。

项目大体上会按照计算机视觉的不同领域和应用场景进行章节划分，循序渐进，带领大家走进CV的世界，以动手实战的方式，从入门到进阶。

目前已经完成的内容包括：`预备知识`、`图像分类入门`、`目标检测入门`、`生成式对抗网络入门`、`CV中的Transformer`四个章节，后续的章节敬请期待。相信随着项目的推进，你我将互相见证彼此的进步。

## 食用方法

:whale: 推荐使用 [**动手学CV-PyTorch 在线阅读**](https://datawhalechina.github.io/dive-into-cv-pytorch) 进行学习

有配套代码的章节，可以在项目`code`目录的相应章节目录下面找到。

以下是目录与代码更新进度

## 动手学CV-Pytorch 目录

* 第一章: 预备知识
    - [x] [1.1 深度学习环境配置](https://datawhalechina.github.io/dive-into-cv-pytorch/#/chapter01_preliminary_knowledge/1.1_environment_install/README.md)
    - [x] [1.2 Pytorch基础使用介绍](https://datawhalechina.github.io/dive-into-cv-pytorch/#/chapter01_preliminary_knowledge/1.2_pytorch_basic_usage_introduction/README.md)
    - [x] [1.3 自动求梯度](https://datawhalechina.github.io/dive-into-cv-pytorch/#/chapter01_preliminary_knowledge/1.3_automatic_gradient/README.md)
    - [x] [1.4 线性回归-小试身手](https://datawhalechina.github.io/dive-into-cv-pytorch/#/chapter01_preliminary_knowledge/1.4_linear_regression_pytorch/README.md)
    - [x] [1.5 初识图像分类：MNIST分类实战](https://datawhalechina.github.io/dive-into-cv-pytorch/#/chapter01_preliminary_knowledge/1.5_mnist_classification/README.md)

* 第二章: 图像分类入门
    - [x] [2.1 数据读取与数据扩增](https://datawhalechina.github.io/dive-into-cv-pytorch/#/chapter02_image_classification_introduction/2.1_dataloader_and_augmentation/README.md)
    - [x] [2.2 图像分类介绍](https://datawhalechina.github.io/dive-into-cv-pytorch/#/chapter02_image_classification_introduction/2.2_introduction_of_image_classification/README.md)
    - [x] [2.3 模型训练与验证](https://datawhalechina.github.io/dive-into-cv-pytorch/#/chapter02_image_classification_introduction/2.3_model_training_and_verification/README.md)
    - [x] 2.4 天池CV入门赛实战
        - [x] [街景字符识别baseline讲解](https://datawhalechina.github.io/dive-into-cv-pytorch/#/chapter02_image_classification_introduction/2.4_classification_action_SVHN/baseline.md)
        - [x] [比赛方案与优化技巧总结](https://datawhalechina.github.io/dive-into-cv-pytorch/#/chapter02_image_classification_introduction/2.4_classification_action_SVHN/ideas_and_tricks_summary.md)
    - [x] [附录A torchvision.transforms速查表](chapter02_image_classification_introduction/appendix/appendixA_data_augment.md)

* 第三章: 目标检测入门
    - [x] [3.1 目标检测基本概念](https://datawhalechina.github.io/dive-into-cv-pytorch/#/chapter03_object_detection_introduction/3_1.md)
    - [x] [3.2 目标检测数据集VOC](https://datawhalechina.github.io/dive-into-cv-pytorch/#/chapter03_object_detection_introduction/3_2.md)
    - [x] [3.3 锚框](https://datawhalechina.github.io/dive-into-cv-pytorch/#/chapter03_object_detection_introduction/3_3.md)
    - [x] [3.4 模型结构](https://datawhalechina.github.io/dive-into-cv-pytorch/#/chapter03_object_detection_introduction/3_4.md)
    - [x] [3.5 损失函数](https://datawhalechina.github.io/dive-into-cv-pytorch/#/chapter03_object_detection_introduction/3_5.md)
    - [x] [3.6 训练与测试](https://datawhalechina.github.io/dive-into-cv-pytorch/#/chapter03_object_detection_introduction/3_6.md)

* 第四章: 图像分割入门
    - 推进中...

* 第五章: 生成式对抗网络入门
    - [x] [5.1 初识生成对抗网络](https://datawhalechina.github.io/dive-into-cv-pytorch/#/chapter05_gan/5_1.md)
    - [x] [5.2 GAN实战: 手写数字生成](https://datawhalechina.github.io/dive-into-cv-pytorch/#/chapter05_gan/5_2.md)
    - [x] [5.3 ConditionGAN实战: 再战手写数字生成](https://datawhalechina.github.io/dive-into-cv-pytorch/#/chapter05_gan/5_3.md)
    - [x] [5.4 DCGAN实战](https://datawhalechina.github.io/dive-into-cv-pytorch/#/chapter05_gan/5_4.md)

* 第六章: CV中的Transformer
    - [X] [6.1 你好，Transformer](https://datawhalechina.github.io/dive-into-cv-pytorch/#/chapter06_transformer/6_1_hello_transformer.md)
    - [X] [6.2 Transformer实战：OCR字符识别](https://datawhalechina.github.io/dive-into-cv-pytorch/#/chapter06_transformer/6_2_ocr_by_transformer.md)


## 常见问题

- **在线教程页面无法打开**: 

    测试中存在部分人打不开在线教程的情况。

    部分小伙伴反馈尝试切换浏览器后可以正常打开了，如果仍然不行，最有效的解决办法是科学上网。

- **无法加载图片的解决办法**: 

    根本解决办法还是科学上网，也可以尝试修改host文件看下是否能解决。

    [windows解决方案：修改host文件](https://blog.csdn.net/u011583927/article/details/104384169)

- **公式无法正常显示解决办法**：

    github中的markdown不支持LATEX公式，如果你喜欢在本项目中直接浏览教程，可以安装chrome的`MathJax Plugin for Github`插件让大部分公式正常显示。但是包含公式的章节强力建议使用 [动手学CV-PyTorch 在线阅读](https://datawhalechina.github.io/dive-into-cv-pytorch) 进行学习


## 致谢

感谢以下Datawhale成员对项目推进作出的贡献(排名不分先后)：

<table align="center" style="width:80%;">
  <caption><b>贡献者名单</b></caption>
<thead>
  <tr>
    <th>成员</th>
    <th>个人简介及贡献</th>
    <th>个人主页</th>
  </tr>
</thead>
<tbody>
  <tr>
    <td><span style="font-weight:normal;font-style:normal;text-decoration:none">安晟</span></td>
    <td><span style="font-weight:normal;font-style:normal;text-decoration:none">图像算法工程师，项目负责人 </td>
    <td><a href="https://blog.csdn.net/u011583927">CSDN</a>,   <a href="https://github.com/monkeyDemon">Github</a></td>
  </tr>
  <tr>
    <td><span style="font-weight:normal;font-style:normal;text-decoration:none">闫永强</span></td>
    <td><span style="font-weight:normal;font-style:normal;text-decoration:none">图像算法工程师，3.4、3.5、3.6、6.1节作者</td>
    <td> </td>
  </tr>
  <tr>
    <td><span style="font-weight:normal;font-style:normal;text-decoration:none">宋志龙</span></td>
    <td><span style="font-weight:normal;font-style:normal;text-decoration:none">图像算法工程师，3.1、3.2、3.3节作者</td>
    <td> </td>
  </tr>
  <tr>
    <td><span style="font-weight:normal;font-style:normal;text-decoration:none">沈豪</span></td>
    <td><span style="font-weight:normal;font-style:normal;text-decoration:none">复旦大学网安博士在读，第五章作者</td>
    <td><a href="https://www.zhihu.com/people/shenhao-63">知乎</a>,   <a href="https://github.com/shenhao-stu">Github</a></td>
  </tr>
  <tr>
    <td><span style="font-weight:normal;font-style:normal;text-decoration:none">袁明坤</span></td>
    <td><span style="font-weight:normal;font-style:normal;text-decoration:none">西安电子科技大学硕士，2.1、6.2节作者</td>
    <td><a href="https://blog.csdn.net/my_kun?spm=1011.2124.3001.5113">CSDN</a></td>
  </tr>
  <tr>
    <td><span style="font-weight:normal;font-style:normal;text-decoration:none">王程伟</span></td>
    <td><span style="font-weight:normal;font-style:normal;text-decoration:none">南昌航空大学研究生，1.5、2.1节作者</td>
    <td><a href="https://blog.csdn.net/weixin_40647819">CSDN</a></td>
  </tr>
  <tr>
    <td><span style="font-weight:normal;font-style:normal;text-decoration:none">张强</span></td>
    <td><span style="font-weight:normal;font-style:normal;text-decoration:none">宁波大学计算机硕士，2.2节作者</td>
    <td><a href="https://github.com/QiangZiBro">Github</a></td>
  </tr>
  <tr>
    <td><span style="font-weight:normal;font-style:normal;text-decoration:none">游璐颖</span></td>
    <td><span style="font-weight:normal;font-style:normal;text-decoration:none">福州大学计算机硕士，1.2节作者</td>
    <td><a href="yluy.gitee.io">个人主页</a></td>
  </tr>
  <tr>
    <td><span style="font-weight:normal;font-style:normal;text-decoration:none">李一飞</span></td>
    <td><span style="font-weight:normal;font-style:normal;text-decoration:none">参与1.3、1.4节优化</td>
  </tr>
  <tr>
    <td><span style="font-weight:normal;font-style:normal;text-decoration:none">樊亮</span></td>
    <td><span style="font-weight:normal;font-style:normal;text-decoration:none">1.3节作者</span></td>
    <td><a href="https://blog.csdn.net/OuDiShenmiss">CSDN</a></td>
  </tr>
  <tr>
    <td><span style="font-weight:normal;font-style:normal;text-decoration:none">杨皓博</span></td>
    <td><span style="font-weight:normal;font-style:normal;text-decoration:none">1.4节作者</span></td>
    <td><a href="https://github.com/Dulpyanghaobo">Github</a></td>
  </tr>
  <tr>
    <td><span style="font-weight:normal;font-style:normal;text-decoration:none">伊雪</span></td>
    <td><span style="font-weight:normal;font-style:normal;text-decoration:none">中电科网安研究院开发工程师，2.3节作者</span></td>
    <td> </td>
  </tr>
  <tr>
    <td>阿水</td>
    <td><span style="font-weight:normal;font-style:normal;text-decoration:none">Datawhale成员，公众号：Coggle数据科学, 为第二章提供部分素材 </td>
    <td><a href="https://www.zhihu.com/people/finlayliu">知乎</a></td>
  </tr>
</tbody>
</table> 


## 关注我们

> "Datawhale是一个专注AI领域的开源组织，以“for the learner，和学习者一起成长”为愿景，构建对学习者最有价值的开源学习社区。关注我们，一起学习成长。"

<img src="https://raw.githubusercontent.com/datawhalechina/dive-into-cv-pytorch/master/markdown_imgs/datawhale_qrcode.jpeg" width="175" height= "200">
