# OpenMMLab 系列新手入门教程（从 MMDetection 库切入）

为了让大家更好的接触和入门 OpenMMLab 这一系列优秀的深度学习学术研究和工业应用开源算法库，
秉着用爱发电和传播 AI 知识的初心下制作一系列教程；帮助新手快速入门、快速使用、习惯官方文档且能够自主上手实验并自由选择阅读更深层的知识。  
让每一位初心者在学习后都能快速学会应用、平滑学习曲线、无缝衔接 OpenMMLab 的其他相关教程，社区开放麦，从整体上减少畏难情绪。

本项目将会分成多期迭代阶段，直到能形成一套流水线方案 （从训练、优化、再到部署），完美涵盖大部分对应下游库的文件与工具；目标是：完美覆盖 jupyter-notebook，关键项都对应手册与拓展材料和衔接官方资料。

现有迭代阶段：

A 阶段：以 openmmlab-mmdetection 为例介绍系列库的基础用法，让学习者可以快速掌握使用方法、习惯文档并能够自主快速熟悉其他下游库。    
B 阶段：针对 mmdeploy 对 mmdetection 进行结合，展开介绍有关部署的入门知识。

## A阶段内容
对于A阶段，目前其中的第一阶段已完成，开始第二阶段迭代制作；
第二阶段迭代将会覆盖进阶的模型修改和对齐自定义等内容。
时间：预计在2~3月份完成第二阶段的制作
```text
第一阶段内容：
    1.0 配置安装
    1.1 数据集准备和制作
    1.2 训练过程
    1.3 结果展示
    1.4 第一阶段补充
```

完成第一阶段后已可以看懂关键的config用法与基本的项目跑通与结果测试；其他下游库利用类似方法可进行环境配置与各config算法的调通。

## 💡教程视频观看地址
[完整视频合集](https://space.bilibili.com/431850986/channel/collectiondetail?sid=743765)

## 💡仓库文件介绍【请先阅读！】

- notebook 

有关教程资料文件都放在这

- tools

有关教程用到的一些有用的工具都放在这

- learning-material.md

分享有关 OpenMMLab 官方与非官方的相关资料合集，包括官方网站、开源库合集、官方视频资料、公众号、非官方生态链等等。

- download_openmmlab.sh

下载工具，方便选择想要下载的openmmlab下游库（感谢`三木君`对本文件mmdeploy部分的修复）

## 贡献
|贡献者|职责|简介|
|  ----  | ----  |  ----  |
|散步（卢雨畋）|教程出品与视频录制|[github](https://github.com/sanbuphy)|


如果你想对项目部分做出修正与补充，欢迎提交 pr，有好的想法也欢迎联系我，我的邮箱是 <EMAIL>


## 反馈
如果你发现任何问题，请提交 [Issue](https://github.com/datawhalechina/openmmlab-tutorial/issues).

## 致谢

感谢 [OpenMMLab](https://openmmlab.com/) 官方对教程的支持，感谢参与测试的每一位小伙伴，是你们让教程变得更好。

## 关注我们

扫描下方二维码关注公众号：Datawhale

![](https://raw.githubusercontent.com/datawhalechina/pumpkin-book/master/res/qrcode.jpeg)
