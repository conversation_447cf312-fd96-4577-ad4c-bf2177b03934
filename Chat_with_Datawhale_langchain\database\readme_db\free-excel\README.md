# Free-Excel

free excel中文意为**自由Excel**，源于希望学习者能够通过该教程能够更加自由地使用Excel进行数据分析、数据统计，解放Excel不再为Excel所困。内容上会更加偏实践应用，力求尽可能简单而又具有一定深度。其他很多教程都是按功能模块来讲解，而且还详细的说明每个功能，令人枯燥乏味。Free Excel根据多年工作经验总结而成，很多内容是常用且重要的，同时学完后能加深对Excel的理解。授之以鱼不如授之以渔，让我们开始愉快的Excel之旅吧！

本课程提供了**网页**与**Markdown** 2种不同的版本来展现课程内容

## 文件内容

- data    :项目数据集
- docs    :网页版课程
- Markdown:MD版课程
- 历史版本:V2.0课程


## 环境

本教程所使用Excel版本为2016或者2019，未提供下载安装，请自行装好环境以便操作。如果电脑没有Excel，可以下载WPS进行相关的操作学习。

WPS下载链接：https://www.wps.cn/product

注：本课程中部分函数为Excel新更新的内容，如果想体验Excel新添加的函数，可将Excel更新至最新版，或将WPS更新至11.1版及以上


## 更新版本
|    版本     |    日期    |       更新内容       |
| :---------: | :--------: | :------------------: |
| v1.0.0 beta | 2021-08-08 | 添加章节内容及数据集 |
| v2.0.0      | 2022-09-09 | 全新的Excel内容与教程 |
| v2.1.0      | 2022-12-17 | 全新的Excel内容增加了网页版 |



## 贡献

|       贡献者       |     版本      |             主页              |          联系          |
| :----------------: | :-----------:| :--------------------------: | :--------------------: |
| Jan Yang（简杨君） | v1.0.0 beta | https://github.com/yangjiada | <EMAIL> |
| 牧小熊（聂雄伟）   | v2.0.0       |https://github.com/muxiaoxiong| <EMAIL>|


## 关注我们

> "Datawhale是一个专注AI领域的开源组织，以“for the learner，和学习者一起成长”为愿景，构建对学习者最有价值的开源学习社区。关注我们，一起学习成长。"

[![img](Markdown/src/datawhale.jpg)](https://github.com/datawhalechina/team-learning-sql/blob/main/img/datawhale_code.jpeg)
