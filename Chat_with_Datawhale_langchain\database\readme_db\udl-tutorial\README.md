# 手推《Understanding Deep Learning》 udl-tutorial

视频讲解深度学习最新神书《Understanding Deep Learning》，附代码实践。

- 书籍下载地址： <https://udlbook.github.io/udlbook/>

## 视频讲解目录

- 视频讲解合集地址： [手推《Understanding Deep Learning》 理论与代码实践](https://space.bilibili.com/284105758/channel/collectiondetail?sid=2132178)

| 目录 |
| :---- |
| [0.1 课程介绍](https://www.bilibili.com/video/BV1bb4y1A7nm/?spm_id_from=333.999.0.0&vd_source=072bec8d7760873ec94ccecfe1095d81) |
| [1.1 介绍-监督学习](https://www.bilibili.com/video/BV1bK4y117h1/?spm_id_from=333.788&vd_source=072bec8d7760873ec94ccecfe1095d81) |
| [1.2 介绍-无监督学习](https://www.bilibili.com/video/BV1Jp4y1m7T3/?spm_id_from=333.788&vd_source=072bec8d7760873ec94ccecfe1095d81) |
| [1.3 介绍-强化学习与本书结构](https://www.bilibili.com/video/BV1Pg4y1e7yW/?spm_id_from=333.788&vd_source=072bec8d7760873ec94ccecfe1095d81) |
| [2 监督学习](https://www.bilibili.com/video/BV19T4y147wY/?spm_id_from=333.788&vd_source=072bec8d7760873ec94ccecfe1095d81) |
| [3.1 浅层神经网络-示例引入](https://www.bilibili.com/video/BV1Gw411L7X4/?spm_id_from=333.788&vd_source=072bec8d7760873ec94ccecfe1095d81) |
| [3.2 浅层神经网络-万能逼近定理](https://www.bilibili.com/video/BV1XA4m1L7qE/?spm_id_from=333.788&vd_source=072bec8d7760873ec94ccecfe1095d81) |
| [3.3 浅层神经网络-多元输入输出](https://www.bilibili.com/video/BV1uF4m137EA/?spm_id_from=333.788&vd_source=072bec8d7760873ec94ccecfe1095d81) |
| [3.4 浅层神经网络-一般形式](https://www.bilibili.com/video/BV1AJ4m1b7G3/?spm_id_from=333.788&vd_source=072bec8d7760873ec94ccecfe1095d81) |
| [3.5 浅层神经网络-总结](https://www.bilibili.com/video/BV1fm411X7As/?spm_id_from=333.788&vd_source=072bec8d7760873ec94ccecfe1095d81) |
| [4.1 深度神经网络-神经网络的组合](https://www.bilibili.com/video/BV1ZA4m1L7hB/?spm_id_from=333.788&vd_source=072bec8d7760873ec94ccecfe1095d81) |
| [4.2 深度神经网络-从组合网络到深度网络](https://www.bilibili.com/video/BV1Tx4y1C7GE/?spm_id_from=333.788&vd_source=072bec8d7760873ec94ccecfe1095d81) |
| [4.3 深度神经网络-深度神经网络](https://www.bilibili.com/video/BV15r421s7uT/?spm_id_from=333.788&vd_source=072bec8d7760873ec94ccecfe1095d81) |
| [4.4 深度神经网络-矩阵表示](https://www.bilibili.com/video/BV1uZ42127yT/?spm_id_from=333.788&vd_source=072bec8d7760873ec94ccecfe1095d81) |
| [4.5 深度神经网络-浅层vs深层神经网络](https://www.bilibili.com/video/BV1QJ4m1e7t2/?spm_id_from=333.788&vd_source=072bec8d7760873ec94ccecfe1095d81) |

## 公告

*视频持续更新中！*



## 贡献者名单

| 姓名 | 职责 | BiliBili |
| :----| :---- | :---- |
| 李立康 | 项目负责人 | [K_Bayesian](https://space.bilibili.com/284105758?spm_id_from=333.788.0.0) |



## 关注我们

<div align=center>
<p>扫描下方二维码关注公众号：Datawhale</p>
<img src="https://raw.githubusercontent.com/datawhalechina/pumpkin-book/master/res/qrcode.jpeg" width = "180" height = "180">
</div>

## LICENSE

<a rel="license" href="http://creativecommons.org/licenses/by-nc-sa/4.0/"><img alt="知识共享许可协议" style="border-width:0" src="https://img.shields.io/badge/license-CC%20BY--NC--SA%204.0-lightgrey" /></a><br />本作品采用<a rel="license" href="http://creativecommons.org/licenses/by-nc-sa/4.0/">知识共享署名-非商业性使用-相同方式共享 4.0 国际许可协议</a>进行许可。

*注：默认使用CC 4.0协议，也可根据自身项目情况选用其他协议*
