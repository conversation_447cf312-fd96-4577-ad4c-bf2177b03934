start	end	text
1000	3000	B站的小伙伴们好
3000	7000	我是蘑菇书一语二语二强化学习教程的作者之一王奇
7000	11000	今天来有给大家带来一个强化学习的入门指南
13000	18000	本次入门指南基于蘑菇书一语二语二强化学习教程
18000	20000	本书的作者目前都是Dell会员成员
20000	22000	也都是数学在读
22000	24000	下面去介绍每个作者
26000	27000	我是王奇
27000	29000	目前就留于中国科研院大学
29000	33000	引用方向是深度学习、静态视觉以及数据挖掘
36000	38000	杨玉云目前就读于清华大学
38000	39000	他的引用方向为
39000	43000	时空数据挖掘、智能冲砍系统以及深度学习
44000	46000	张记目前就读于北京大学
46000	49000	他的引用方向为强化学习记忆人
52000	54000	接下来开始正式的分享
54000	55000	本次分享分为三部分
55000	56000	第一部分
56000	57000	为什么要学强化学习
57000	58000	第二部分
58000	60000	为什么要用本书来学
60000	61000	第三部分
61000	62000	这本书怎么学最高效
64000	66000	首先讲一下为什么要学强化学习
69000	71000	我们先聊一下强化学习的基本概念
71000	75000	强化学习用来学习如何做出一系列好的决策
75000	77000	而人工智能的基本挑战是
77000	80000	学习在不确定的情况下做出好的决策
80000	81000	这边我举个例子
81000	83000	比如你想让一个小孩学会走路
83000	86000	他就需要通过不断尝试来发现
86000	87000	怎么走比较好
87000	88000	怎么走比较快
91000	94000	强化学习的交互过程可以通过这张图来表示
94000	97000	强化学习由智能体和环境两部分组成
97000	98000	在强化学习过程中
98000	100000	智能体与环境一直在交互
101000	104000	智能体在环境中获取某个状态后
104000	106000	它会利用刚刚的状态输出一个动作
106000	108000	这个动作也被称为决策
108000	112000	然后这个动作会被环境中执行
112000	115000	环境会根据智能体采取的动作
115000	117000	来输出下一个状态
117000	120000	以及当前这个动作带来的奖励
120000	121000	整体它的目的呢
121000	124000	就是尽可能的多的
125000	128000	尽可能多的在环境中获取奖励
135000	136000	强化学习的应用非常广泛
136000	139000	比如说我们可以使用强化学习来玩游戏
139000	141000	玩游戏的话可以玩这种电子游戏
141000	143000	也可以玩这种围棋游戏
144000	146000	围棋游戏中比较出名的一个
146000	149000	强化学习的算法就是AlphaGo
151000	152000	此外我们可以使用强化学习
152000	153000	来控制机器人
153000	155000	以及来实现助力交通
155000	157000	另外还可以使用强化学习
157000	159000	来更好地给我们做推进
161000	162000	接下来就到第二部分
162000	166000	也就是为什么要使用本书来学习强化学习
166000	167000	这部分其实也是讲
167000	170000	这个蘑菇书它出版的一些故事
171000	173000	当时我在学习强化学习的时候
173000	175000	搜集了一些资料
175000	178000	然后我发现这些资料
178000	179000	都有点灰色难懂
179000	182000	并不是那么容易地上手
183000	185000	于是我开始到网上
186000	188000	搜索一些公开课来学习
188000	190000	首先我搜索到的是
190000	192000	李宏毅老师的一些公开课
192000	195000	很多人就是入门深度学习
195000	196000	和基学低门课
196000	198000	其实就是李宏毅老师的课
198000	200000	李宏毅老师的课
202000	205000	李宏毅老师的基础学习和深度学习公开课
205000	207000	在编上有很高的播放量
208000	211000	于是我搜索了李宏毅老师的强化学习课
211000	213000	这门课叫顺强化学习
213000	215000	这门课跟以往的李宏毅老师的
215000	217000	课人的风格都是一样的
217000	219000	就是非常的生动有趣
219000	221000	李宏毅老师经常会用
221000	224000	玩亚达利游戏的例子来讲强化学习
224000	226000	这样强化学你就变得通透易懂
226000	228000	然后就很多人都会
228000	231000	把这门课作为自己的强化学习入门课
231000	234000	这边我们念出了一些
234000	237000	就是观众的一些好评
237000	240000	比如说这边观众说
240000	242000	国内这个讲得最好了
242000	244000	然后李宏毅老师讲得真厉害
244000	247000	还有这个评论比较搞笑
247000	249000	这个视频每天晚上都有
249000	251000	几个到十几个人在看
251000	253000	因此我们可以发现
253000	259000	这门课还是非常受人欢迎的
262000	264000	后来我发现李宏毅老师的课
264000	265000	不是那么全面
265000	267000	他的课主要集中于讲解
267000	268000	迅速强化学算法
268000	270000	而忽略了全球强化学算法
270000	273000	以及一些比较前沿的强化学算法
273000	274000	因此我开始
274000	276000	到网上搜索其他的强化学公开课
276000	278000	当时是搜索到了
278000	280000	周博恩老师的强化学纲要
280000	282000	周博恩老师是
282000	285000	人文智能底层的一个顶尖学者
285000	288000	他在人文智能底层会议
288000	290000	亲爱发表了五十余篇学术论文
290000	293000	然后论文的作用量超过一万次
293000	297000	周博恩老师的这门强化学纲要课
297000	298000	就是理论严谨
298000	299000	然后内容丰富
299000	301000	全面的教训介绍了强化学领域
301000	303000	并有相关代码实践
308000	309000	在学习完
309000	314000	女红衣和周博恩老师两门强化学课以后
314000	316000	我发现还是有一点不足
316000	318000	就是代码的实践还偏少
318000	322000	于是我在网上搜索其他的强化学课
322000	324000	当时就搜索了李克强老师的一个
324000	328000	叫试验冠军代理丛林实践强化学习这门课
328000	330000	这门课有个特别突出的优点
330000	332000	就是实战性强
332000	335000	突兵课只能会使用单单的代码来讲解强化学习
335000	337000	这边也有很多好评
340000	344000	有了这三门课以后
344000	347000	我发现这三门课
347000	350000	通过这些公开课来进学的话
350000	352000	有些优点但也有缺点
352000	355000	优点的话就是这些课都是非常经典的一些公开课
355000	358000	然后他们这些课的这些老师
358000	360000	也都是人文领域的一些大流
360000	363000	然后他们的播放量非常高
363000	365000	就比较受欢迎
365000	367000	但是这些课呢也会存在一些问题
367000	369000	就用这些公开课来进学的课会存在一些问题
369000	371000	比如说它不便于实际的查询
371000	374000	然后对于一些重点的知识点它会缺乏一些讲解
374000	377000	此外这些知识点比较分散
377000	379000	就每个老师他讲的知识点各有侧重
379000	383000	此外就是视频的扩弱化比较严重
383000	387000	因此基于这三门公开课
387000	391000	就是我和杨亦远和江静
391000	393000	三个DDR成员
393000	396000	对这些三门课的内容进行整合补充优化
396000	399000	然后写出了这本教材叫
399000	401000	Easy IR 强化域教程
405000	407000	这本教程当时是在
407000	409000	一开始是在Github上发布的
409000	411000	然后Github上呢当时发完以后
411000	412000	就是有很多好评
412000	413000	比如这边
413000	415000	比如说有人的同学就说
415000	416000	昨天非常棒
416000	417000	超越有帮助感谢博主
417000	419000	然后等等等等
419000	423000	然后目前这本书对应的那个Github仓库的
423000	426000	start数已经达48k
426000	428000	然后
428000	430000	它的增长就是它的
430000	432000	start数的一个增长曲线
432000	434000	也是曾经上升的一个态势
435000	438000	后来我们在DataWare举办了一次组队学习
438000	440000	然后组队学习的那个教材呢
440000	442000	就是以我们这本书的
442000	445000	Github的那个在线版
445000	447000	作为一个教材
449000	451000	在组队学习结束以后
451000	453000	然后也是有很多的
453000	456000	来自世界各地的学习的小伙伴
456000	457000	给出好评
457000	458000	比如这边我就
458000	460000	列出了一个小伙伴的好评
462000	464000	然后因为在Github上有很多的读者
464000	466000	会通过issue来给我们反馈
466000	469000	比如说他们会觉得我们来的地方
469000	471000	翻译来电会有些问题
471000	472000	他们会直接列出来
472000	475000	然后我们也会给他们及时的反馈
475000	478000	然后对我们的教程进行优化
479000	482000	后来我们初入以后
482000	487000	有幸得到了七位强行学习领域
487000	488000	大开的亲笔推荐
488000	490000	比如说台湾大学的宁欧英老师
490000	493000	周伯伦老师以及李克强老师
493000	497000	还有比如清华的宁生梦老师
497000	499000	汪峥老师张伟老师
499000	501000	胡玉静老师等等
502000	503000	然后这边我们列出了
503000	505000	宁欧英老师的一个推荐语
505000	507000	因为我觉得宁欧英老师的
507000	510000	推荐语非常的有趣
510000	511000	比如他说
511000	512000	他当时看到我们这个
512000	514000	伊丽艾尔这本书的时候
514000	515000	他第一个想法是
515000	517000	这成员把强化学习的知识整理得真好
517000	519000	不仅有理论说明
519000	520000	还加上了诚实实力
520000	523000	同学们以后可以直接阅读这份教程
523000	525000	这样我以后上课
525000	528000	就不用再讲强化学习的部分了
533000	534000	可以发现宁欧英对我们
534000	536000	这本书还是挺认可的
536000	537000	然后再来讲一个问题
537000	539000	这本书为什么叫蘑菇书呢
539000	541000	难道是因为作者都是吃货
541000	544000	之前有一本西瓜书
544000	545000	还有一本南瓜书
545000	547000	然后作为一个吃货的话
547000	549000	还有一个传承一下叫蘑菇书
549000	552000	就吃完西瓜南瓜再来热蘑菇
552000	554000	但其实并不是
554000	557000	蘑菇书真的寓意是玛里奥
557000	559000	大家如果玩过超级玛里奥游戏
559000	561000	就知道玛里奥吃了蘑菇以后
561000	562000	会变得更加强大
562000	565000	然后我们也希望读者
566000	569000	在吃下这个蘑菇书以后
569000	572000	能够养有兴致的探索强化学习
572000	574000	然后像玛里奥那样越强大
574000	578000	然后进而在人工智能领域里面
578000	581000	获得一些奇葩收获
583000	585000	这边我们放出了一个小彩蛋
585000	588000	就是我们的作者之一杨亦远
588000	590000	跟宁欧英老师的一个意外接触
591000	594000	当时杨亦远在参加一个顶位的时候
594000	599000	发现宁欧英老师也在参会者名单里面
599000	601000	他给宁欧英老师发了封邮件
601000	604000	介绍了我们当时GitHub的开源教程
605000	608000	然后非常惊喜的就是
608000	611000	宁欧英老师很快地给出了回信
611000	615000	然后也是对我们这个教程给出了好评
618000	621000	这边列出了蘑菇书获得的一些荣誉
621000	623000	比如说人民邮件出版社的季度好书
624000	626000	然后日读期期间
626000	629000	当当计算机期中网的第一名
629000	631000	然后上市时间以后
631000	634000	获得的就是京东人工智能榜的第一名
634000	637000	全网的推文阅读量破十万
637000	639000	然后目前清华大学李淑木教授
639000	642000	小米NLP首位科学家王斌老师
642000	645000	以及百度高级研发工程师李克强老师
645000	651000	还有一些等20家的一个大咖公众号
651000	654000	以及微博大V社区他们进行的一个转发推荐
654000	658000	然后这个也被推荐到华为电影大学的一个保定图书馆
659000	662000	蘑菇书全书一共13章可以分两部分
662000	665000	第一部分他介绍了强学的基础知识
665000	667000	以及传统的强化学习算法
667000	669000	第二部分他介绍了
669000	671000	适用强化学习算法
671000	674000	以及常见问题的解决方法
675000	678000	咱们这本书还有一些突出的特点
678000	680000	比如第一点
680000	683000	我们会利用一些简单生动的例子来解释强化学概念
683000	686000	比如说我们可以用这个玩视频游戏
686000	688000	以及下围棋的例子
688000	691000	来对强化学的一些基本概念做出解释
693000	695000	然后还有一些其他特点
695000	698000	比如说我们会对专业的一些公式
698000	700000	进行详细的推导和分析
700000	702000	这边我们以这个比尔曼方程的
702000	704000	这个推导过程的例子
704000	706000	我们会一步一步的把这个推导过程念出来
706000	707000	不会跳步走
707000	709000	然后此外呢
711000	712000	我们还会对一些
712000	714000	可能大家比较难以理解的地方
714000	716000	我们会加入一些注解
716000	717000	通过这些注解
717000	720000	我们会让大家更容易理解一些相关的概念
723000	726000	此外本书配有对应的一个观念词
726000	728000	习题和面试题
728000	730000	当读者读完一章以后
730000	733000	大家可以通过观念词来快速的掌握重点
733000	736000	然后一个通过习题和面试题
736000	738000	来巩固对知识的理解
738000	740000	然后这个习题和面试题
740000	742000	也方便大家的一个程度补缺
742000	743000	然后最后呢
743000	745000	我们会有对应的代码实战
745000	747000	大家学完这个理论以后
747000	750000	还要通过动手来把这个代码进行
750000	752000	来把这个算法进行一个实践
752000	755000	就是大家把这个算法进行实践以后
755000	757000	大家才算对这个算法
757000	759000	有一个比较深入了解
761000	763000	接下来到最后一部分
763000	765000	叫这本书怎么学比较高效
766000	768000	第一个
768000	769000	当然你可以把这本书
769000	771000	作为三门公开课的一个必要教材
771000	774000	就是当你在看这三门课的时候
774000	778000	如果你发现有哪些概念不是很清楚的情况下
778000	780000	你是可以直接翻到
780000	782000	本书对应的知识点进行学习
782000	785000	当然本书也是完全图形于三门教材的
785000	788000	大家可以直接预读本书进行学习
790000	792000	本书在GitHub上面
792000	793000	配有对应的代码
793000	795000	这个代码也会适宜的更新
795000	799000	大家如果只是想很快的
799000	801000	把这个算法给应用上
801000	802000	大家可以直接去GitHub上
802000	804000	跑对应的代码
806000	811000	此外本书还有相关的一个刊物和修订
811000	812000	这些刊物和修订
812000	814000	也会根据大家的反馈的一些意见
814000	816000	进行一些适宜的更新
818000	820000	然后这边也要讲一下
820000	823000	本书它在GitHub有个叫PK版
823000	826000	然后它还有这种纸质书的版本叫纸质版
826000	827000	它们有什么区别呢
827000	830000	就GitHub的那种PK版本是本书的一个初稿
830000	833000	然后在本书的作者已经憋进来
833000	834000	不断的修改中
835000	838000	我们最后有了纸质版
839000	841000	大家看到我们
841000	844000	对这个PK版本里面有了大量的修订
844000	846000	所以说纸质书的一个质量
846000	849000	相对于PK版本是要高不少的
851000	853000	讲到这里大家可能会有疑问
855000	858000	可能是如果我目前我涉及的工作
858000	859000	不涉及到强化学习
859000	861000	那我还有必要学强化学习吗
863000	865000	对于这个问题我想用乔布斯的观点
865000	866000	叫黏生命的点
866000	867000	或者叫应用相连
869000	871000	这边就举一下乔布斯他本人的例子
872000	875000	乔布斯当时在大学学了一门书法课
875000	878000	这门课在很多人眼里都是没有用处的
878000	880000	而乔布斯他本人也是出于兴趣
880000	881000	才学这门课
884000	888000	他也没有对这门课抱有很大的期望
888000	890000	但是后来发现他这门课
890000	893000	在设计苹果电脑字体的时候
893000	894000	取到了极大作用
895000	898000	如果乔布斯当时没有学这门课的话
898000	899000	大家可能就看不到
899000	905000	苹果电脑中这些优美丰富赏意悦目的字体
906000	910000	而对强化学习来说
910000	913000	强化学习这种应用非常广泛的技术
913000	917000	即使现在可能跟你的工作没有很大关联
917000	918000	但说不定某一天
918000	921000	可能你的工作就要涉及到相关的
921000	922000	涉及到强化学习
922000	924000	这时候如果你在之前
924000	927000	对强化学习有一个基本了解
927000	928000	或者说入门的强化学习
928000	931000	这时候可能对你的工作
932000	935000	会起到一个意想不到的助理
939000	942000	最后我们念出了蘑菇书
942000	945000	在京东以及档案的一个购买的链接
945000	946000	以及对应的二维码
947000	949000	然后大家可以在这两个
951000	953000	可以在京东和档案上
953000	955000	购买对应的直述进行学习
956000	958000	然后我们念一下slogan
958000	960000	就是我们这本书的一个标语
960000	961000	标语叫EAR
961000	964000	像柴蘑菇一样轻松入门强化学习
964000	965000	然后这个标语
965000	968000	也代表了我们作者的编写这本书的一个目的
968000	970000	就想让大家学习强化学习的时候
970000	971000	更加轻松
971000	974000	不要像当时作者学习强化学习那样
974000	975000	有很多的困难
975000	979000	然后最后祝大家都能够轻松入门强化学习
981000	983000	然后学习强化学习的过程都很顺利
