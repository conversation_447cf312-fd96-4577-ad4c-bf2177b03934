# design-and-analysis-of-algorithm
### 项目简介

书籍《算法设计与分析基础(第3版)》以一种连贯、独特的思路讲解了各类经典算法模型，具有较为新颖的论述风格，源于经典又高于经典。本项目基于该书籍，对书籍的概念以笔记的形式进行记录和解读，重点对部分课后习题进行解答，力求在引导对基础算法知识理解的同时，增强读者对算法设计过程的思考和延申。

<a href = "https://img.shields.io/badge/Document-Ver1.0.0-blue.svg"></a>

### 立项理由

- 不同于以往按照排序、查找、图等分类方式，而是从算法设计技术角度将属于同一种设计策略的方法分为一类，强调解决问题的思路而非问题归类。
- 选取每种解决思路中最经典的求解例子进行分析和解读，从而引导读者对设计思想进一步理解。
- 注重对课后习题的解答，来源于课本又高于课本，从而发散算法设计的思考。

### 项目受众

主要面向对算法设计与分析基础知识感兴趣的初学者，同时也适用于读者以一种新的角度回顾相关理论知识。

### 项目亮点

不同于高校的经典计算机算法教材，该书籍提出一种全新的算法分类方式，新分类法以减治、变治、时空权衡、迭代优化等方式对算法设计思路进行分类，是一种较为新颖的分析方式，能够较为自然的符合程序设计人员的思考逻辑，加深对算法设计过程的理解。



### 项目规划


1.目录（如有多级至少精确到二级）

├─docs-----------------------------------------------------------算法设计与分析基础（第三版）  

│  ├─ch01--------------------------------------------------------第1章 绪论     

│  ├─ch02--------------------------------------------------------第2章 算法效率分析基础 

│  ├─ch03--------------------------------------------------------第3章 蛮力法

│  │  ├─ch3-1.md------------------习题3.1选择排序与冒泡排序

│  │  ├─ch3-2.md------------------习题3.2 顺序查找和蛮力字符串匹配

│  │  ├─ch3-3.md------------------习题3.3 最近对和凸包问题的蛮力算法

│  │  ├─ch3-4.md------------------习题3.4 穷举查找

│  │  └─ch3-5.md------------------习题3.5 深度优先查找与广度优先查找

│  ├─ch05--------------------------------------------------------第5章 分治法

│  ├─ch06--------------------------------------------------------第6章 变治法

│  ├─ch08--------------------------------------------------------第8章 动态规划 

│  ├─ch09--------------------------------------------------------第9章 贪婪技术

│  └─ch10--------------------------------------------------------第10章  迭代改进 

└─README.md---------------------------------------------------项目说明文档 

2.各章节负责人
1. 绪论（钱杰-常州工学院）
2. 算法效率分析基础（金思远-香港科技大学）
3. 蛮力法（曾而康-中国科学院大学）
5. 分治法（顾洁帆-同济大学）
6. 变治法（胡泽航-东北大学）
8. 动态规划（周台春-中国科学院大学）
9. 贪婪技术（赵玉炜-中国科学院大学）
10. 迭代改进（尹晓丹-中国科学院大学）

3.预估完成日期

1. 09月10日-10月07日 完成初稿内容编写 
2. 10月07日-10月15日 文档内容提交与合并 
3. 10月16日-10月20日 优化与完善：对各个章节的内容、代码进行整理 


