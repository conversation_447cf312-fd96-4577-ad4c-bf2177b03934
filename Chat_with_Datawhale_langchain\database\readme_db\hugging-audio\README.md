# 对课程的期待

## 欢迎来到音频课程

亲爱的学习者，

欢迎来到本课程，学习如何在音频中使用Transformer。Transformer又一次地证明了自己是最强大、最通用的深度学习架构之一，能够在自然语言处理、计算机视觉以及最近的音频处理等各种任务中取得最先进的结果。

在本课程中，我们将探索如何将Transformer应用于音频数据。您将学习如何使用它们来处理一系列与音频相关的任务。无论您对语音识别、音频分类还是从文本生成语音感兴趣，Transformer和本课程都能满足您的需求。

在整个课程中，您将了解处理音频数据的具体方法，学习不同的Transformer架构，并利用强大的预训练模型训练自己的音频Transformer。

本课程专为具有深度学习背景并熟悉Transformer的学习者设计。无需具备音频数据处理方面的专业知识。

## 课程结构

课程分为几个单元，深入涵盖不同主题：

第 1 单元：学习处理音频数据的具体方法，包括音频处理技术和数据准备。

第 2 单元： 了解音频应用，学习如何使用Transformer流水线完成不同的任务，如音频分类和语音识别。

第 3 单元：探索音频Transformer架构，了解它们之间的区别，以及它们最适合用于哪些任务。

第 4 单元：学习如何构建自己的音乐流派分类器。

第 5 单元：深入研究语音识别，并建立一个转录会议录音的模型。

第 6单元：学习如何从文本生成语音。

第 7 单元：学习如何使用Transformer构建真实世界的音频应用。

每个单元都包含一个理论部分，您将深入了解基本概念和技术。在整个课程中，我们会提供测验，帮助您测试知识，巩固学习成果。有些章节还包括实践练习，让您有机会学以致用。

课程结束时，您将在使用Transformer处理音频数据方面打下坚实的基础，并能将这些技术应用到各种音频相关任务中。
